# 手机屏幕防灭屏解决方案

## 📱 问题描述

在执行自动化测试时，手机屏幕会因为超时设置而自动关闭（灭屏），导致：
- UI自动化测试无法正常进行
- 测试脚本执行中断
- 需要手动重新点亮屏幕
- 影响测试结果的准确性

## ✨ 解决方案特性

🔧 **自动屏幕管理**
- 自动设置屏幕超时时间（15秒到永不超时）
- 启用充电时保持屏幕常亮功能
- 测试结束后自动恢复原始设置

🚀 **简单易用**
- 提供测试基类，继承即可使用
- 一键设置测试环境
- 支持不同测试场景的预设配置

🛡️ **安全可靠**
- 自动保存原始设置
- 异常安全保护，确保设置恢复
- 支持手动恢复机制

📊 **状态监控**
- 实时查看屏幕设置状态
- 详细的日志记录
- 充电状态检测

## 🚀 快速开始

### 1. 最简单的使用方式（推荐）

```python
from core.base_test_with_screen_management import BaseTestWithScreenManagement

class MyTest(BaseTestWithScreenManagement):
    def test_example(self):
        # 屏幕会自动设置为30分钟超时（默认）
        # 测试结束后自动恢复原始设置
        pass
```

### 2. 自定义配置

```python
class MyTest(BaseTestWithScreenManagement):
    def setUp(self):
        # 设置45分钟超时，启用充电时常亮
        self.configure_screen_management(
            enabled=True,
            timeout_minutes=45,
            enable_stay_on=True
        )
        super().setUp()
    
    def test_long_running(self):
        # 你的测试代码
        pass
```

### 3. 直接使用管理器

```python
from utils.screen_manager import ScreenManager

screen_manager = ScreenManager()

# 一键设置测试环境
screen_manager.setup_for_testing(timeout_minutes=30)

# 执行测试...

# 恢复原始设置
screen_manager.restore_original_settings()
```

## 📋 不同测试场景

| 测试类型 | 推荐设置 | 使用方法 |
|---------|---------|----------|
| **快速测试** (5-15分钟) | 15-30分钟超时 | `setup_for_testing(timeout_minutes=30)` |
| **中等测试** (30-60分钟) | 45-60分钟超时 | `setup_for_testing(timeout_minutes=60)` |
| **长时间测试** (数小时) | 永不超时 | `set_screen_never_timeout()` |
| **过夜测试** (8小时+) | 永不超时 | `set_screen_never_timeout()` |

## 🔧 核心组件

### ScreenManager 类
- **位置**：`utils/screen_manager.py`
- **功能**：屏幕设置的核心管理类

### BaseTestWithScreenManagement 类
- **位置**：`core/base_test_with_screen_management.py`
- **功能**：带屏幕管理的测试基类

### 使用示例
- **位置**：`examples/screen_management_example.py`
- **功能**：完整的使用示例代码

## 📚 文档

- 📖 **详细使用指南**：[screen_management_guide.md](docs/screen_management_guide.md)
- 📋 **快速参考**：[screen_management_quick_reference.md](docs/screen_management_quick_reference.md)

## ⚙️ 环境要求

### 系统要求
- Android 6.0+ (API 23+)
- 已开启USB调试
- ADB连接正常

### Python依赖
- Python 3.6+
- subprocess（标准库）
- 项目现有的日志模块

### 硬件建议
- 连接充电器（推荐）
- USB数据线连接电脑

## 🎯 验证功能

运行示例代码验证功能是否正常：

```bash
# 运行基本示例
python examples/screen_management_example.py

# 运行测试基类示例
python -m unittest core.base_test_with_screen_management.ExampleTestWithScreenManagement -v
```

## 💡 实际应用示例

### Ella对话测试
```python
class EllaDialogueTest(BaseTestWithScreenManagement):
    def setUp(self):
        self.configure_screen_management(timeout_minutes=45)
        super().setUp()
    
    def test_ella_conversation(self):
        # Ella对话测试，屏幕保持45分钟不关闭
        pass
```

### 应用稳定性测试
```python
class StabilityTest(BaseTestWithScreenManagement, LongRunningTestMixin):
    def test_24_hour_stability(self):
        self.setup_for_overnight_test()  # 永不超时
        # 24小时稳定性测试
        pass
```

## ⚠️ 重要注意事项

### 使用前准备
1. **连接充电器** - 确保设备有足够电量
2. **开启USB调试** - 在开发者选项中开启
3. **授权ADB连接** - 首次连接时在设备上授权

### 最佳实践
1. **优先使用测试基类** - 自动处理所有设置
2. **根据测试时长选择合适超时** - 避免过长或过短
3. **确保异常安全** - 测试失败时也要恢复设置
4. **定期检查设备状态** - 避免过热或电量不足

### 故障排除
```bash
# 检查ADB连接
adb devices

# 检查当前屏幕超时设置
adb shell settings get system screen_off_timeout

# 检查充电状态
adb shell dumpsys battery | grep "powered"
```

## 📊 功能验证结果

根据实际测试，该解决方案已成功验证：

✅ **屏幕超时设置**：成功设置从15秒到永不超时的各种时间  
✅ **充电时常亮**：成功启用所有充电方式的屏幕常亮  
✅ **设置恢复**：测试结束后成功恢复原始设置  
✅ **状态监控**：准确获取屏幕状态信息  
✅ **异常处理**：测试异常时也能正确恢复设置  

## 🔄 版本信息

- **当前版本**：v1.0.0
- **发布日期**：2025-08-08
- **维护状态**：积极维护中
- **兼容性**：已测试主流Android设备

## 📞 技术支持

如遇到问题或需要帮助：

1. **查看详细文档**：`docs/screen_management_guide.md`
2. **运行示例代码**：`examples/screen_management_example.py`
3. **检查故障排除指南**：文档中的故障排除章节
4. **联系开发团队**：提供详细的错误信息和设备型号

---

**让测试过程中的手机屏幕永不熄灭！** 🌟
