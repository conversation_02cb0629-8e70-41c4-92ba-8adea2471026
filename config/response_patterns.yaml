# 响应模式配置文件
# 用于定义各种动态内容的正则表达式匹配模式

weather_patterns:
  # 天气查询响应模式
  standard:
    description: "标准天气格式: 地点 is 天气 today. The high is forecast at 温度°C and the low as 温度°C."
    pattern: ".+\\s+is\\s+.+\\s+today\\.\\s+The\\s+high\\s+is\\s+forecast\\s+at\\s+\\d+°C\\s+and\\s+the\\s+low\\s+as\\s+\\d+°C\\."
    examples:
      - "Pudong is Fair today. The high is forecast at 32°C and the low as 28°C."
      - "Shanghai is Cloudy today. The high is forecast at 28°C and the low as 22°C."
  
  flexible:
    description: "灵活天气格式: 包含today和两个温度值"
    pattern: ".*(today|Today).*\\d+°[CF].*\\d+°[CF].*"
    examples:
      - "Today in Shanghai: Sunny, high 32°C, low 28°C"
      - "Weather today: Rainy, 25°C to 18°C"
  
  minimal:
    description: "最小天气格式: 只要包含温度信息"
    pattern: ".*\\d+°[CF].*"
    examples:
      - "Temperature is 32°C"
      - "It's 25°F outside"
  
  comprehensive:
    description: "综合天气格式: 包含天气关键词和温度"
    pattern: ".*(weather|Weather|temperature|Temperature).*\\d+°[CF].*"
    examples:
      - "The weather is sunny with 32°C"
      - "Current temperature: 25°C"

time_patterns:
  # 时间查询响应模式
  twelve_hour:
    description: "12小时制时间格式"
    pattern: ".*\\d{1,2}:\\d{2}\\s*(AM|PM|am|pm).*"
    examples:
      - "It's 3:45 PM"
      - "The time is 10:30 AM"
  
  twenty_four_hour:
    description: "24小时制时间格式"
    pattern: ".*\\d{1,2}:\\d{2}.*"
    examples:
      - "Current time: 15:45"
      - "It's 09:30"
  
  oclock:
    description: "整点时间格式"
    pattern: ".*\\d{1,2}\\s*o'clock.*"
    examples:
      - "It's 3 o'clock"
      - "The time is 10 o'clock"
  
  with_period:
    description: "带时段的时间格式"
    pattern: ".*(morning|afternoon|evening|night).*\\d{1,2}:\\d{2}.*"
    examples:
      - "Good morning, it's 9:30"
      - "This evening at 7:45"

location_patterns:
  # 地点相关响应模式
  city_weather:
    description: "城市天气格式"
    pattern: ".*(Shanghai|Beijing|Guangzhou|Shenzhen|Pudong|Chongqing).*\\d+°[CF].*"
    examples:
      - "Shanghai weather: 32°C"
      - "In Beijing, it's 28°C"
  
  general_location:
    description: "通用地点格式"
    pattern: ".*(in|In)\\s+[A-Za-z\\s]+.*\\d+°[CF].*"
    examples:
      - "In New York, it's 75°F"
      - "Weather in London: 20°C"

app_response_patterns:
  # 应用操作响应模式
  app_opened:
    description: "应用已打开"
    pattern: ".*(opened|启动|打开|launched).*"
    examples:
      - "Camera app opened"
      - "已为您打开计算器"
  
  bluetooth_status:
    description: "蓝牙状态"
    pattern: ".*(bluetooth|蓝牙).*(on|off|开启|关闭|enabled|disabled).*"
    examples:
      - "Bluetooth is now on"
      - "蓝牙已开启"
  
  done_response:
    description: "完成响应"
    pattern: ".*(done|Done|完成|好的|已完成).*"
    examples:
      - "Done"
      - "已完成操作"

validation_rules:
  # 验证规则配置
  weather_validation:
    required_elements:
      - temperature: "必须包含温度信息"
      - time_reference: "必须包含时间参考(today/now等)"
    optional_elements:
      - location: "可选包含地点信息"
      - weather_condition: "可选包含天气状况"
    
  time_validation:
    required_elements:
      - time_format: "必须包含时间格式"
    optional_elements:
      - period: "可选包含时段信息"
      - greeting: "可选包含问候语"

# 使用说明
usage_examples:
  basic_usage: |
    # 基础用法
    pattern = config['weather_patterns']['standard']['pattern']
    result = verify_response_pattern(pattern, response_text)
  
  flexible_usage: |
    # 灵活用法 - 尝试多个模式
    patterns = [
      config['weather_patterns']['standard']['pattern'],
      config['weather_patterns']['flexible']['pattern'],
      config['weather_patterns']['minimal']['pattern']
    ]
    for pattern in patterns:
      if verify_response_pattern(pattern, response_text):
        return True
    return False
  
  custom_pattern: |
    # 自定义模式
    custom_pattern = r".*custom.*pattern.*"
    result = verify_response_pattern(custom_pattern, response_text)
