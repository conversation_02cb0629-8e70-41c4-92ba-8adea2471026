# Ella测试用例生成工具 V2 使用指南

## 新功能：自定义目录名称

V2版本的主要优化是支持自定义输出目录名称，让测试用例的组织更加灵活和有序。

## 功能特性

### 1. 优先级逻辑
- **最高优先级**: `output_path` - 完整的文件路径
- **中等优先级**: `output_dir_name` - 自定义目录名称
- **默认优先级**: 根据命令类型自动选择目录（`open_app` 或 `third_coupling`）

### 2. 支持的函数

#### 单个测试用例生成
```python
from tools.ella_test_generator_v2 import generate_with_custom_response

# 使用自定义目录名称
file_path = generate_with_custom_response(
    command="play music",
    expected_response=["Done"],
    output_dir_name="system_coupling"  # 自定义目录名称
)
```

#### 批量生成到同一目录
```python
from tools.ella_test_generator_v2 import batch_generate_with_responses

commands_and_responses = [
    ("navigate to disneyland", ["Done", "正在为您导航"]),
    ("order a burger", ["Sorry", "暂不支持订餐功能"])
]

# 所有测试用例生成到同一自定义目录
generated_files = batch_generate_with_responses(
    commands_and_responses, 
    output_dir_name="custom_tests"
)
```

#### 批量生成到不同目录
```python
from tools.ella_test_generator_v2 import batch_generate_with_custom_dirs

commands_responses_dirs = [
    ("open bluetooth", ["Done", "蓝牙已开启"], "bluetooth_tests"),
    ("open camera", ["Done", "相机已打开"], "camera_tests"),
    ("switch to performance mode", ["Done", "性能模式已启用"], "system_settings")
]

# 每个测试用例生成到指定的目录
generated_files = batch_generate_with_custom_dirs(commands_responses_dirs)
```

## 使用示例

### 示例1: 系统功能测试
```python
# 生成系统相关的测试用例到专门的目录
generate_with_custom_response(
    "play music", 
    ["Done", "音乐已开始播放"], 
    output_dir_name="system_functions"
)

generate_with_custom_response(
    "set volume to 50", 
    ["Done", "音量已设置为50"], 
    output_dir_name="system_functions"
)
```

### 示例2: 应用分类测试
```python
# 按应用类型分类生成测试用例
app_tests = [
    ("open bluetooth", ["Done", "蓝牙已开启"], "connectivity_tests"),
    ("open wifi", ["Done", "WiFi已开启"], "connectivity_tests"),
    ("open camera", ["Done", "相机已打开"], "media_tests"),
    ("open gallery", ["Done", "图库已打开"], "media_tests")
]

batch_generate_with_custom_dirs(app_tests)
```

### 示例3: 功能模块测试
```python
# 导航功能测试
navigation_tests = [
    ("navigate to beijing", ["Done", "正在为您导航到北京"]),
    ("navigate to shanghai", ["Done", "正在为您导航到上海"]),
    ("find nearest gas station", ["Done", "已找到最近的加油站"])
]

batch_generate_with_responses(navigation_tests, output_dir_name="navigation_tests")
```

## 目录结构示例

使用自定义目录名称后，测试用例的组织结构会更加清晰：

```
testcases/test_ella/
├── bluetooth_tests/
│   └── test_open_bluetooth.py
├── camera_tests/
│   └── test_open_camera.py
├── custom_tests/
│   ├── test_navigate_to_disneyland.py
│   └── test_order_a_burger.py
├── system_coupling/
│   └── test_play_music.py
├── system_settings/
│   └── test_switch_to_performance_mode.py
├── open_app/          # 默认应用打开目录
└── third_coupling/    # 默认第三方集成目录
```

## 最佳实践

1. **按功能模块分类**: 使用描述性的目录名称，如 `bluetooth_tests`, `camera_tests`
2. **按测试类型分类**: 如 `smoke_tests`, `regression_tests`, `performance_tests`
3. **按应用分类**: 如 `system_apps`, `third_party_apps`, `media_apps`
4. **保持命名一致性**: 使用统一的命名规范，如下划线分隔的小写字母

## 注意事项

- 如果同时提供 `output_path` 和 `output_dir_name`，将优先使用 `output_path`
- 目录会自动创建，无需手动创建
- 建议使用英文目录名称以避免编码问题
- 目录名称应该具有描述性，便于后续维护和查找
