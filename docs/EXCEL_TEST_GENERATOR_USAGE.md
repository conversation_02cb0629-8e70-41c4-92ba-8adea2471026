# Excel驱动的Ella测试用例批量生成工具使用指南

## 概述

本工具基于pandas模块封装，用于读取Excel文件并批量生成Ella测试脚本。工具会自动解析Excel中的命令、期望响应和输出目录信息，结合`ella_test_generator_v2.py`生成标准化的测试用例。

## 功能特性

### 🔍 智能解析
- **命令提取**: 从Excel的`query`列提取测试命令
- **响应解析**: 自动解析`expected_response`列中的文本，提取关键词（如【Sorry】）
- **目录映射**: 将中文目录名映射为英文目录名

### 📁 自动分类
- **unsupported_commands**: 响应包含"Sorry"或"Oops"的不支持命令
- **system_coupling**: 系统耦合相关测试
- **third_coupling**: 第三方耦合相关测试
- **component_coupling**: 模块耦合相关测试

### 🚀 批量生成
- 支持试运行模式（预览不生成文件）
- 支持指定范围生成
- 支持全量批量生成
- 详细的生成统计和错误处理

### ✨ 智能命名优化
- **类名优化**: 只保留命令中的字母，去除数字和特殊字符
- **方法名优化**: 只保留字母并用下划线连接
- **词汇过滤**: 自动过滤常见无意义词汇（a, the, to, and, or, of, in, on, at, by, for, with）
- **中文处理**: 正确处理中文字符和特殊符号

## 文件结构

```
tools/
├── excel_test_generator.py      # 核心Excel处理工具
├── batch_generate_demo.py       # 演示脚本（生成前10个）
├── batch_generate_all.py        # 全量生成脚本
└── case_sorry_oops.xlsx         # 源Excel文件
```

## Excel文件格式要求

Excel文件应包含以下列：

| 列名 | 说明 | 示例 |
|------|------|------|
| query | 测试命令 | `disable touch optimization` |
| output_dir_name | 输出目录（中文） | `系统耦合` |
| expected_response | 期望响应描述 | `1、响应断言：响应文本包含【Sorry】` |

### 期望响应格式支持

工具支持多种期望响应格式：

1. **标准格式**: `1、响应断言：响应文本包含【Sorry】` → 提取`Sorry`
2. **多关键词**: `响应包含【Done】或【完成】` → 提取`Done`, `完成`
3. **复杂描述**: `Sorry, weather data error,try later` → 提取`Sorry, weather data error,try later`

### 目录名映射

| 中文目录名 | 英文目录名 | 说明 |
|------------|------------|------|
| 系统耦合 | system_coupling | 系统设置相关 |
| 三方耦合 | third_coupling | 第三方服务集成 |
| 模块耦合 | component_coupling | 应用模块功能 |
| 聊天 | dialogue | 对话交互 |
| 不支持指令 | unsupported_commands | 不支持的命令 |

## 使用方法

### 1. 交互式使用

```bash
python tools/excel_test_generator.py
```

提供交互式菜单：
- 试运行（预览）
- 生成前10个测试用例
- 生成所有测试用例
- 自定义范围生成

### 2. 快速演示

```bash
python tools/batch_generate_demo.py
```

直接生成前10个测试用例，用于验证功能。

### 3. 全量生成

```bash
python tools/batch_generate_all.py
```

生成所有110个测试用例，包含确认提示和详细统计。

### 4. 编程方式使用

```python
from tools.excel_test_generator import ExcelTestGenerator

# 创建生成器
generator = ExcelTestGenerator()

# 生成指定范围的测试用例
files = generator.generate_batch_tests(start_row=0, end_row=10)

# 试运行模式
files = generator.generate_batch_tests(dry_run=True)

# 生成所有测试用例
files = generator.generate_batch_tests()
```

## 生成的测试文件示例

### 不支持命令测试

<augment_code_snippet path="testcases/test_ella/unsupported_commands/test_disable_touch_optimization.py" mode="EXCERPT">
````python
@allure.feature("Ella语音助手")
@allure.story("不支持的指令")
class TestEllaDisableTouchOptimization(SimpleEllaTest):
    """Ella disable touch optimization 测试类 - 验证不支持的指令响应"""
    command = "disable touch optimization"
    expected_text = ['Sorry']
````
</augment_code_snippet>

### 命名优化示例

| 原始命令 | 优化前类名 | 优化后类名 |
|----------|------------|------------|
| `set sim1 ringtone` | `TestEllaSetSim1Ringtone` | `TestEllaSetSimRingtone` |
| `check balance of sim2` | `TestEllaCheckBalanceOfSim2` | `TestEllaCheckBalanceSim` |
| `set date & time` | `TestEllaSetDate&Time` | `TestEllaSetDateTime` |
| `disable zonetouch master` | `TestEllaDisableZonetouchMaster` | `TestEllaDisableZonetouchMaster` |</augment_code_snippet>

### 系统耦合测试

<augment_code_snippet path="testcases/test_ella/system_coupling/test_switching_charging_speed.py" mode="EXCERPT">
````python
@allure.feature("Ella语音助手")
@allure.story("第三方集成")
class TestEllaSwitchingChargingSpeed(SimpleEllaTest):
    """Ella switching charging speed 测试类"""
    command = "switching charging speed"
    expected_text = ['Done']
````
</augment_code_snippet>

## 输出统计

工具会提供详细的生成统计：

```
📊 批量生成完成:
   ✅ 成功: 100
   ⏭️  跳过: 10
   ❌ 失败: 0
   📁 总计: 100 个文件

📁 各目录生成统计:
  - unsupported_commands: 85 个文件
  - system_coupling: 12 个文件
  - third_coupling: 3 个文件
```

## 错误处理

- **空命令**: 自动跳过空的命令行
- **无效数据**: 记录警告并继续处理其他记录
- **生成失败**: 记录错误详情，不影响其他文件生成
- **目录创建**: 自动创建不存在的输出目录

## 注意事项

1. **依赖检查**: 确保已安装pandas模块
2. **文件路径**: Excel文件默认为`tools/case_sorry_oops.xlsx`
3. **覆盖提醒**: 重复生成会覆盖已存在的测试文件
4. **编码支持**: 支持中英文混合内容
5. **内存使用**: 大量数据时注意内存占用

## 扩展功能

### 自定义Excel路径

```python
generator = ExcelTestGenerator("path/to/your/excel.xlsx")
```

### 自定义期望响应提取

可以修改`extract_expected_response`方法来支持更多格式。

### 自定义目录映射

可以修改`map_output_dir_name`方法来添加新的目录映射规则。

## 故障排除

### 常见问题

1. **pandas未安装**: `pip install pandas openpyxl`
2. **Excel文件不存在**: 检查文件路径是否正确
3. **权限问题**: 确保有写入testcases目录的权限
4. **编码问题**: 确保Excel文件使用UTF-8编码

### 调试模式

使用试运行模式来调试：

```python
generator.generate_batch_tests(dry_run=True)
```

这样可以预览将要生成的内容而不实际创建文件。
