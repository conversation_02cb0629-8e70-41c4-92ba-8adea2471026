# 进程清理优化说明

## 问题描述
Google Maps 应用已启动但未被清理掉，需要优化 `clear_all_running_processes` 方法。

## 优化策略

### 1. 调整清理优先级
- **原策略**: 优先使用温和的系统级清理，Recent页面清理作为备用
- **新策略**: 优先使用Recent页面清理，系统级清理作为补充

### 2. 配置文件优化

#### 更新 `config/process_cleanup_config.json`
```json
{
  "cleanup_settings": {
    "recent_apps_priority": true,        // 新增：Recent清理优先
    "min_apps_for_fallback": 2,         // 降低阈值
    "cleanup_timeout": 8,                // 增加超时时间
    "stabilization_wait": 2,             // 增加稳定等待时间
    "force_stop_enabled": true,          // 新增：启用强制停止
    "recent_cleanup_wait": 3             // 新增：Recent清理等待时间
  }
}
```

#### 添加Google Maps等应用包名
- `com.google.android.apps.maps` (Google Maps)
- `com.google.android.gms` (Google Play Services)
- `com.android.vending` (Google Play Store)

### 3. 方法优化

#### 3.1 `clear_all_running_processes()` 主方法
```python
# 新的清理策略顺序：
1. Recent页面清理（优先）- 能清理Google Maps等顽固应用
2. 系统级清理（补充）- 清理剩余应用
3. 强制停止（针对性）- 处理特别顽固的应用
```

#### 3.2 `_clear_via_recent_apps()` 增强版
- 支持多次重试机制
- 增加多种清理手势：
  - 点击清理按钮
  - 向上滑动手势
  - 长按清理操作
- 增加等待时间确保清理生效

#### 3.3 新增 `_force_stop_stubborn_apps()` 方法
专门处理顽固应用：
- 使用 `am force-stop` 强制停止
- 使用 `pm disable-user` + `pm enable` 重置应用状态
- 执行系统内存清理和垃圾回收
- 重点处理以下应用：
  - Google Maps
  - Google Play Services
  - Chrome浏览器
  - Facebook
  - WhatsApp
  - 微信
  - Netflix
  - Spotify

## 技术细节

### Recent页面清理增强
1. **多重手势支持**：
   - 配置文件定义的按钮点击
   - 向上滑动手势 (适用于手势导航)
   - 长按操作 (某些设备支持)

2. **重试机制**：
   - 最多重试3次
   - 每次重试间隔等待
   - 确保清理操作生效

3. **等待优化**：
   - 页面加载等待：1.5秒
   - 操作间隔等待：0.8秒
   - 清理完成等待：3秒

### 强制停止机制
1. **双重停止**：
   - `am force-stop` 强制停止进程
   - `pm disable-user` + `pm enable` 重置应用状态

2. **系统级清理**：
   - 内存清理广播
   - 垃圾回收命令

3. **超时保护**：
   - 每个操作都有超时限制
   - 避免清理过程卡死

## 使用方法

### 1. 直接调用
```python
from testcases.test_ella.base_ella_test import BaseEllaTest

base_test = BaseEllaTest()
base_test.clear_all_running_processes()
```

### 2. 测试验证
```bash
python test_process_cleanup.py
```

### 3. 在测试用例中使用
```python
class TestElla(BaseEllaTest):
    def test_command(self, ella_app):
        # clear_all_running_processes 会在 ella_app fixture 中自动调用
        pass
```

## 预期效果

1. **Google Maps 清理**：能够有效清理 Google Maps 等顽固应用
2. **清理彻底性**：通过多重策略确保应用被彻底清理
3. **系统稳定性**：保持清理过程的稳定性和可靠性
4. **测试环境**：为测试用例提供更干净的环境

## 监控和调试

### 日志输出
- 详细的清理过程日志
- 每个步骤的执行状态
- 清理结果统计

### 测试脚本
- `test_process_cleanup.py` 提供完整的测试验证
- 支持单独测试 Google Maps 清理
- 支持多应用清理测试

## 注意事项

1. **设备兼容性**：不同Android版本的Recent页面布局可能不同
2. **权限要求**：需要ADB调试权限
3. **性能影响**：清理过程可能需要几秒钟时间
4. **应用恢复**：被强制停止的应用在下次使用时会正常启动

## 后续优化建议

1. **动态检测**：根据设备型号和Android版本动态调整清理策略
2. **智能重试**：根据清理效果智能调整重试次数
3. **性能监控**：监控清理过程的性能影响
4. **配置扩展**：支持更多设备特定的清理配置
