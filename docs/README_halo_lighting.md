# 系统 Active Halo Lighting 状态检查功能

## 功能概述

参考蓝牙状态检查方法，实现了获取Android系统 Active Halo Lighting（活跃光环照明）状态的功能。该功能可以检测系统当前是否启用了 Halo Lighting 效果，支持多种检测方法以确保兼容性。

## 实现的方法

### 1. `check_active_halo_lighting_status()` - 基本状态检查

```python
def check_active_halo_lighting_status(self) -> bool:
    """
    检查系统 Active Halo Lighting 状态
    
    Returns:
        bool: Active Halo Lighting 是否已开启
    """
```

**功能特点：**
- **方法1**: 检查通知LED脉冲设置 `notification_light_pulse`
- **方法2**: 检查Infinix特有的Halo Lighting设置
  - `halo_lighting_enabled`
  - `active_halo_lighting`
  - `notification_halo_light`
  - `halo_light_enable`
- **方法3**: 检查secure设置中的halo lighting
- **方法4**: 检查系统属性中的halo lighting相关配置
- 多重检测确保准确性和兼容性

### 2. `get_active_halo_lighting_detailed_status()` - 详细状态信息

```python
def get_active_halo_lighting_detailed_status(self) -> dict:
    """
    获取 Active Halo Lighting 详细状态信息
    
    Returns:
        dict: Active Halo Lighting 详细状态信息
    """
```

**返回信息包括：**
- `halo_lighting_enabled`: 是否启用Halo Lighting
- `notification_led_enabled`: 通知LED是否启用
- `detection_method`: 检测方法
- `system_settings`: 系统设置详情
- `secure_settings`: 安全设置详情
- `system_properties`: 系统属性详情
- `supported_features`: 支持的功能列表

## 使用示例

### 基本使用

```python
from pages.base.system_status_checker import SystemStatusChecker

checker = SystemStatusChecker()

# 检查 Active Halo Lighting 是否开启
is_halo_enabled = checker.check_active_halo_lighting_status()
if is_halo_enabled:
    print("Active Halo Lighting 已开启")
else:
    print("Active Halo Lighting 已关闭")
```

### 获取详细信息

```python
# 获取详细 Halo Lighting 信息
halo_info = checker.get_active_halo_lighting_detailed_status()
print(f"检测方法: {halo_info['detection_method']}")
print(f"通知LED: {halo_info['notification_led_enabled']}")
print(f"系统设置: {halo_info['system_settings']}")
```

### 在测试用例中使用

```python
def test_notification_with_halo():
    checker = SystemStatusChecker()
    is_halo_enabled = checker.check_active_halo_lighting_status()
    
    if is_halo_enabled:
        # 验证 Halo Lighting 效果
        assert halo_ring_element.is_visible()
        assert led_pulse_effect.is_active()
    else:
        # 验证标准通知效果
        assert standard_notification.is_visible()
```

## 技术实现细节

### 检测方法优先级
1. **通知LED脉冲** - `notification_light_pulse` (最通用)
2. **Infinix特有设置** - 品牌特定的halo lighting设置
3. **安全设置** - secure命名空间中的相关设置
4. **系统属性** - 底层系统属性配置

### ADB命令支持
- `adb shell settings get system notification_light_pulse` - 通知LED脉冲
- `adb shell settings get system halo_lighting_enabled` - Halo Lighting设置
- `adb shell settings get secure active_halo_lighting_enabled` - 安全设置
- `adb shell getprop persist.vendor.halo.lighting` - 系统属性

### 错误处理
- 网络超时处理（3-5秒超时）
- ADB命令执行失败的降级处理
- 多方法检测确保可靠性
- 详细的日志记录

### 兼容性
- 支持Infinix Note 40系列的Active Halo AI Lighting
- 兼容标准Android通知LED功能
- 支持不同厂商的自定义实现
- 向后兼容传统通知灯功能

## 测试验证

项目包含完整的测试文件：
- `test_halo_lighting.py` - 功能测试脚本
- `example_halo_lighting_usage.py` - 使用示例和演示

运行测试：
```bash
python test_halo_lighting.py
python example_halo_lighting_usage.py
```

## 测试结果示例

```
Active Halo Lighting 是否开启: True
状态: 开启
Halo Lighting 开启: True
通知LED开启: True
检测方法: SYSTEM_SETTINGS_NOTIFICATION_LIGHT_PULSE
系统设置:
  notification_light_pulse: 1
```

## 集成说明

该功能已集成到 `pages/base/system_status_checker.py` 中，与现有的蓝牙、WiFi、位置服务、主题状态等检查功能保持一致的API设计和代码风格。

可以与其他系统状态检查功能配合使用，为自动化测试提供完整的系统环境感知能力，特别适用于需要验证通知效果和LED灯光功能的测试场景。

## 特别说明

Active Halo Lighting 是较新的功能，主要在Infinix等品牌的新款手机中支持。对于不支持该功能的设备，方法会通过标准的通知LED检测来提供基础的灯光状态信息。
