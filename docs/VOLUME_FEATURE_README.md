# 系统音量获取功能

## 概述

参考 `get_screen_brightness` 方法的实现方式，新增了系统音量获取功能，包括两个主要方法：

1. `get_system_volume(volume_type)` - 获取指定类型的音量值
2. `get_system_volume_detailed_status()` - 获取所有音量类型的详细状态信息

## 功能特性

### 支持的音量类型

- **music**: 媒体音量 (默认)
- **ring**: 铃声音量
- **alarm**: 闹钟音量
- **notification**: 通知音量
- **system**: 系统音量
- **voice_call**: 通话音量

### 多种检测方法

类似于屏幕亮度检测，音量获取使用多种方法确保兼容性：

1. **ADB Settings命令**: 通过 `settings get system volume_*` 获取
2. **AudioManager Dumpsys**: 通过 `dumpsys audio` 解析音频流信息
3. **Media Session**: 通过 `dumpsys media_session` 获取媒体音量
4. **系统属性**: 通过 `getprop` 获取音频相关属性
5. **ALSA设备**: 通过音频设备文件获取硬件音量信息

## 使用方法

### 基本用法

```python
from pages.base.system_status_checker import SystemStatusChecker

# 创建检查器实例
checker = SystemStatusChecker()

# 获取媒体音量 (默认)
media_volume = checker.get_system_volume()
print(f"媒体音量: {media_volume}")

# 获取铃声音量
ring_volume = checker.get_system_volume("ring")
print(f"铃声音量: {ring_volume}")

# 获取闹钟音量
alarm_volume = checker.get_system_volume("alarm")
print(f"闹钟音量: {alarm_volume}")
```

### 详细状态获取

```python
# 获取所有音量的详细状态
detailed_status = checker.get_system_volume_detailed_status()

print(f"检测方法: {detailed_status['detection_method']}")
print(f"音频模式: {detailed_status['audio_mode']}")
print(f"媒体音量: {detailed_status['music_volume']}")
print(f"铃声音量: {detailed_status['ring_volume']}")
print(f"静音状态: {detailed_status['mute_status']}")
print(f"音量百分比: {detailed_status['volume_percentages']}")
```

### 返回值说明

- **get_system_volume()**: 返回整数音量值 (通常0-15或0-100)，-1表示获取失败
- **get_system_volume_detailed_status()**: 返回包含以下信息的字典：
  - `music_volume`, `ring_volume`, `alarm_volume` 等: 各类型音量值
  - `max_volumes`: 各类型的最大音量值
  - `volume_percentages`: 音量百分比
  - `mute_status`: 静音状态
  - `detection_method`: 检测方法
  - `audio_mode`: 当前音频模式
  - `current_audio_focus`: 当前音频焦点

## 测试和示例

### 运行测试

```bash
python test_volume_functionality.py
```

### 查看使用示例

```bash
python volume_usage_example.py
```

## 实现细节

### 方法1: ADB Settings
```bash
adb shell settings get system volume_music
adb shell settings get system volume_ring
adb shell settings get system volume_alarm
```

### 方法2: AudioManager Dumpsys
```bash
adb shell dumpsys audio
```
解析输出中的 `STREAM_MUSIC`, `STREAM_RING` 等音频流信息。

### 方法3: Media Session
```bash
adb shell dumpsys media_session
```
获取当前媒体播放的音量信息。

### 方法4: 系统属性
```bash
adb shell getprop persist.vendor.audio.music.volume
adb shell getprop ro.config.media_vol_default
```

### 方法5: ALSA设备
```bash
adb shell cat /proc/asound/card0/codec#0
adb shell cat /sys/class/sound/controlC0/volume
```

## 错误处理

- 所有方法都包含异常处理
- 如果一种方法失败，会自动尝试下一种方法
- 返回 -1 表示所有方法都失败
- 详细的日志记录帮助调试问题

## 兼容性

- 支持不同Android版本的音量系统
- 兼容不同厂商的音频实现
- 自动适配不同的音量范围 (0-15, 0-100等)
- 支持双卡手机的音频配置

## 注意事项

1. 需要ADB连接和相应权限
2. 某些系统可能限制音频信息访问
3. 音量值范围可能因设备而异
4. 静音状态检测可能在某些设备上不准确
