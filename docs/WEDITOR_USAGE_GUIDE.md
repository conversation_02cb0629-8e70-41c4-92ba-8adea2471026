# WEditor 使用指南

## 简介
WEditor 是一个基于 Web 的 Android UI 自动化测试工具，可以帮助您：
- 可视化地查看和分析 Android 应用界面
- 生成 UI 自动化测试代码
- 实时预览和调试 UI 元素

## 安装状态
✅ weditor 已成功安装 (版本 0.6.0)
✅ 所有依赖项已安装完成

## 启动 WEditor

### 方法1：命令行启动
```bash
# 在 PowerShell 中运行
python -m weditor
```

### 方法2：指定端口启动
```bash
python -m weditor --port 17310
```

## 使用步骤

### 1. 启动 WEditor 服务
```bash
python -m weditor
```
启动后会显示类似信息：
```
listening on http://*************:17310
```

### 2. 打开浏览器
在浏览器中访问显示的地址，例如：
```
http://localhost:17310
```

### 3. 连接设备
- 确保 Android 设备已连接并开启 USB 调试
- 在 WEditor 界面中选择设备
- 点击连接按钮

### 4. 查看和分析界面
- 实时查看设备屏幕
- 点击界面元素查看属性
- 生成定位代码

## 与现有项目集成

### 在测试用例中使用
WEditor 可以帮助您：
1. 快速定位 UI 元素
2. 生成元素定位代码
3. 验证元素属性

### 示例代码生成
WEditor 可以生成类似以下的代码：
```python
# 点击元素
d(text="确定").click()

# 输入文本
d(resourceId="com.example:id/edit_text").set_text("Hello")

# 等待元素出现
d(text="加载完成").wait(timeout=10)
```

## 常见问题解决

### 1. 设备连接问题
如果设备无法连接，请检查：
- USB 调试是否开启
- adb 是否正常工作：`adb devices`
- 设备是否授权

### 2. 端口占用问题
如果默认端口被占用，可以指定其他端口：
```bash
python -m weditor --port 18000
```

### 3. 权限问题
如果遇到权限问题，确保：
- 设备已授权 USB 调试
- 应用有必要的权限

## 与项目的 uiautomator2 集成

您的项目已经使用 uiautomator2，WEditor 与其完全兼容：

```python
import uiautomator2 as u2

# 连接设备
d = u2.connect()

# 使用 WEditor 生成的代码
d(text="Ella").click()
d(resourceId="com.transsion.ella:id/input_text").set_text("Hello")
```

## 最佳实践

1. **元素定位优先级**：
   - resourceId > text > description > xpath

2. **稳定性考虑**：
   - 使用 wait() 方法等待元素出现
   - 添加适当的延时

3. **代码复用**：
   - 将常用的元素定位封装成方法
   - 使用页面对象模式

## 下一步
1. 启动 WEditor：`python -m weditor`
2. 在浏览器中打开显示的地址
3. 连接您的测试设备
4. 开始分析和生成测试代码

## 注意事项
- WEditor 主要用于开发和调试阶段
- 生成的代码需要根据实际项目结构进行调整
- 建议结合现有的页面对象模式使用
