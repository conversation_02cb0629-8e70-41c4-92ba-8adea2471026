# Ella测试用例生成器 - 断言类型优化

## 概述

已成功优化Ella测试用例生成模块，支持根据断言类型自动生成相应的验证步骤。现在可以根据不同的测试需求，灵活配置验证内容。

## 支持的断言类型

### 1. text - 文本响应验证
验证Ella的响应是否包含期望的文本内容。

**生成的验证步骤：**
```python
with allure.step("验证响应包含期望内容"):
    result = self.verify_expected_in_response(expected_text, response_text)
    assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"
```

### 2. process - 应用状态验证
验证应用是否按预期打开或关闭。

**生成的验证步骤：**
- 对于应用打开类命令：
```python
with allure.step(f"验证{app_name}已打开"):
    assert final_status, f"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"
```

- 对于其他命令：
```python
with allure.step(f"验证应用已打开"):
    assert not final_status, f"初始={initial_status}, 最终={final_status}, 响应='{response_text}'"
```

### 3. files - 文件存在验证
验证命令执行后是否生成了预期的文件。

**生成的验证步骤：**
```python
with allure.step(f"验证文件存在"):
    assert files_status, f"文件不存在！"
```

## 使用方法

### 基本用法

```python
from tools.ella_test_generator_v2 import generate_with_custom_response

# 生成包含所有三种断言类型的测试用例
file_path = generate_with_custom_response(
    command="stop screen recording",
    expected_response=["Screen recording finished"],
    output_dir_name="component_coupling",
    assertion_types=['text', 'process', 'files']
)
```

### 不同断言类型组合示例

#### 1. 只验证文本响应
```python
# 适用于聊天对话类命令
assertion_types = ['text']
```

#### 2. 验证文本 + 应用状态
```python
# 适用于打开应用类命令
assertion_types = ['text', 'process']
```

#### 3. 验证文本 + 文件生成
```python
# 适用于文件操作类命令
assertion_types = ['text', 'files']
```

#### 4. 全面验证
```python
# 适用于复杂操作命令
assertion_types = ['text', 'process', 'files']
```

## 生成的测试用例特点

### 1. 自动参数配置
根据断言类型自动设置`simple_command_test`的参数：
- `verify_status`: 当包含'process'断言时为True
- `verify_files`: 当包含'files'断言时为True

### 2. 智能验证步骤
根据断言类型组合，自动生成相应的验证步骤，确保测试用例的完整性和准确性。

### 3. 灵活配置
支持任意断言类型组合，满足不同测试场景的需求。

## 完整示例

以下是一个包含所有三种断言类型的完整测试用例：

```python
"""
Ella语音助手第三方集成指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("第三方集成")
class TestEllaStopScreenRecording(SimpleEllaTest):
    """Ella stop screen recording 测试类"""
    command = "stop screen recording"
    expected_text = ['Screen recording finished']

    @allure.title(f"测试{command}能正常执行")
    @allure.description(f"{command}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_stop_screen_recording(self, ella_app):
        f"""{self.command}"""

        command = self.command
        expected_text = self.expected_text

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command, verify_status=True, verify_files=True
            )

        with allure.step("验证响应包含期望内容"):
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step(f"验证应用已打开"):
            assert not final_status, f"初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

        with allure.step(f"验证文件存在"):
            assert files_status, f"文件不存在！"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
```

## 优化效果

1. **提高测试覆盖率**: 根据命令特性选择合适的验证方式
2. **减少冗余代码**: 避免不必要的验证步骤
3. **增强可维护性**: 统一的验证步骤生成逻辑
4. **提升灵活性**: 支持任意断言类型组合

## 向后兼容性

- 如果不指定`assertion_types`参数，系统会使用默认的断言类型
- 现有的生成函数仍然可以正常工作
- 新增的参数都是可选的，不会影响现有代码

## 运行示例

可以运行以下命令查看不同断言类型的效果：

```bash
python tools/assertion_types_example.py
```

这将生成多个示例测试用例，展示不同断言类型组合的使用效果。
