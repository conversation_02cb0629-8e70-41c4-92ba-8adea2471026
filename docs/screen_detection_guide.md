# 屏幕状态检测功能指南

## 📱 功能概述

基于您选中的代码风格，我在 `pages/base/system_status_checker.py` 中实现了两个新的屏幕检测功能：

✅ **`check_screen_status()`** - 详细屏幕状态检测  
✅ **`is_screen_off()`** - 简单灭屏状态检测

## 🔧 核心功能

### 1. 详细屏幕状态检测 (`check_screen_status()`)

返回完整的屏幕状态信息字典：

```python
from pages.base.system_status_checker import SystemStatusChecker

checker = SystemStatusChecker()
status = checker.check_screen_status()

# 返回的状态信息包括：
{
    'screen_on': True/False/None,           # 屏幕是否开启
    'screen_locked': True/False/None,       # 屏幕是否锁定
    'interactive': True/False/None,         # 是否可交互
    'display_state': 'ON'/'OFF'/None,       # 显示状态
    'screen_timeout_ms': 600000,            # 超时时间(毫秒)
    'screen_timeout_description': '10.0分钟', # 超时描述
    'stay_on_while_plugged': 0,             # 充电时常亮设置
    'stay_on_description': '关闭',           # 充电常亮描述
    'brightness_level': 52,                 # 亮度级别
    'brightness_mode': '自动',               # 亮度模式
    'power_state': '屏幕唤醒',               # 电源状态
    'wake_lock_count': 0,                   # 唤醒锁数量
    'charging_status': '充电中(USB)',        # 充电状态
    'battery_level': 100                    # 电池电量
}
```

### 2. 简单灭屏检测 (`is_screen_off()`)

快速检查屏幕是否已灭屏：

```python
is_off = checker.is_screen_off()

# 返回值：
# True  - 屏幕已灭屏
# False - 屏幕未灭屏  
# None  - 无法确定状态
```

## 📊 实际测试结果

从测试结果可以看到，功能运行正常：

```
=== 屏幕检测功能测试 ===

1. 基本屏幕状态检测:
   📱 屏幕状态: 未灭屏

2. 详细屏幕状态:
   🔆 屏幕开启: 是
   🔒 屏幕锁定: 未知
   👆 可交互: 未知
   ⏰ 超时设置: 10.0分钟
   🔌 充电常亮: 关闭
   🔋 充电状态: 充电中(USB)
   📊 电池电量: 100%
   💡 亮度级别: 52
   ⚡ 电源状态: 屏幕唤醒
```

## 🚀 使用方法

### 基本用法

```python
from pages.base.system_status_checker import SystemStatusChecker

# 创建检测器
checker = SystemStatusChecker()

# 快速检查是否灭屏
if checker.is_screen_off():
    print("屏幕已灭屏，需要唤醒")
else:
    print("屏幕正常，可以进行操作")

# 获取详细状态
status = checker.check_screen_status()
print(f"屏幕超时: {status['screen_timeout_description']}")
print(f"电池电量: {status['battery_level']}%")
```

### 在测试中使用

```python
def test_ui_operation(self):
    """UI操作测试"""
    checker = SystemStatusChecker()
    
    # 检查屏幕状态
    if checker.is_screen_off():
        # 屏幕已灭屏，先唤醒
        self.wake_up_screen()
    
    # 获取详细状态进行判断
    status = checker.check_screen_status()
    
    if status['screen_on'] and not status['screen_locked']:
        # 屏幕开启且未锁定，可以进行UI操作
        self.perform_ui_operations()
    else:
        # 需要解锁或其他处理
        self.handle_screen_issues()
```

### 状态监控

```python
def monitor_screen_status():
    """监控屏幕状态变化"""
    checker = SystemStatusChecker()
    
    previous_state = None
    
    while True:
        is_off = checker.is_screen_off()
        current_state = "灭屏" if is_off else "亮屏"
        
        if previous_state and current_state != previous_state:
            print(f"屏幕状态变化: {previous_state} → {current_state}")
        
        previous_state = current_state
        time.sleep(5)
```

### 条件判断

```python
def check_screen_readiness():
    """检查屏幕是否适合UI操作"""
    checker = SystemStatusChecker()
    status = checker.check_screen_status()
    
    if status['screen_on'] and status['interactive'] and not status['screen_locked']:
        return True, "屏幕状态良好，可以进行UI操作"
    elif status['screen_on'] and status['screen_locked']:
        return False, "屏幕已锁定，需要解锁"
    elif not status['screen_on']:
        return False, "屏幕已关闭，需要唤醒"
    else:
        return False, "屏幕状态不明确"
```

## 🔍 检测原理

### 屏幕开关状态检测

通过 `adb shell dumpsys power` 命令获取电源管理信息，解析以下关键字段：

- `mScreenOn=true/false` - 屏幕开关状态
- `Display Power: state=ON/OFF` - 显示电源状态  
- `mWakefulness=Awake/Asleep` - 唤醒状态
- `mInteractive=true/false` - 交互状态

### 屏幕锁定状态检测

通过 `adb shell dumpsys window` 命令获取窗口管理信息，解析：

- `mShowingLockscreen=true/false` - 锁屏显示状态
- `KeyguardController` 相关信息 - 键盘锁状态

### 屏幕设置检测

通过 `adb shell settings get` 命令获取系统设置：

- `system screen_off_timeout` - 屏幕超时时间
- `global stay_on_while_plugged_in` - 充电时常亮设置
- `system screen_brightness` - 屏幕亮度
- `system screen_brightness_mode` - 亮度模式

### 电池和充电状态

通过 `adb shell dumpsys battery` 命令获取电池信息：

- `AC powered/USB powered/Wireless powered` - 充电方式
- `level` - 电池电量百分比

## ⚠️ 注意事项

### 兼容性
- 支持 Android 6.0+ 系统
- 不同厂商的定制系统可能有差异
- 某些字段在特定设备上可能无法获取

### 性能考虑
- `is_screen_off()` 使用了优化的快速检测方法
- `check_screen_status()` 会执行多个ADB命令，耗时较长
- 建议根据需要选择合适的检测方法

### 错误处理
- 所有方法都包含完善的异常处理
- ADB连接失败时会返回默认值或None
- 超时设置为5-10秒，避免长时间阻塞

## 🎯 应用场景

### 1. UI自动化测试前检查
```python
if checker.is_screen_off():
    # 唤醒屏幕后再进行UI操作
    wake_up_screen()
```

### 2. 长时间测试监控
```python
# 定期检查屏幕状态，确保测试正常进行
status = checker.check_screen_status()
if status['screen_timeout_ms'] < 300000:  # 小于5分钟
    # 延长超时时间
    extend_screen_timeout()
```

### 3. 测试环境验证
```python
# 验证测试环境是否配置正确
status = checker.check_screen_status()
if status['stay_on_while_plugged'] == 0:
    print("建议启用充电时保持常亮功能")
```

### 4. 故障诊断
```python
# 当UI操作失败时，检查是否因为屏幕问题
if ui_operation_failed():
    status = checker.check_screen_status()
    if not status['screen_on']:
        print("UI操作失败可能是因为屏幕已关闭")
```

## 📁 相关文件

- **核心实现**: `pages/base/system_status_checker.py`
- **使用示例**: `examples/screen_detection_example.py`
- **简单测试**: `test_screen_detection.py`

## 🔗 与屏幕管理器集成

可以与之前实现的屏幕管理器配合使用：

```python
from pages.base.system_status_checker import SystemStatusChecker
from utils.screen_manager import ScreenManager

checker = SystemStatusChecker()
manager = ScreenManager()

# 检查当前状态
if checker.is_screen_off():
    print("屏幕已灭屏")
    
# 设置屏幕常亮
manager.set_screen_never_timeout()

# 验证设置是否生效
status = checker.check_screen_status()
print(f"设置后超时: {status['screen_timeout_description']}")
```

---

**现在您可以精确地检测和监控设备的屏幕状态，为自动化测试提供更可靠的基础！** 🌟
