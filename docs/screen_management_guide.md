# 屏幕管理工具使用指南

## 概述

在执行自动化测试时，手机屏幕可能会因为超时而自动关闭，这会影响测试的正常执行。本工具提供了完整的屏幕管理解决方案，可以：

- 设置屏幕超时时间
- 启用充电时保持屏幕常亮
- 在测试结束后自动恢复原始设置
- 提供测试基类自动处理屏幕设置

## 核心组件

### 1. ScreenManager 类

位置：`utils/screen_manager.py`

主要功能：
- 获取和设置屏幕超时时间
- 控制充电时保持屏幕常亮设置
- 获取屏幕状态信息
- 自动保存和恢复原始设置

### 2. BaseTestWithScreenManagement 类

位置：`core/base_test_with_screen_management.py`

主要功能：
- 测试开始时自动设置屏幕参数
- 测试结束时自动恢复原始设置
- 提供便捷的屏幕管理方法
- 支持不同类型测试的混入类

## 快速开始

### 方法一：直接使用 ScreenManager

```python
from utils.screen_manager import ScreenManager

# 创建屏幕管理器
screen_manager = ScreenManager()

# 设置测试环境(30分钟超时 + 充电时常亮)
screen_manager.setup_for_testing(timeout_minutes=30)

# 执行你的测试代码
# ...

# 恢复原始设置
screen_manager.restore_original_settings()
```

### 方法二：使用测试基类(推荐)

```python
from core.base_test_with_screen_management import BaseTestWithScreenManagement

class MyTest(BaseTestWithScreenManagement):
    def setUp(self):
        # 配置屏幕管理参数
        self.configure_screen_management(
            enabled=True,
            timeout_minutes=45,  # 45分钟超时
            enable_stay_on=True
        )
        super().setUp()  # 自动设置屏幕参数
    
    def test_my_case(self):
        # 你的测试代码
        # 屏幕会保持45分钟不关闭
        pass
    
    # tearDown会自动恢复原始设置
```

## 详细使用方法

### 1. 基本屏幕超时设置

```python
from utils.screen_manager import ScreenManager

screen_manager = ScreenManager()

# 设置不同的超时时间
screen_manager.set_screen_timeout_minutes(5)    # 5分钟
screen_manager.set_screen_timeout_minutes(30)   # 30分钟
screen_manager.set_screen_never_timeout()       # 永不超时

# 直接设置毫秒值
screen_manager.set_screen_timeout(300000)       # 5分钟(300000毫秒)
```

### 2. 充电时保持常亮设置

```python
# 启用所有充电方式时保持常亮
screen_manager.enable_stay_on_while_plugged()

# 禁用充电时保持常亮
screen_manager.disable_stay_on_while_plugged()

# 自定义设置
screen_manager.set_stay_on_while_plugged(3)  # AC和USB充电时保持常亮
```

充电时保持常亮设置值说明：
- 0: 关闭
- 1: AC充电时保持常亮
- 2: USB充电时保持常亮
- 3: AC和USB充电时都保持常亮
- 4: 无线充电时保持常亮
- 7: 所有充电方式都保持常亮

### 3. 获取屏幕状态信息

```python
status = screen_manager.get_screen_status_info()

print(f"屏幕超时时间: {status['screen_timeout_description']}")
print(f"充电时保持常亮: {status['stay_on_description']}")
print(f"屏幕当前状态: {status['power_state']}")
```

### 4. 一键测试环境设置

```python
# 一键设置测试环境(推荐用法)
success = screen_manager.setup_for_testing(timeout_minutes=30)

# 执行测试...

# 一键恢复原始设置
screen_manager.restore_original_settings()
```

## 测试基类使用

### 1. 基本用法

```python
from core.base_test_with_screen_management import BaseTestWithScreenManagement

class MyTest(BaseTestWithScreenManagement):
    def test_example(self):
        # 屏幕会自动设置为30分钟超时(默认)
        # 测试结束后自动恢复原始设置
        pass
```

### 2. 自定义配置

```python
class MyTest(BaseTestWithScreenManagement):
    def setUp(self):
        # 自定义屏幕管理配置
        self.configure_screen_management(
            enabled=True,           # 启用屏幕管理
            timeout_minutes=60,     # 60分钟超时
            enable_stay_on=True     # 启用充电时常亮
        )
        super().setUp()
    
    def test_long_running(self):
        # 屏幕会保持60分钟不关闭
        pass
```

### 3. 长时间测试

```python
from core.base_test_with_screen_management import (
    BaseTestWithScreenManagement, 
    LongRunningTestMixin
)

class MyLongTest(BaseTestWithScreenManagement, LongRunningTestMixin):
    def test_overnight(self):
        # 为过夜测试设置永不超时
        self.setup_for_overnight_test()
        
        # 执行长时间测试...
```

### 4. 禁用屏幕管理

```python
class MyTest(BaseTestWithScreenManagement):
    def setUp(self):
        # 禁用屏幕管理
        self.configure_screen_management(enabled=False)
        super().setUp()
    
    def test_without_screen_management(self):
        # 不会修改屏幕设置
        pass
```

## 常见使用场景

### 1. 短时间测试(5-15分钟)

```python
screen_manager.setup_for_testing(timeout_minutes=15)
```

### 2. 中等时间测试(30-60分钟)

```python
screen_manager.setup_for_testing(timeout_minutes=60)
```

### 3. 长时间测试(数小时)

```python
screen_manager.set_screen_never_timeout()
screen_manager.enable_stay_on_while_plugged()
```

### 4. 过夜测试

```python
screen_manager.set_screen_never_timeout()
screen_manager.enable_stay_on_while_plugged()
```

## 最佳实践

### 1. 使用测试基类(推荐)

使用 `BaseTestWithScreenManagement` 可以自动处理屏幕设置和恢复，避免手动管理的复杂性。

### 2. 根据测试时长设置合适的超时时间

- 短测试: 15-30分钟
- 中等测试: 60分钟
- 长测试: 永不超时

### 3. 确保连接充电器

启用"充电时保持常亮"功能时，确保设备连接到充电器，这样屏幕会一直保持亮起。

### 4. 测试结束后恢复设置

始终在测试结束后恢复原始设置，避免影响设备的正常使用。

### 5. 异常处理

```python
try:
    screen_manager.setup_for_testing(timeout_minutes=30)
    # 执行测试...
except Exception as e:
    print(f"测试异常: {e}")
finally:
    # 确保恢复原始设置
    screen_manager.restore_original_settings()
```

## 故障排除

### 1. 设置不生效

- 确保ADB连接正常
- 检查设备是否有权限限制
- 尝试手动在设备上修改设置验证

### 2. 屏幕仍然自动关闭

- 检查充电器是否连接
- 确认"充电时保持常亮"设置已启用
- 检查设备是否有省电模式等限制

### 3. 无法恢复原始设置

- 手动在设备设置中恢复
- 重启设备恢复默认设置

## 示例代码

完整的示例代码请参考：
- `examples/screen_management_example.py` - 基本使用示例
- `core/base_test_with_screen_management.py` - 测试基类示例

## 注意事项

1. 本工具需要ADB连接和相应权限
2. 不同Android版本和设备厂商可能有差异
3. 建议在测试前先验证功能是否正常
4. 长时间保持屏幕常亮可能影响设备电池寿命
5. 测试结束后务必恢复原始设置
