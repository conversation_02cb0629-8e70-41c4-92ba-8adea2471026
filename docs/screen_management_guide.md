# 手机屏幕防灭屏解决方案

## 问题背景

在执行自动化测试过程中，手机屏幕会因为超时设置而自动关闭（灭屏），这会导致：
- 测试无法正常进行
- UI自动化操作失败
- 测试结果不准确
- 需要手动干预重新点亮屏幕

## 解决方案概述

本方案提供了完整的屏幕管理工具，通过ADB命令自动控制手机屏幕设置，确保测试过程中屏幕保持常亮：

✅ **自动设置屏幕超时时间**（15秒到永不超时）
✅ **启用充电时保持屏幕常亮**（连接充电器时屏幕不关闭）
✅ **测试结束后自动恢复原始设置**（不影响日常使用）
✅ **提供测试基类自动处理**（无需手动管理）
✅ **支持不同测试场景**（短测试、长测试、过夜测试）

## 核心组件

### 1. ScreenManager 类 🔧

**位置**：`utils/screen_manager.py`

**核心功能**：
- 🕐 获取和设置屏幕超时时间
- 🔌 控制充电时保持屏幕常亮设置
- 📊 获取屏幕状态详细信息
- 💾 自动保存和恢复原始设置
- ⚡ 一键设置测试环境

### 2. BaseTestWithScreenManagement 类 🚀

**位置**：`core/base_test_with_screen_management.py`

**核心功能**：
- 🔄 测试开始时自动设置屏幕参数
- 🔙 测试结束时自动恢复原始设置
- ⚙️ 提供便捷的屏幕管理方法
- 🎯 支持不同类型测试的混入类
- 🛡️ 异常安全保护（确保设置恢复）

## 🚀 快速开始

### 方法一：直接使用 ScreenManager（适合简单场景）

```python
from utils.screen_manager import ScreenManager

# 创建屏幕管理器
screen_manager = ScreenManager()

# 一键设置测试环境（30分钟超时 + 充电时常亮）
screen_manager.setup_for_testing(timeout_minutes=30)

# 执行你的测试代码
# 现在屏幕会保持30分钟不关闭，如果连接充电器则一直不关闭
# ...

# 测试结束后恢复原始设置
screen_manager.restore_original_settings()
```

### 方法二：使用测试基类（推荐⭐）

```python
from examples.base_test_with_screen_management import BaseTestWithScreenManagement


class MyTest(BaseTestWithScreenManagement):
    def setUp(self):
        # 配置屏幕管理参数
        self.configure_screen_management(
            enabled=True,  # 启用屏幕管理
            timeout_minutes=45,  # 45分钟超时
            enable_stay_on=True  # 启用充电时常亮
        )
        super().setUp()  # 自动设置屏幕参数

    def test_my_case(self):
        # 你的测试代码
        # 屏幕会保持45分钟不关闭，连接充电器时永不关闭
        pass

    # tearDown会自动恢复原始设置，无需手动处理
```

### 方法三：一键永不超时（适合长时间测试）

```python
from utils.screen_manager import ScreenManager

screen_manager = ScreenManager()

# 设置屏幕永不超时
screen_manager.set_screen_never_timeout()

# 启用充电时保持常亮
screen_manager.enable_stay_on_while_plugged()

# 执行长时间测试...

# 恢复原始设置
screen_manager.restore_original_settings()
```

## 📋 不同测试场景的推荐设置

| 测试类型 | 测试时长 | 推荐超时时间 | 使用方法 | 说明 |
|---------|---------|-------------|----------|------|
| **快速测试** | 5-15分钟 | 15-30分钟 | `setup_for_testing(timeout_minutes=30)` | 适合单元测试、功能测试 |
| **中等测试** | 30-60分钟 | 60分钟 | `setup_for_testing(timeout_minutes=60)` | 适合集成测试、回归测试 |
| **长时间测试** | 数小时 | 永不超时 | `set_screen_never_timeout()` | 适合压力测试、稳定性测试 |
| **过夜测试** | 8小时以上 | 永不超时 | `set_screen_never_timeout()` | 适合长期稳定性测试 |

## 🔧 详细使用方法

### 1. 基本屏幕超时设置

```python
from utils.screen_manager import ScreenManager

screen_manager = ScreenManager()

# 设置不同的超时时间
screen_manager.set_screen_timeout_minutes(5)    # 5分钟
screen_manager.set_screen_timeout_minutes(30)   # 30分钟
screen_manager.set_screen_timeout_minutes(60)   # 60分钟
screen_manager.set_screen_never_timeout()       # 永不超时

# 直接设置毫秒值（高级用法）
screen_manager.set_screen_timeout(300000)       # 5分钟(300000毫秒)
screen_manager.set_screen_timeout(1800000)      # 30分钟(1800000毫秒)
screen_manager.set_screen_timeout(2147483647)   # 永不超时(最大值)
```

### 2. 充电时保持常亮设置 🔌

```python
# 启用所有充电方式时保持常亮（推荐）
screen_manager.enable_stay_on_while_plugged()

# 禁用充电时保持常亮
screen_manager.disable_stay_on_while_plugged()

# 自定义设置（高级用法）
screen_manager.set_stay_on_while_plugged(3)  # AC和USB充电时保持常亮
screen_manager.set_stay_on_while_plugged(7)  # 所有充电方式都保持常亮
```

**充电时保持常亮设置值说明**：
- `0`: 关闭
- `1`: AC充电时保持常亮
- `2`: USB充电时保持常亮
- `3`: AC和USB充电时都保持常亮
- `4`: 无线充电时保持常亮
- `7`: 所有充电方式都保持常亮 ⭐（推荐）

### 3. 获取屏幕状态信息 📊

```python
status = screen_manager.get_screen_status_info()

print(f"屏幕超时时间: {status['screen_timeout_description']}")
print(f"充电时保持常亮: {status['stay_on_description']}")
print(f"屏幕当前状态: {status['power_state']}")
print(f"屏幕是否点亮: {'是' if status['is_screen_on'] else '否'}")
```

### 4. 一键测试环境设置 ⚡

```python
# 一键设置测试环境（推荐用法）
success = screen_manager.setup_for_testing(timeout_minutes=30)

if success:
    print("测试环境设置成功，屏幕将保持30分钟不关闭")
    # 执行测试...
else:
    print("测试环境设置失败，请检查ADB连接")

# 一键恢复原始设置
screen_manager.restore_original_settings()
```

## 🧪 测试基类使用指南

### 1. 基本用法（零配置）

```python
from examples.base_test_with_screen_management import BaseTestWithScreenManagement


class MyTest(BaseTestWithScreenManagement):
    def test_example(self):
        # 屏幕会自动设置为30分钟超时(默认)
        # 测试结束后自动恢复原始设置
        # 无需任何额外配置
        pass
```

### 2. 自定义配置（推荐）

```python
class MyTest(BaseTestWithScreenManagement):
    def setUp(self):
        # 自定义屏幕管理配置
        self.configure_screen_management(
            enabled=True,           # 启用屏幕管理
            timeout_minutes=60,     # 60分钟超时
            enable_stay_on=True     # 启用充电时常亮
        )
        super().setUp()

    def test_long_running(self):
        # 屏幕会保持60分钟不关闭
        # 如果连接充电器则永不关闭
        pass
```

### 3. 长时间测试（混入类）

```python
from examples.base_test_with_screen_management import (
    BaseTestWithScreenManagement,
    LongRunningTestMixin
)


class MyLongTest(BaseTestWithScreenManagement, LongRunningTestMixin):
    def test_overnight(self):
        # 为过夜测试设置永不超时
        self.setup_for_overnight_test()

        # 执行长时间测试...
        # 屏幕永不关闭
        pass

    def test_stress_test(self):
        # 为长时间测试设置2小时超时
        self.setup_for_long_running_test(timeout_minutes=120)

        # 执行压力测试...
        pass
```

### 4. 快速测试（混入类）

```python
from examples.base_test_with_screen_management import (
    BaseTestWithScreenManagement,
    QuickTestMixin
)


class MyQuickTest(BaseTestWithScreenManagement, QuickTestMixin):
    def test_quick_function(self):
        # 为快速测试设置5分钟超时
        self.setup_for_quick_test(timeout_minutes=5)

        # 执行快速测试...
        pass
```

### 5. 禁用屏幕管理

```python
class MyTest(BaseTestWithScreenManagement):
    def setUp(self):
        # 禁用屏幕管理（不修改屏幕设置）
        self.configure_screen_management(enabled=False)
        super().setUp()

    def test_without_screen_management(self):
        # 不会修改屏幕设置，使用系统默认值
        pass
```

### 6. 动态调整屏幕设置

```python
class MyTest(BaseTestWithScreenManagement):
    def test_dynamic_adjustment(self):
        # 测试过程中动态调整屏幕设置

        # 开始时使用默认设置
        self.log_screen_status()

        # 中途设置为永不超时
        self.set_screen_never_timeout()

        # 执行长时间操作...

        # 恢复到较短超时
        self.set_screen_timeout_minutes(10)

        # 执行后续测试...
```

## 💡 实际应用场景示例

### 1. Ella对话测试（中等时长）

```python
from core.base_test_with_screen_management import BaseTestWithScreenManagement

class EllaDialogueTest(BaseTestWithScreenManagement):
    def setUp(self):
        # Ella对话测试通常需要30-45分钟
        self.configure_screen_management(
            enabled=True,
            timeout_minutes=45,
            enable_stay_on=True
        )
        super().setUp()

    def test_ella_conversation(self):
        # 执行Ella对话测试
        # 屏幕会保持45分钟不关闭
        pass
```

### 2. 应用稳定性测试（长时间）

```python
class AppStabilityTest(BaseTestWithScreenManagement, LongRunningTestMixin):
    def setUp(self):
        self.configure_screen_management(enabled=True)
        super().setUp()

    def test_24_hour_stability(self):
        # 24小时稳定性测试
        self.setup_for_overnight_test()  # 永不超时

        # 执行24小时测试...
        pass
```

### 3. 快速功能验证（短时间）

```python
class QuickFunctionTest(BaseTestWithScreenManagement, QuickTestMixin):
    def test_login_function(self):
        # 快速登录功能测试，5分钟足够
        self.setup_for_quick_test(timeout_minutes=5)

        # 执行登录测试...
        pass
```

### 4. 批量测试套件

```python
class BatchTestSuite(BaseTestWithScreenManagement):
    def setUp(self):
        # 批量测试可能需要2-3小时
        self.configure_screen_management(
            enabled=True,
            timeout_minutes=180,  # 3小时
            enable_stay_on=True
        )
        super().setUp()

    def test_all_features(self):
        # 执行所有功能测试
        pass
```

## ⭐ 最佳实践指南

### 1. 优先使用测试基类（强烈推荐）

✅ **推荐做法**：
```python
# 使用测试基类，自动处理所有屏幕设置
class MyTest(BaseTestWithScreenManagement):
    def setUp(self):
        self.configure_screen_management(timeout_minutes=30)
        super().setUp()
```

❌ **不推荐做法**：
```python
# 手动管理屏幕设置，容易遗漏恢复步骤
def test_manual():
    screen_manager = ScreenManager()
    screen_manager.setup_for_testing(30)
    # 测试代码...
    # 容易忘记恢复设置
```

### 2. 根据测试类型选择合适的超时时间

| 测试类型 | 推荐超时 | 原因 |
|---------|---------|------|
| 单元测试 | 15-30分钟 | 执行时间短，避免过长设置 |
| 功能测试 | 30-60分钟 | 需要较多交互操作 |
| 集成测试 | 60-120分钟 | 涉及多个模块，耗时较长 |
| 稳定性测试 | 永不超时 | 需要长时间运行 |
| 过夜测试 | 永不超时 | 无人值守运行 |

### 3. 确保连接充电器（重要⚡）

```python
# 设置前检查充电状态
def check_charging_status():
    # 通过ADB检查充电状态
    result = subprocess.run(
        ["adb", "shell", "dumpsys", "battery"],
        capture_output=True, text=True
    )
    return "AC powered: true" in result.stdout or "USB powered: true" in result.stdout

# 在测试开始前提醒
if not check_charging_status():
    print("⚠️  建议连接充电器以确保屏幕保持常亮")
```

### 4. 异常安全处理

```python
# 使用测试基类自动处理异常
class SafeTest(BaseTestWithScreenManagement):
    def test_with_exception_safety(self):
        try:
            # 测试代码，即使出现异常
            raise Exception("测试异常")
        except Exception as e:
            print(f"测试异常: {e}")
        # tearDown会自动恢复设置，无需手动处理

# 手动使用时的异常处理
def manual_safe_test():
    screen_manager = ScreenManager()
    try:
        screen_manager.setup_for_testing(timeout_minutes=30)
        # 执行测试...
    except Exception as e:
        print(f"测试异常: {e}")
    finally:
        # 确保恢复原始设置
        screen_manager.restore_original_settings()
```

### 5. 测试环境验证

```python
def verify_test_environment():
    """验证测试环境是否正确设置"""
    screen_manager = ScreenManager()

    # 检查当前设置
    status = screen_manager.get_screen_status_info()

    print("=== 测试环境检查 ===")
    print(f"屏幕超时: {status['screen_timeout_description']}")
    print(f"充电常亮: {status['stay_on_description']}")
    print(f"屏幕状态: {status['power_state']}")

    # 验证设置是否符合预期
    if status['screen_timeout_ms'] and status['screen_timeout_ms'] >= 1800000:  # 30分钟以上
        print("✅ 屏幕超时设置合适")
    else:
        print("⚠️  屏幕超时时间可能过短")

    if status['stay_on_while_plugged'] == 7:
        print("✅ 充电时保持常亮已启用")
    else:
        print("⚠️  建议启用充电时保持常亮")
```

## 🔧 故障排除指南

### 1. 设置不生效

**问题现象**：执行设置命令后屏幕超时时间没有改变

**解决方案**：
```bash
# 1. 检查ADB连接
adb devices

# 2. 检查ADB权限
adb shell settings get system screen_off_timeout

# 3. 手动验证设置
adb shell settings put system screen_off_timeout 1800000
```

**可能原因**：
- ADB连接断开或不稳定
- 设备需要授权ADB调试
- 设备厂商限制了系统设置修改
- Android版本不兼容

### 2. 屏幕仍然自动关闭

**问题现象**：设置了长超时时间但屏幕还是会关闭

**解决方案**：
```python
# 检查充电状态和设置
def diagnose_screen_issue():
    screen_manager = ScreenManager()

    # 1. 检查当前设置
    status = screen_manager.get_screen_status_info()
    print(f"超时设置: {status['screen_timeout_description']}")
    print(f"充电常亮: {status['stay_on_description']}")

    # 2. 检查充电状态
    result = subprocess.run(["adb", "shell", "dumpsys", "battery"],
                          capture_output=True, text=True)
    if "AC powered: true" in result.stdout:
        print("✅ AC充电器已连接")
    elif "USB powered: true" in result.stdout:
        print("✅ USB充电器已连接")
    else:
        print("❌ 未检测到充电器，请连接充电器")

    # 3. 检查省电模式
    result = subprocess.run(["adb", "shell", "dumpsys", "power"],
                          capture_output=True, text=True)
    if "mLowPowerModeEnabled=true" in result.stdout:
        print("⚠️  设备处于省电模式，可能影响屏幕设置")
```

**可能原因**：
- 充电器未连接
- 设备处于省电模式
- 系统有其他屏幕管理应用干扰
- 设备厂商的定制系统限制

### 3. 无法恢复原始设置

**问题现象**：测试结束后无法恢复到原始的屏幕设置

**解决方案**：
```python
# 手动恢复常见的默认设置
def manual_restore_defaults():
    screen_manager = ScreenManager()

    # 恢复常见的默认值
    screen_manager.set_screen_timeout(600000)  # 10分钟
    screen_manager.set_stay_on_while_plugged(0)  # 关闭充电时常亮

    print("已恢复到常见的默认设置")

# 或者通过设备设置手动恢复
# 设置 -> 显示 -> 屏幕超时 -> 选择合适的时间
# 设置 -> 开发者选项 -> 不锁定屏幕 -> 关闭
```

### 4. ADB连接问题

**问题现象**：无法通过ADB连接设备

**解决方案**：
```bash
# 1. 重启ADB服务
adb kill-server
adb start-server

# 2. 检查设备连接
adb devices -l

# 3. 重新授权设备
adb shell  # 如果提示授权，在设备上点击允许

# 4. 检查USB调试是否开启
# 设置 -> 开发者选项 -> USB调试
```

## 📚 参考资料和示例

### 完整示例代码
- 📁 `examples/screen_management_example.py` - 基本使用示例
- 📁 `core/base_test_with_screen_management.py` - 测试基类示例
- 📁 `utils/screen_manager.py` - 核心管理类

### 运行示例
```bash
# 运行基本示例
python examples/screen_management_example.py

# 运行测试基类示例
python -m unittest core.base_test_with_screen_management.ExampleTestWithScreenManagement
```

## ⚠️ 重要注意事项

### 系统兼容性
- ✅ **支持**：Android 6.0+ (API 23+)
- ✅ **测试过的设备**：小米、华为、OPPO、vivo、三星等主流品牌
- ⚠️ **可能有限制**：部分定制系统可能有额外限制

### 使用限制
1. **ADB权限**：需要开启USB调试并授权ADB连接
2. **系统权限**：某些设备可能需要额外的系统权限
3. **电池影响**：长时间保持屏幕常亮会消耗更多电量
4. **发热问题**：长时间亮屏可能导致设备发热

### 安全建议
1. **测试结束后务必恢复设置**，避免影响日常使用
2. **建议连接充电器**，避免电量耗尽
3. **定期检查设备温度**，避免过热
4. **不要在生产环境使用**，仅限测试环境

### 版本更新
- 当前版本：v1.0.0
- 最后更新：2025-08-08
- 维护状态：积极维护中

如有问题或建议，请联系开发团队。
