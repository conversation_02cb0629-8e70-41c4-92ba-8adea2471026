# 屏幕防灭屏快速参考

## 🚀 一分钟快速上手

### 问题
测试过程中手机屏幕自动关闭，影响测试执行。

### 解决方案
使用屏幕管理工具自动设置屏幕超时时间和充电时保持常亮。

## 📋 快速使用

### 方法一：测试基类（推荐⭐）

```python
from core.base_test_with_screen_management import BaseTestWithScreenManagement

class MyTest(BaseTestWithScreenManagement):
    def setUp(self):
        # 设置45分钟超时，启用充电时常亮
        self.configure_screen_management(
            enabled=True,
            timeout_minutes=45,
            enable_stay_on=True
        )
        super().setUp()
    
    def test_my_case(self):
        # 你的测试代码
        # 屏幕会保持45分钟不关闭
        pass
    
    # 测试结束后自动恢复原始设置
```

### 方法二：直接使用

```python
from utils.screen_manager import ScreenManager

# 创建管理器
screen_manager = ScreenManager()

# 设置测试环境
screen_manager.setup_for_testing(timeout_minutes=30)

# 执行测试...

# 恢复原始设置
screen_manager.restore_original_settings()
```

### 方法三：永不超时（长时间测试）

```python
from utils.screen_manager import ScreenManager

screen_manager = ScreenManager()

# 设置永不超时
screen_manager.set_screen_never_timeout()

# 启用充电时常亮
screen_manager.enable_stay_on_while_plugged()

# 执行长时间测试...

# 恢复设置
screen_manager.restore_original_settings()
```

## ⚙️ 常用设置

| 测试类型 | 推荐设置 | 代码 |
|---------|---------|------|
| 快速测试 | 15分钟 | `setup_for_testing(timeout_minutes=15)` |
| 中等测试 | 45分钟 | `setup_for_testing(timeout_minutes=45)` |
| 长时间测试 | 永不超时 | `set_screen_never_timeout()` |
| 过夜测试 | 永不超时 | `set_screen_never_timeout()` |

## 🔧 核心API

### ScreenManager 主要方法

```python
# 基本设置
screen_manager.set_screen_timeout_minutes(30)    # 设置30分钟超时
screen_manager.set_screen_never_timeout()        # 永不超时
screen_manager.enable_stay_on_while_plugged()    # 启用充电时常亮
screen_manager.disable_stay_on_while_plugged()   # 禁用充电时常亮

# 一键操作
screen_manager.setup_for_testing(timeout_minutes=30)  # 一键设置测试环境
screen_manager.restore_original_settings()            # 一键恢复原始设置

# 状态查询
status = screen_manager.get_screen_status_info()       # 获取详细状态
timeout = screen_manager.get_current_screen_timeout()  # 获取当前超时时间
```

### 测试基类配置

```python
# 在setUp中配置
self.configure_screen_management(
    enabled=True,           # 是否启用屏幕管理
    timeout_minutes=45,     # 超时时间(分钟)
    enable_stay_on=True     # 是否启用充电时常亮
)

# 测试过程中动态调整
self.set_screen_never_timeout()              # 设置永不超时
self.set_screen_timeout_minutes(60)          # 设置60分钟超时
self.log_screen_status()                     # 记录当前状态
```

## ⚠️ 重要提醒

### 使用前准备
1. **连接充电器** - 启用充电时常亮功能需要连接充电器
2. **开启USB调试** - 确保ADB连接正常
3. **授权ADB** - 首次连接时需要在设备上授权

### 最佳实践
1. **优先使用测试基类** - 自动处理设置和恢复
2. **根据测试时长选择合适超时** - 避免设置过长或过短
3. **测试结束后恢复设置** - 避免影响日常使用
4. **异常处理** - 确保即使测试失败也能恢复设置

### 故障排除
```python
# 检查ADB连接
adb devices

# 检查当前设置
adb shell settings get system screen_off_timeout

# 检查充电状态
adb shell dumpsys battery | grep "AC powered\|USB powered"

# 手动设置（测试用）
adb shell settings put system screen_off_timeout 1800000  # 30分钟
```

## 📁 文件位置

- **核心类**：`utils/screen_manager.py`
- **测试基类**：`core/base_test_with_screen_management.py`
- **使用示例**：`examples/screen_management_example.py`
- **详细文档**：`docs/screen_management_guide.md`

## 🎯 快速测试

运行示例验证功能：

```bash
# 运行基本示例
python examples/screen_management_example.py

# 运行测试基类示例
python -m unittest core.base_test_with_screen_management.ExampleTestWithScreenManagement -v
```

## 📞 支持

如遇问题：
1. 查看详细文档：`docs/screen_management_guide.md`
2. 检查示例代码：`examples/screen_management_example.py`
3. 联系开发团队

---

**版本**：v1.0.0 | **更新时间**：2025-08-08
