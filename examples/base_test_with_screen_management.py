"""
带屏幕管理的测试基类
自动处理测试过程中的屏幕设置，防止手机自动灭屏
"""
import unittest
import time
from typing import Optional
from core.logger import log
from utils.screen_manager import ScreenManager


class BaseTestWithScreenManagement(unittest.TestCase):
    """带屏幕管理的测试基类"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.screen_manager: Optional[ScreenManager] = None
        self._screen_setup_enabled = True
        self._screen_timeout_minutes = 30
        self._enable_stay_on_while_plugged = True

    def configure_screen_management(self, 
                                  enabled: bool = True,
                                  timeout_minutes: int = 30,
                                  enable_stay_on: bool = True):
        """
        配置屏幕管理参数

        Args:
            enabled: 是否启用屏幕管理
            timeout_minutes: 屏幕超时时间(分钟)
            enable_stay_on: 是否启用充电时保持常亮
        """
        self._screen_setup_enabled = enabled
        self._screen_timeout_minutes = timeout_minutes
        self._enable_stay_on_while_plugged = enable_stay_on
        log.info(f"屏幕管理配置: 启用={enabled}, 超时={timeout_minutes}分钟, 充电常亮={enable_stay_on}")

    def setUp(self):
        """测试设置 - 自动配置屏幕参数"""
        super().setUp()
        
        if self._screen_setup_enabled:
            try:
                log.info("开始设置测试屏幕参数...")
                self.screen_manager = ScreenManager()
                
                # 获取并记录当前状态
                current_status = self.screen_manager.get_screen_status_info()
                log.info(f"测试前屏幕状态: 超时={current_status['screen_timeout_description']}, "
                        f"充电常亮={current_status['stay_on_description']}")
                
                # 设置测试环境
                success = self.screen_manager.setup_for_testing(
                    timeout_minutes=self._screen_timeout_minutes
                )
                
                if success:
                    log.info("测试屏幕参数设置成功")
                    
                    # 验证设置结果
                    new_status = self.screen_manager.get_screen_status_info()
                    log.info(f"设置后屏幕状态: 超时={new_status['screen_timeout_description']}, "
                            f"充电常亮={new_status['stay_on_description']}")
                else:
                    log.warning("测试屏幕参数设置失败，测试可能会受到屏幕自动关闭影响")
                    
            except Exception as e:
                log.error(f"设置测试屏幕参数异常: {e}")
                self.screen_manager = None

    def tearDown(self):
        """测试清理 - 自动恢复屏幕参数"""
        if self._screen_setup_enabled and self.screen_manager:
            try:
                log.info("开始恢复原始屏幕参数...")
                
                success = self.screen_manager.restore_original_settings()
                
                if success:
                    log.info("原始屏幕参数恢复成功")
                    
                    # 验证恢复结果
                    restored_status = self.screen_manager.get_screen_status_info()
                    log.info(f"恢复后屏幕状态: 超时={restored_status['screen_timeout_description']}, "
                            f"充电常亮={restored_status['stay_on_description']}")
                else:
                    log.warning("原始屏幕参数恢复失败")
                    
            except Exception as e:
                log.error(f"恢复原始屏幕参数异常: {e}")
            finally:
                self.screen_manager = None
        
        super().tearDown()

    def get_screen_status(self) -> dict:
        """
        获取当前屏幕状态信息

        Returns:
            dict: 屏幕状态信息
        """
        if self.screen_manager:
            return self.screen_manager.get_screen_status_info()
        else:
            return {}

    def set_screen_never_timeout(self) -> bool:
        """
        在测试过程中设置屏幕永不超时

        Returns:
            bool: 是否设置成功
        """
        if self.screen_manager:
            return self.screen_manager.set_screen_never_timeout()
        else:
            log.warning("屏幕管理器未初始化")
            return False

    def set_screen_timeout_minutes(self, minutes: int) -> bool:
        """
        在测试过程中设置屏幕超时时间

        Args:
            minutes: 超时时间(分钟)

        Returns:
            bool: 是否设置成功
        """
        if self.screen_manager:
            return self.screen_manager.set_screen_timeout_minutes(minutes)
        else:
            log.warning("屏幕管理器未初始化")
            return False

    def log_screen_status(self):
        """记录当前屏幕状态到日志"""
        if self.screen_manager:
            status = self.screen_manager.get_screen_status_info()
            log.info(f"当前屏幕状态: 超时={status['screen_timeout_description']}, "
                    f"充电常亮={status['stay_on_description']}, "
                    f"屏幕状态={status['power_state']}")
        else:
            log.warning("屏幕管理器未初始化，无法获取屏幕状态")


class LongRunningTestMixin:
    """长时间运行测试的混入类"""

    def setup_for_long_running_test(self, timeout_minutes: int = 60):
        """
        为长时间运行的测试设置屏幕参数

        Args:
            timeout_minutes: 屏幕超时时间(分钟)，默认60分钟
        """
        if hasattr(self, 'screen_manager') and self.screen_manager:
            log.info(f"为长时间测试设置屏幕超时: {timeout_minutes}分钟")
            success = self.screen_manager.set_screen_timeout_minutes(timeout_minutes)
            if success:
                log.info("长时间测试屏幕设置完成")
            else:
                log.warning("长时间测试屏幕设置失败")
        else:
            log.warning("屏幕管理器未初始化，无法设置长时间测试参数")

    def setup_for_overnight_test(self):
        """为过夜测试设置屏幕参数(永不超时)"""
        if hasattr(self, 'screen_manager') and self.screen_manager:
            log.info("为过夜测试设置屏幕永不超时")
            success = self.screen_manager.set_screen_never_timeout()
            if success:
                log.info("过夜测试屏幕设置完成")
            else:
                log.warning("过夜测试屏幕设置失败")
        else:
            log.warning("屏幕管理器未初始化，无法设置过夜测试参数")


class QuickTestMixin:
    """快速测试的混入类"""

    def setup_for_quick_test(self, timeout_minutes: int = 5):
        """
        为快速测试设置屏幕参数

        Args:
            timeout_minutes: 屏幕超时时间(分钟)，默认5分钟
        """
        if hasattr(self, 'screen_manager') and self.screen_manager:
            log.info(f"为快速测试设置屏幕超时: {timeout_minutes}分钟")
            success = self.screen_manager.set_screen_timeout_minutes(timeout_minutes)
            if success:
                log.info("快速测试屏幕设置完成")
            else:
                log.warning("快速测试屏幕设置失败")
        else:
            log.warning("屏幕管理器未初始化，无法设置快速测试参数")


# 示例测试类
class ExampleTestWithScreenManagement(BaseTestWithScreenManagement, LongRunningTestMixin):
    """示例测试类 - 演示如何使用屏幕管理"""

    def setUp(self):
        """测试设置"""
        # 配置屏幕管理参数
        self.configure_screen_management(
            enabled=True,
            timeout_minutes=45,  # 45分钟超时
            enable_stay_on=True
        )
        super().setUp()

    def test_example_short_test(self):
        """示例短测试"""
        log.info("开始执行短测试")
        
        # 记录屏幕状态
        self.log_screen_status()
        
        # 模拟测试步骤
        time.sleep(2)
        
        log.info("短测试执行完成")

    def test_example_long_test(self):
        """示例长测试"""
        log.info("开始执行长测试")
        
        # 为长测试设置更长的超时时间
        self.setup_for_long_running_test(timeout_minutes=90)
        
        # 记录屏幕状态
        self.log_screen_status()
        
        # 模拟长时间测试步骤
        for i in range(5):
            log.info(f"执行长测试步骤 {i+1}/5")
            time.sleep(1)
        
        log.info("长测试执行完成")

    def test_example_overnight_test(self):
        """示例过夜测试"""
        log.info("开始执行过夜测试")
        
        # 为过夜测试设置永不超时
        self.setup_for_overnight_test()
        
        # 记录屏幕状态
        self.log_screen_status()
        
        # 模拟过夜测试(这里只是短暂模拟)
        log.info("模拟过夜测试执行...")
        time.sleep(3)
        
        log.info("过夜测试执行完成")


if __name__ == "__main__":
    # 运行示例测试
    unittest.main(verbosity=2)
