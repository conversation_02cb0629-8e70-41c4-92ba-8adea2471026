"""
Ella弹窗处理实际使用示例
展示如何在Ella测试中使用集成的弹窗处理功能
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import BaseEllaTest, SimpleEllaTest
from core.logger import log


class TestEllaWithPopupHandling(BaseEllaTest):
    """使用弹窗处理的Ella测试示例"""

    @pytest.fixture(scope="function")
    def ella_app_with_popup_config(self):
        """配置了弹窗处理的Ella应用fixture"""
        # 自定义弹窗处理配置
        self.set_popup_handling_config(
            enabled=True,
            timeout=5,  # 增加超时时间以处理复杂弹窗
            check_interval=0.3
        )
        
        # 使用父类的ella_app fixture
        with self.ella_app() as ella_page:
            yield ella_page

    @allure.feature("蓝牙控制")
    @allure.story("蓝牙开关控制")
    def test_bluetooth_control_with_popup_handling(self, ella_app):
        """测试蓝牙控制命令的弹窗处理"""
        with allure.step("执行蓝牙开启命令"):
            # 执行命令，自动处理可能出现的系统弹窗
            initial_status, final_status, response_text, _ = self.execute_command_and_verify(
                ella_app, "open bluetooth", expected_status_change=True
            )

        with allure.step("验证蓝牙状态变化"):
            assert final_status, f"蓝牙应该已开启: 初始={initial_status}, 最终={final_status}"

        with allure.step("验证响应内容"):
            self.verify_expected_in_response("bluetooth", response_text)

        # 记录测试结果
        summary = self.create_test_summary("open bluetooth", initial_status, final_status, response_text)
        self.attach_test_summary(summary)
        self.take_screenshot(ella_app, "bluetooth_opened")

    @allure.feature("应用启动")
    @allure.story("联系人应用")
    def test_contacts_app_with_popup_handling(self, ella_app):
        """测试联系人应用启动的弹窗处理"""
        with allure.step("执行打开联系人命令"):
            # 执行命令，自动处理可能的权限弹窗或应用启动弹窗
            initial_status, final_status, response_text, _ = self.execute_command_and_verify(
                ella_app, "open contacts", expected_status_change=True
            )

        with allure.step("验证联系人应用已打开"):
            assert final_status, f"联系人应用应该已打开: 初始={initial_status}, 最终={final_status}"

        with allure.step("验证响应内容"):
            self.verify_expected_in_response(["contacts", "contact"], response_text)

        # 记录测试结果
        summary = self.create_test_summary("open contacts", initial_status, final_status, response_text)
        self.attach_test_summary(summary)
        self.take_screenshot(ella_app, "contacts_opened")

    @allure.feature("相机功能")
    @allure.story("拍照功能")
    def test_camera_photo_with_popup_handling(self, ella_app):
        """测试相机拍照功能的弹窗处理"""
        # 相机功能可能需要更长的弹窗处理时间
        self.set_popup_handling_config(enabled=True, timeout=10, check_interval=0.5)

        with allure.step("执行拍照命令"):
            # 执行命令，自动处理相机权限弹窗
            initial_status, final_status, response_text, files_status = self.execute_command_and_verify(
                ella_app, "take a photo", expected_status_change=False, verify_files=True
            )

        with allure.step("验证拍照功能"):
            # 验证响应内容包含相机相关信息
            self.verify_expected_in_response(["photo", "camera", "picture"], response_text)

        with allure.step("验证文件生成"):
            if files_status:
                log.info("✅ 成功检测到拍照文件")
            else:
                log.warning("⚠️ 未检测到拍照文件，可能需要手动验证")

        # 记录测试结果
        summary = self.create_test_summary("take a photo", initial_status, final_status, response_text)
        self.attach_test_summary(summary)
        self.take_screenshot(ella_app, "photo_taken")


class TestEllaSimpleWithPopup(SimpleEllaTest):
    """使用SimpleEllaTest的弹窗处理示例"""

    def setup_method(self):
        """测试前配置"""
        # 启用弹窗处理
        self.enable_popup_handling(timeout=5, check_interval=0.3)

    @allure.feature("系统设置")
    @allure.story("WiFi控制")
    def test_wifi_control_simple(self, ella_app):
        """简化的WiFi控制测试"""
        # 使用简化的测试方法，自动处理弹窗
        initial_status, final_status, response_text, _ = self.simple_command_test(
            ella_app, "open wifi", verify_status=True
        )

        # 验证响应
        self.verify_expected_in_response("wifi", response_text)

    @allure.feature("手动弹窗处理")
    @allure.story("手动检查示例")
    def test_manual_popup_check_example(self, ella_app):
        """手动弹窗检查示例"""
        with allure.step("执行可能产生弹窗的操作"):
            # 执行命令但不使用自动弹窗处理
            self.disable_popup_handling()
            ella_app.execute_text_command("open camera")

        with allure.step("手动检查弹窗"):
            # 手动检查并处理弹窗
            popup_handled = self.manual_popup_check(ella_app)
            if popup_handled:
                log.info("✅ 手动处理了弹窗")
            else:
                log.info("ℹ️ 没有检测到弹窗")

        with allure.step("继续测试流程"):
            # 重新启用自动弹窗处理
            self.enable_popup_handling()
            
            # 等待响应
            ella_app.wait_for_response(timeout=5)
            response_text = ella_app.get_response_text()
            
            # 验证响应
            if response_text:
                self.verify_expected_in_response("camera", response_text)

        self.take_screenshot(ella_app, "manual_popup_handled")


class TestEllaPopupConfiguration(BaseEllaTest):
    """弹窗处理配置示例"""

    @allure.feature("配置管理")
    @allure.story("动态配置")
    def test_dynamic_popup_configuration(self, ella_app):
        """动态弹窗配置示例"""
        
        with allure.step("使用默认配置执行命令"):
            # 使用默认弹窗处理配置
            log.info("使用默认弹窗处理配置")
            initial_status, final_status, response_text, _ = self.execute_command_and_verify(
                ella_app, "open bluetooth"
            )

        with allure.step("调整配置执行复杂命令"):
            # 为复杂操作调整配置
            self.set_popup_handling_config(
                enabled=True,
                timeout=10,  # 增加超时时间
                check_interval=0.5  # 增加检查间隔
            )
            
            log.info("使用调整后的弹窗处理配置")
            initial_status2, final_status2, response_text2, _ = self.execute_command_and_verify(
                ella_app, "take a selfie"
            )

        with allure.step("禁用弹窗处理执行命令"):
            # 临时禁用弹窗处理
            self.set_popup_handling_config(enabled=False)
            
            log.info("禁用弹窗处理")
            initial_status3, final_status3, response_text3, _ = self.execute_command_and_verify(
                ella_app, "close bluetooth"
            )

        with allure.step("验证所有操作结果"):
            # 验证所有操作的响应
            all_responses = [response_text, response_text2, response_text3]
            log.info(f"所有响应: {all_responses}")

        # 恢复默认配置
        self.set_popup_handling_config(enabled=True, timeout=3, check_interval=0.3)

    @allure.feature("性能测试")
    @allure.story("弹窗处理性能")
    def test_popup_handling_performance(self, ella_app):
        """弹窗处理性能测试"""
        import time

        commands = [
            "open bluetooth",
            "close bluetooth", 
            "open wifi",
            "close wifi"
        ]

        with allure.step("测试启用弹窗处理的性能"):
            self.set_popup_handling_config(enabled=True)
            
            start_time = time.time()
            for command in commands:
                self.execute_command_and_verify(ella_app, command)
                time.sleep(1)  # 命令间隔
            enabled_time = time.time() - start_time

        with allure.step("测试禁用弹窗处理的性能"):
            self.set_popup_handling_config(enabled=False)
            
            start_time = time.time()
            for command in commands:
                self.execute_command_and_verify(ella_app, command)
                time.sleep(1)  # 命令间隔
            disabled_time = time.time() - start_time

        with allure.step("分析性能影响"):
            performance_impact = enabled_time - disabled_time
            log.info(f"启用弹窗处理耗时: {enabled_time:.2f}秒")
            log.info(f"禁用弹窗处理耗时: {disabled_time:.2f}秒")
            log.info(f"性能影响: {performance_impact:.2f}秒")
            
            # 性能影响应该在可接受范围内
            assert performance_impact < 5.0, f"弹窗处理性能影响过大: {performance_impact:.2f}秒"

        # 恢复默认配置
        self.set_popup_handling_config(enabled=True, timeout=3, check_interval=0.3)


if __name__ == "__main__":
    # 运行示例测试
    pytest.main([
        __file__,
        "-v",
        "--allure-dir=reports/allure-results",
        "-k", "test_bluetooth_control_with_popup_handling"
    ])
