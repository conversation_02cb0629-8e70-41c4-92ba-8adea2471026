"""
弹窗处理工具演示脚本
展示如何使用 core/popup_tool.py 中的弹窗处理功能
"""
import time
import uiautomator2 as u2
from core.popup_tool import create_popup_tool, create_automation_with_popup
from core.logger import log


def demo_basic_popup_detection():
    """演示基本的弹窗检测功能"""
    print("\n=== 基本弹窗检测演示 ===")
    
    # 创建弹窗处理工具
    popup_tool = create_popup_tool()
    
    # 检测当前是否有弹窗
    if popup_tool.is_popup_present():
        print("✓ 检测到弹窗存在")
        
        # 尝试关闭弹窗
        if popup_tool.detect_and_close_popup(timeout=10):
            print("✓ 成功关闭弹窗")
        else:
            print("✗ 弹窗关闭失败")
    else:
        print("ℹ 当前没有检测到弹窗")


def demo_automation_with_popup():
    """演示自动化操作中的弹窗处理"""
    print("\n=== 自动化操作弹窗处理演示 ===")
    
    # 创建自动化工具
    automation = create_automation_with_popup()
    
    try:
        # 模拟一系列操作，每个操作都会自动处理弹窗
        print("执行点击操作...")
        automation.click(500, 300)
        time.sleep(1)
        
        print("执行输入操作...")
        automation.input_text("测试文本")
        time.sleep(1)
        
        print("执行元素点击操作...")
        automation.click_element({'className': 'android.widget.Button'})
        time.sleep(1)
        
        print("✓ 所有操作完成，弹窗已自动处理")
        
    except Exception as e:
        print(f"✗ 操作过程中出现错误: {e}")


def demo_safe_action_wrapper():
    """演示安全操作包装器"""
    print("\n=== 安全操作包装器演示 ===")
    
    popup_tool = create_popup_tool()
    
    def complex_operation():
        """复杂的自定义操作"""
        device = popup_tool.device
        
        # 执行一系列操作
        device.click(100, 200)
        time.sleep(0.5)
        
        device.send_keys("用户名")
        time.sleep(0.5)
        
        device.click(200, 300)
        time.sleep(0.5)
        
        device.send_keys("密码")
        time.sleep(0.5)
        
        device.click(300, 400)  # 登录按钮
        
        return "登录操作完成"
    
    try:
        # 使用safe_action包装操作，自动处理弹窗
        result = popup_tool.safe_action(complex_operation)
        print(f"✓ {result}")
        
    except Exception as e:
        print(f"✗ 操作失败: {e}")


def demo_app_launch_with_popup_handling():
    """演示应用启动时的弹窗处理"""
    print("\n=== 应用启动弹窗处理演示 ===")
    
    automation = create_automation_with_popup()
    
    # 应用包名（请根据实际情况修改）
    app_package = "com.android.settings"  # 使用系统设置作为示例
    
    try:
        print(f"启动应用: {app_package}")
        automation.device.app_start(app_package)
        
        # 等待应用启动
        time.sleep(3)
        
        print("检查并处理启动后的弹窗...")
        if automation.detect_and_close_popup(timeout=10):
            print("✓ 处理了启动弹窗")
        else:
            print("ℹ 没有发现启动弹窗")
        
        # 等待一段时间观察
        time.sleep(2)
        
        print("应用启动完成")
        
    except Exception as e:
        print(f"✗ 应用启动失败: {e}")


def demo_permission_dialog_handling():
    """演示权限对话框处理"""
    print("\n=== 权限对话框处理演示 ===")
    
    popup_tool = create_popup_tool()
    
    # 检查是否有权限相关的弹窗
    device = popup_tool.device
    
    # 查找权限相关的文本
    permission_keywords = ['权限', '允许', '拒绝', 'permission', 'allow', 'deny']
    
    found_permission_dialog = False
    for keyword in permission_keywords:
        if device(textContains=keyword).exists(timeout=1):
            print(f"✓ 发现权限相关弹窗: {keyword}")
            found_permission_dialog = True
            break
    
    if found_permission_dialog:
        print("尝试处理权限弹窗...")
        if popup_tool.detect_and_close_popup(timeout=5):
            print("✓ 权限弹窗处理成功")
        else:
            print("✗ 权限弹窗处理失败")
    else:
        print("ℹ 没有发现权限相关弹窗")


def demo_batch_operations():
    """演示批量操作中的弹窗处理"""
    print("\n=== 批量操作弹窗处理演示 ===")
    
    popup_tool = create_popup_tool()
    
    # 定义一系列操作
    operations = [
        ("点击操作1", lambda: popup_tool.device.click(100, 200)),
        ("点击操作2", lambda: popup_tool.device.click(200, 300)),
        ("输入操作", lambda: popup_tool.device.send_keys("测试")),
        ("点击操作3", lambda: popup_tool.device.click(300, 400)),
    ]
    
    success_count = 0
    
    for i, (name, operation) in enumerate(operations, 1):
        try:
            print(f"执行第{i}个操作: {name}")
            
            # 使用safe_action包装每个操作
            popup_tool.safe_action(operation)
            
            print(f"✓ {name} 完成")
            success_count += 1
            
            # 操作间隔
            time.sleep(1)
            
        except Exception as e:
            print(f"✗ {name} 失败: {e}")
    
    print(f"\n批量操作完成: {success_count}/{len(operations)} 成功")


def demo_device_info():
    """演示设备信息获取"""
    print("\n=== 设备信息演示 ===")
    
    popup_tool = create_popup_tool()
    device = popup_tool.device
    
    try:
        info = device.info
        print(f"设备信息:")
        print(f"  屏幕尺寸: {info.get('displayWidth', 'N/A')} x {info.get('displayHeight', 'N/A')}")
        print(f"  设备型号: {info.get('productName', 'N/A')}")
        print(f"  Android版本: {info.get('version', 'N/A')}")
        print(f"  SDK版本: {info.get('sdkInt', 'N/A')}")
        
        # 检查UIAutomator2服务状态
        print(f"  UIAutomator2服务: {'正常' if device.uiautomator.running() else '异常'}")
        
    except Exception as e:
        print(f"✗ 获取设备信息失败: {e}")


def main():
    """主函数 - 运行所有演示"""
    print("弹窗处理工具演示程序")
    print("=" * 50)
    
    try:
        # 检查设备连接
        device = u2.connect()
        print(f"✓ 设备连接成功")
        
        # 运行各种演示
        demo_device_info()
        demo_basic_popup_detection()
        demo_automation_with_popup()
        demo_safe_action_wrapper()
        demo_permission_dialog_handling()
        demo_batch_operations()
        
        # 注意：应用启动演示可能会影响当前应用状态，所以放在最后
        # demo_app_launch_with_popup_handling()
        
        print("\n" + "=" * 50)
        print("✓ 所有演示完成")
        
    except Exception as e:
        print(f"✗ 演示程序出错: {e}")
        log.error(f"演示程序异常: {e}")


if __name__ == "__main__":
    main()
