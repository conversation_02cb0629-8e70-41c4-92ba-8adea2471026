"""
屏幕状态检测使用示例
演示如何使用新增的屏幕检测功能
"""
import sys
import os
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def example_basic_screen_detection():
    """基本屏幕检测示例"""
    print("\n=== 基本屏幕检测示例 ===")
    
    checker = SystemStatusChecker()
    
    # 简单检查屏幕是否灭屏
    is_off = checker.is_screen_off()
    
    if is_off is True:
        print("📱 屏幕状态: 已灭屏")
    elif is_off is False:
        print("📱 屏幕状态: 未灭屏")
    else:
        print("📱 屏幕状态: 无法确定")


def example_detailed_screen_status():
    """详细屏幕状态检测示例"""
    print("\n=== 详细屏幕状态检测示例 ===")
    
    checker = SystemStatusChecker()
    
    # 获取详细屏幕状态
    status = checker.check_screen_status()
    
    print("📊 详细屏幕状态信息:")
    print(f"  🔆 屏幕开启: {'是' if status['screen_on'] else '否' if status['screen_on'] is not None else '未知'}")
    print(f"  🔒 屏幕锁定: {'是' if status['screen_locked'] else '否' if status['screen_locked'] is not None else '未知'}")
    print(f"  👆 可交互: {'是' if status['interactive'] else '否' if status['interactive'] is not None else '未知'}")
    print(f"  📺 显示状态: {status['display_state'] or '未知'}")
    print(f"  ⏰ 超时设置: {status['screen_timeout_description']}")
    print(f"  🔌 充电常亮: {status['stay_on_description']}")
    print(f"  💡 亮度级别: {status['brightness_level'] or '未知'}")
    print(f"  🌟 亮度模式: {status['brightness_mode'] or '未知'}")
    print(f"  🔋 充电状态: {status['charging_status'] or '未知'}")
    print(f"  📊 电池电量: {status['battery_level']}%" if status['battery_level'] is not None else "  📊 电池电量: 未知")
    print(f"  🔓 唤醒锁数量: {status['wake_lock_count']}")
    print(f"  ⚡ 电源状态: {status['power_state']}")


def example_screen_monitoring():
    """屏幕状态监控示例"""
    print("\n=== 屏幕状态监控示例 ===")
    
    checker = SystemStatusChecker()
    
    print("开始监控屏幕状态变化（监控10次，每次间隔3秒）...")
    print("请在监控期间尝试开启/关闭屏幕以观察状态变化")
    
    previous_state = None
    
    for i in range(10):
        print(f"\n--- 第 {i+1}/10 次检测 ---")
        
        # 检查屏幕是否灭屏
        is_off = checker.is_screen_off()
        
        current_state = "灭屏" if is_off is True else "亮屏" if is_off is False else "未知"
        
        if previous_state is not None and current_state != previous_state:
            print(f"🔄 屏幕状态变化: {previous_state} → {current_state}")
        else:
            print(f"📱 屏幕状态: {current_state}")
        
        previous_state = current_state
        
        if i < 9:  # 最后一次不需要等待
            time.sleep(3)


def example_screen_status_with_conditions():
    """带条件判断的屏幕状态检测示例"""
    print("\n=== 带条件判断的屏幕状态检测示例 ===")
    
    checker = SystemStatusChecker()
    
    # 获取详细状态
    status = checker.check_screen_status()
    
    print("🔍 屏幕状态分析:")
    
    # 判断屏幕是否适合进行UI操作
    if status['screen_on'] and status['interactive'] and not status['screen_locked']:
        print("✅ 屏幕状态良好，适合进行UI自动化操作")
    elif status['screen_on'] and status['screen_locked']:
        print("⚠️ 屏幕已开启但被锁定，需要解锁后才能进行UI操作")
    elif not status['screen_on']:
        print("❌ 屏幕已关闭，需要唤醒屏幕后才能进行UI操作")
    elif not status['interactive']:
        print("⚠️ 屏幕开启但不可交互，可能处于特殊状态")
    else:
        print("❓ 屏幕状态不明确，建议进一步检查")
    
    # 判断是否需要调整屏幕设置
    if status['screen_timeout_ms'] and status['screen_timeout_ms'] < 300000:  # 小于5分钟
        print("⚠️ 屏幕超时时间较短，建议延长以避免测试中断")
    
    # 判断充电状态对屏幕常亮的影响
    if status['stay_on_while_plugged'] and status['stay_on_while_plugged'] > 0:
        if "充电中" in (status['charging_status'] or ""):
            print("✅ 已启用充电时保持常亮，且设备正在充电，屏幕不会自动关闭")
        else:
            print("⚠️ 已启用充电时保持常亮，但设备未充电，屏幕仍可能自动关闭")
    else:
        print("ℹ️ 未启用充电时保持常亮功能")


def example_wait_for_screen_state():
    """等待屏幕状态变化示例"""
    print("\n=== 等待屏幕状态变化示例 ===")
    
    checker = SystemStatusChecker()
    
    def wait_for_screen_on(timeout=30):
        """等待屏幕开启"""
        print(f"等待屏幕开启（超时: {timeout}秒）...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            is_off = checker.is_screen_off()
            
            if is_off is False:  # 屏幕已开启
                print("✅ 屏幕已开启")
                return True
            elif is_off is True:  # 屏幕仍然关闭
                print("⏳ 屏幕仍然关闭，继续等待...")
            else:  # 无法确定状态
                print("❓ 无法确定屏幕状态，继续等待...")
            
            time.sleep(2)
        
        print("❌ 等待屏幕开启超时")
        return False
    
    def wait_for_screen_off(timeout=30):
        """等待屏幕关闭"""
        print(f"等待屏幕关闭（超时: {timeout}秒）...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            is_off = checker.is_screen_off()
            
            if is_off is True:  # 屏幕已关闭
                print("✅ 屏幕已关闭")
                return True
            elif is_off is False:  # 屏幕仍然开启
                print("⏳ 屏幕仍然开启，继续等待...")
            else:  # 无法确定状态
                print("❓ 无法确定屏幕状态，继续等待...")
            
            time.sleep(2)
        
        print("❌ 等待屏幕关闭超时")
        return False
    
    # 检查当前状态
    current_is_off = checker.is_screen_off()
    
    if current_is_off is True:
        print("当前屏幕已关闭，演示等待屏幕开启...")
        wait_for_screen_on(15)
    elif current_is_off is False:
        print("当前屏幕已开启，演示等待屏幕关闭...")
        print("请手动关闭屏幕或等待自动超时...")
        wait_for_screen_off(15)
    else:
        print("无法确定当前屏幕状态")


def example_integration_with_screen_manager():
    """与屏幕管理器集成使用示例"""
    print("\n=== 与屏幕管理器集成使用示例 ===")
    
    try:
        from utils.screen_manager import ScreenManager
        
        checker = SystemStatusChecker()
        screen_manager = ScreenManager()
        
        print("🔧 演示屏幕管理和检测的集成使用...")
        
        # 1. 检查当前状态
        print("\n1. 检查当前屏幕状态:")
        status = checker.check_screen_status()
        print(f"   屏幕开启: {'是' if status['screen_on'] else '否' if status['screen_on'] is not None else '未知'}")
        print(f"   超时设置: {status['screen_timeout_description']}")
        
        # 2. 设置屏幕永不超时
        print("\n2. 设置屏幕永不超时...")
        success = screen_manager.set_screen_never_timeout()
        print(f"   设置结果: {'成功' if success else '失败'}")
        
        # 3. 验证设置是否生效
        print("\n3. 验证设置是否生效:")
        time.sleep(2)  # 等待设置生效
        new_status = checker.check_screen_status()
        print(f"   新的超时设置: {new_status['screen_timeout_description']}")
        
        # 4. 恢复原始设置
        print("\n4. 恢复原始设置...")
        screen_manager.restore_original_settings()
        
        print("✅ 屏幕管理和检测集成演示完成")
        
    except ImportError:
        print("❌ 无法导入屏幕管理器，请确保已正确安装")


def main():
    """主函数 - 运行所有示例"""
    print("屏幕状态检测功能使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        example_basic_screen_detection()
        example_detailed_screen_status()
        example_screen_status_with_conditions()
        
        # 交互式示例（需要用户操作）
        print("\n" + "=" * 50)
        user_input = input("是否运行交互式示例？(y/n): ").lower().strip()
        
        if user_input == 'y':
            example_screen_monitoring()
            example_wait_for_screen_state()
        
        # 集成示例
        example_integration_with_screen_manager()
        
        print("\n" + "=" * 50)
        print("所有示例执行完成")
        
    except KeyboardInterrupt:
        print("\n用户中断执行")
    except Exception as e:
        print(f"\n执行异常: {e}")
        log.error(f"示例执行异常: {e}")


if __name__ == "__main__":
    main()
