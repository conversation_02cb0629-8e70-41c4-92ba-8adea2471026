"""
屏幕管理使用示例
演示如何在测试中防止手机自动灭屏
"""
import sys
import os
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.screen_manager import ScreenManager
from core.logger import log


def example_basic_usage():
    """基本使用示例"""
    print("\n=== 基本使用示例 ===")
    
    screen_manager = ScreenManager()
    
    # 获取当前屏幕状态
    status = screen_manager.get_screen_status_info()
    print(f"当前屏幕超时时间: {status['screen_timeout_description']}")
    print(f"充电时保持常亮: {status['stay_on_description']}")
    
    # 设置屏幕30分钟后超时
    success = screen_manager.set_screen_timeout_minutes(30)
    print(f"设置30分钟超时: {'成功' if success else '失败'}")
    
    # 启用充电时保持屏幕常亮
    success = screen_manager.enable_stay_on_while_plugged()
    print(f"启用充电时常亮: {'成功' if success else '失败'}")


def example_testing_setup():
    """测试环境设置示例"""
    print("\n=== 测试环境设置示例 ===")
    
    screen_manager = ScreenManager()
    
    # 一键设置测试环境(30分钟超时 + 充电时常亮)
    success = screen_manager.setup_for_testing(timeout_minutes=30)
    print(f"测试环境设置: {'成功' if success else '失败'}")
    
    # 显示设置后的状态
    status = screen_manager.get_screen_status_info()
    print(f"设置后屏幕超时: {status['screen_timeout_description']}")
    print(f"设置后充电常亮: {status['stay_on_description']}")


def example_never_timeout():
    """永不超时设置示例"""
    print("\n=== 永不超时设置示例 ===")
    
    screen_manager = ScreenManager()
    
    # 设置屏幕永不超时
    success = screen_manager.set_screen_never_timeout()
    print(f"设置永不超时: {'成功' if success else '失败'}")
    
    # 验证设置
    status = screen_manager.get_screen_status_info()
    print(f"当前超时设置: {status['screen_timeout_description']}")


def example_restore_settings():
    """恢复原始设置示例"""
    print("\n=== 恢复原始设置示例 ===")
    
    screen_manager = ScreenManager()
    
    # 先进行一些设置更改
    print("进行测试设置...")
    screen_manager.setup_for_testing(timeout_minutes=60)
    
    # 等待一段时间模拟测试过程
    print("模拟测试执行中...")
    time.sleep(2)
    
    # 恢复原始设置
    print("恢复原始设置...")
    success = screen_manager.restore_original_settings()
    print(f"恢复原始设置: {'成功' if success else '失败'}")


def example_detailed_status():
    """详细状态查看示例"""
    print("\n=== 详细状态查看示例 ===")
    
    screen_manager = ScreenManager()
    
    # 获取详细状态信息
    status = screen_manager.get_screen_status_info()
    
    print("=== 屏幕状态详细信息 ===")
    print(f"屏幕超时时间: {status['screen_timeout_ms']}ms ({status['screen_timeout_description']})")
    print(f"充电时保持常亮: {status['stay_on_description']} (值: {status['stay_on_while_plugged']})")
    print(f"屏幕当前状态: {status['power_state']}")
    
    if status['is_screen_on'] is not None:
        print(f"屏幕是否点亮: {'是' if status['is_screen_on'] else '否'}")


def example_test_case_integration():
    """测试用例集成示例"""
    print("\n=== 测试用例集成示例 ===")
    
    screen_manager = ScreenManager()
    
    try:
        # 测试开始前设置
        print("测试开始 - 设置屏幕参数...")
        screen_manager.setup_for_testing(timeout_minutes=45)
        
        # 模拟测试执行
        print("执行测试用例...")
        for i in range(3):
            print(f"  执行测试步骤 {i+1}/3...")
            time.sleep(1)  # 模拟测试步骤
        
        print("测试执行完成")
        
    except Exception as e:
        print(f"测试执行异常: {e}")
        
    finally:
        # 测试结束后恢复设置
        print("测试结束 - 恢复原始设置...")
        screen_manager.restore_original_settings()


def example_custom_timeout():
    """自定义超时时间示例"""
    print("\n=== 自定义超时时间示例 ===")
    
    screen_manager = ScreenManager()
    
    # 设置不同的超时时间
    timeout_options = [
        (15000, "15秒"),
        (30000, "30秒"), 
        (60000, "1分钟"),
        (300000, "5分钟"),
        (600000, "10分钟"),
        (1800000, "30分钟")
    ]
    
    print("可用的超时时间选项:")
    for timeout_ms, description in timeout_options:
        print(f"  {description}: {timeout_ms}ms")
    
    # 设置5分钟超时
    print("\n设置5分钟超时...")
    success = screen_manager.set_screen_timeout(300000)
    print(f"设置结果: {'成功' if success else '失败'}")
    
    # 验证设置
    current_timeout = screen_manager.get_current_screen_timeout()
    if current_timeout:
        print(f"当前超时时间: {current_timeout}ms ({current_timeout/1000/60:.1f}分钟)")


def main():
    """主函数 - 运行所有示例"""
    print("屏幕管理工具使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        example_detailed_status()
        example_basic_usage()
        example_testing_setup()
        example_custom_timeout()
        example_never_timeout()
        example_test_case_integration()
        example_restore_settings()
        
        print("\n" + "=" * 50)
        print("所有示例执行完成")
        
    except KeyboardInterrupt:
        print("\n用户中断执行")
    except Exception as e:
        print(f"\n执行异常: {e}")
        log.error(f"示例执行异常: {e}")


if __name__ == "__main__":
    main()
