"""
闹钟检测器
提供闹钟相关的检测和管理功能
"""
import re
import subprocess
from typing import List, Dict, Optional
import sys
import os

# 添加父目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 导入日志模块
try:
    from core.logger import log
except ImportError:
    # 简单的日志替代
    class SimpleLog:
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def debug(self, msg): print(f"DEBUG: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
    log = SimpleLog()

# 导入工具类
try:
    from pages.base.detectors.detector_utils import DetectorUtils
except ImportError:
    # 如果导入失败，创建一个简单的替代
    class DetectorUtils:
        @staticmethod
        def execute_adb_command(command, timeout=10):
            try:
                import subprocess
                result = subprocess.run(command, capture_output=True, text=True, timeout=timeout)
                return result.returncode == 0, result.stdout
            except:
                return False, ""


class AlarmDetector:
    """闹钟检测器"""

    def __init__(self):
        self.timeout = 15

    def check_alarm_ringing(self) -> bool:
        """
        检查闹钟是否正在响铃

        Returns:
            bool: 闹钟是否正在响铃
        """
        try:
            log.info("检查闹钟是否正在响铃")

            # 检查闹钟应用是否在前台
            success, output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "dumpsys", "window", "windows", "|", "grep", "-E", "mCurrentFocus.*deskclock"],
                timeout=5
            )

            if success and output.strip():
                log.info("✅ 检测到闹钟应用在前台")
                return True

            # 检查音频焦点
            success, output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "dumpsys", "audio", "|", "grep", "-i", "alarm"],
                timeout=5
            )

            if success and 'AUDIOFOCUS_GAIN' in output:
                log.info("✅ 检测到闹钟音频焦点")
                return True

            log.info("❌ 闹钟未在响铃")
            return False

        except Exception as e:
            log.error(f"检查闹钟响铃状态失败: {e}")
            return False
    
    def check_alarm_status(self) -> bool:
        """
        检查闹钟状态 - 检查是否有实际启用的闹钟

        Returns:
            bool: 是否有启用的闹钟（不包括已禁用的闹钟）
        """
        try:
            log.info("检查闹钟状态")

            # 优先检查闹钟数据库中的启用状态
            has_enabled_alarms = self._check_alarm_database()
            if has_enabled_alarms:
                log.info("✅ 通过数据库检测到启用的闹钟")
                return True

            # 如果数据库查询失败，检查系统设置中的下一个闹钟
            has_next_alarm = self._check_next_alarm()
            if has_next_alarm:
                # 进一步验证这个下一个闹钟是否真的启用
                if self._verify_next_alarm_enabled():
                    log.info("✅ 通过系统设置检测到启用的下一个闹钟")
                    return True
                else:
                    log.info("⚠️ 系统显示有下一个闹钟，但可能未启用")

            log.info("❌ 未检测到任何启用的闹钟")
            return False

        except Exception as e:
            log.error(f"检查闹钟状态失败: {e}")
            return False

    def _check_next_alarm(self) -> bool:
        """检查系统设置中的下一个闹钟"""
        try:
            success, output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "settings", "get", "system", "next_alarm_formatted"],
                timeout=5
            )

            if success and output.strip() and output.strip() != "null":
                log.debug(f"系统下一个闹钟: {output.strip()}")
                return True

            return False
        except Exception as e:
            log.debug(f"检查系统下一个闹钟失败: {e}")
            return False

    def _verify_next_alarm_enabled(self) -> bool:
        """验证下一个闹钟是否真的启用"""
        try:
            # 获取所有闹钟并检查是否有启用的
            alarms = self.get_alarm_list()
            enabled_alarms = [a for a in alarms if a.get('enabled', False)]

            if enabled_alarms:
                log.debug(f"找到 {len(enabled_alarms)} 个启用的闹钟")
                return True

            # 如果没有找到启用的闹钟，可能是解析问题，进行额外检查
            success, output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "dumpsys", "alarm", "|", "grep", "-i", "deskclock"],
                timeout=10
            )

            if success and output.strip():
                # 检查是否有活跃的闹钟任务
                lines = output.split('\n')
                for line in lines:
                    if 'rtc_wakeup' in line.lower() and 'when=' in line.lower():
                        log.debug(f"找到活跃闹钟任务: {line[:100]}...")
                        return True

            return False
        except Exception as e:
            log.debug(f"验证下一个闹钟状态失败: {e}")
            return False

    def _check_alarm_database(self) -> bool:
        """检查闹钟数据库中是否有启用的闹钟"""
        try:
            # 检查闹钟应用的数据库
            success, output = DetectorUtils.execute_adb_command([
                "adb", "shell", "content", "query",
                "--uri", "content://com.android.deskclock/alarms",
                "--projection", "enabled,hour,minutes"
            ], timeout=10)

            if success and output.strip():
                lines = output.strip().split('\n')
                for line in lines:
                    if 'enabled=1' in line:
                        log.debug(f"找到启用的闹钟: {line}")
                        return True

            return False
        except Exception as e:
            log.debug(f"检查闹钟数据库失败: {e}")
            return False

    def _check_alarm_process(self) -> bool:
        """检查是否有活跃的闹钟进程"""
        try:
            success, output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "dumpsys", "alarm"], timeout=self.timeout
            )

            if success:
                # 检查是否有实际的闹钟任务
                lines = output.split('\n')
                for line in lines:
                    line = line.strip().lower()
                    # 查找具体的闹钟任务，而不是系统服务
                    if ('deskclock' in line and 'alarm' in line and
                        any(keyword in line for keyword in ['rtc_wakeup', 'set', 'trigger'])):
                        log.debug(f"找到活跃闹钟任务: {line[:100]}...")
                        return True

            return False
        except Exception as e:
            log.debug(f"检查闹钟进程失败: {e}")
            return False
    
    def get_alarm_list(self) -> List[Dict[str, any]]:
        """
        获取闹钟列表

        Returns:
            List[Dict]: 闹钟列表
        """
        try:
            log.info("获取闹钟列表")
            alarms = []

            # 方法1: 直接查询闹钟数据库
            alarms.extend(self._get_alarms_from_database())

            # 方法2: 如果数据库查询失败，尝试解析dumpsys输出
            if not alarms:
                success, alarm_output = DetectorUtils.execute_adb_command(
                    ["adb", "shell", "dumpsys", "alarm"], timeout=self.timeout
                )

                if success:
                    alarms.extend(self._parse_alarm_database_output(alarm_output))

            # 去重并排序
            unique_alarms = []
            seen_times = set()
            for alarm in alarms:
                time_key = f"{alarm.get('hour', 0)}:{alarm.get('minute', 0)}"
                if time_key not in seen_times:
                    seen_times.add(time_key)
                    unique_alarms.append(alarm)

            log.info(f"找到 {len(unique_alarms)} 个闹钟")
            return unique_alarms

        except Exception as e:
            log.error(f"获取闹钟列表失败: {e}")
            return []

    def _get_alarms_from_database(self) -> List[Dict[str, any]]:
        """从闹钟数据库直接获取闹钟列表"""
        alarms = []
        try:
            success, output = DetectorUtils.execute_adb_command([
                "adb", "shell", "content", "query",
                "--uri", "content://com.android.deskclock/alarms",
                "--projection", "_id,hour,minutes,enabled,label"
            ], timeout=10)

            if success and output.strip():
                lines = output.strip().split('\n')
                for line in lines:
                    if 'Row:' in line:
                        alarm_data = self._parse_alarm_row(line)
                        if alarm_data:
                            alarms.append(alarm_data)

        except Exception as e:
            log.debug(f"从数据库获取闹钟失败: {e}")

        return alarms

    def _parse_alarm_row(self, row_line: str) -> Optional[Dict[str, any]]:
        """解析闹钟数据库行"""
        try:
            # 解析格式: Row: 0 _id=1, hour=8, minutes=30, enabled=1, label=起床
            alarm_data = {}

            # 提取各个字段
            import re
            patterns = {
                'id': r'_id=(\d+)',
                'hour': r'hour=(\d+)',
                'minute': r'minutes=(\d+)',
                'enabled': r'enabled=([01])',
                'label': r'label=([^,]*)'
            }

            for field, pattern in patterns.items():
                match = re.search(pattern, row_line)
                if match:
                    value = match.group(1)
                    if field in ['id', 'hour', 'minute']:
                        alarm_data[field] = int(value)
                    elif field == 'enabled':
                        alarm_data[field] = bool(int(value))
                    else:
                        alarm_data[field] = value.strip()

            # 只返回有效的闹钟数据
            if 'hour' in alarm_data and 'minute' in alarm_data:
                alarm_data['time'] = f"{alarm_data['hour']:02d}:{alarm_data['minute']:02d}"
                alarm_data['source'] = 'database'
                return alarm_data

        except Exception as e:
            log.debug(f"解析闹钟行失败: {e}")

        return None
    
    def _parse_alarm_database_output(self, output: str) -> List[Dict[str, any]]:
        """
        解析闹钟数据库输出
        
        Args:
            output: 数据库输出内容
            
        Returns:
            List[Dict]: 解析出的闹钟列表
        """
        alarms = []
        try:
            lines = output.split('\n')
            for line in lines:
                line = line.strip()
                # 查找包含时间信息的行
                if any(keyword in line.lower() for keyword in ['alarm', 'clock', 'time']):
                    # 简单的时间模式匹配
                    time_pattern = r'\b([0-1]?[0-9]|2[0-3]):[0-5][0-9]\b'
                    matches = re.findall(time_pattern, line)
                    for match in matches:
                        if match not in [alarm.get('time') for alarm in alarms]:
                            alarms.append({
                                'time': match,
                                'enabled': 'enabled' in line.lower() or 'on' in line.lower(),
                                'source': 'dumpsys'
                            })
        except Exception as e:
            log.debug(f"解析闹钟输出失败: {e}")
        
        return alarms
    
    def clear_all_alarms(self) -> bool:
        """
        清除所有闹钟
        
        Returns:
            bool: 是否成功清除
        """
        try:
            log.info("清除所有闹钟")
            
            # 通过ADB命令清除闹钟
            commands = [
                ["adb", "shell", "am", "broadcast", "-a", "android.intent.action.ALARM_CHANGED"],
                ["adb", "shell", "settings", "put", "system", "alarm_alert", ""],
            ]
            
            success_count = 0
            for cmd in commands:
                try:
                    success, _ = DetectorUtils.execute_adb_command(cmd, timeout=10)
                    if success:
                        success_count += 1
                except Exception as e:
                    log.debug(f"执行清除命令失败: {e}")
            
            success = success_count > 0
            log.info(f"闹钟清除{'成功' if success else '失败'}")
            return success
            
        except Exception as e:
            log.error(f"清除闹钟失败: {e}")
            return False
    
    def set_alarm(self, hour: int, minute: int, enabled: bool = True) -> bool:
        """
        设置闹钟
        
        Args:
            hour: 小时 (0-23)
            minute: 分钟 (0-59)
            enabled: 是否启用
            
        Returns:
            bool: 是否设置成功
        """
        try:
            if not (0 <= hour <= 23) or not (0 <= minute <= 59):
                log.error("无效的时间参数")
                return False
            
            log.info(f"设置闹钟: {hour:02d}:{minute:02d}")
            
            # 使用Intent启动闹钟设置
            success, _ = DetectorUtils.execute_adb_command([
                "adb", "shell", "am", "start",
                "-a", "android.intent.action.SET_ALARM",
                "--ei", "android.intent.extra.alarm.HOUR", str(hour),
                "--ei", "android.intent.extra.alarm.MINUTES", str(minute),
                "--ez", "android.intent.extra.alarm.SKIP_UI", "true"
            ], timeout=10)
            
            if success:
                log.info(f"闹钟设置成功: {hour:02d}:{minute:02d}")
            else:
                log.error("闹钟设置失败")
            
            return success
            
        except Exception as e:
            log.error(f"设置闹钟失败: {e}")
            return False
    
    def get_next_alarm_info(self) -> Optional[Dict[str, any]]:
        """
        获取下一个闹钟信息
        
        Returns:
            Optional[Dict]: 下一个闹钟信息，如果没有则返回None
        """
        try:
            success, output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "settings", "get", "system", "next_alarm_formatted"], 
                timeout=5
            )
            
            if success and output.strip():
                return {
                    'formatted_time': output.strip(),
                    'source': 'system_settings'
                }
            
            # 备用方法：从闹钟列表中找到最近的
            alarms = self.get_alarm_list()
            if alarms:
                # 简单返回第一个找到的闹钟
                return alarms[0]
            
            return None
            
        except Exception as e:
            log.debug(f"获取下一个闹钟信息失败: {e}")
            return None


if __name__ == '__main__':
    detector = AlarmDetector()
    print(detector.check_alarm_ringing())
    # print(detector.get_alarm_list())
    # print(detector.clear_all_alarms())
    # print(detector.set_alarm(8, 30))
    # print(detector.get_next_alarm_info())
