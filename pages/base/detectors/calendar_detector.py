"""
Calendar检测器
Calendar应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class CalendarDetector(BaseAppDetector):
    """Calendar应用检测器"""

    def __init__(self):
        super().__init__(AppType.CALENDAR)

    def get_package_names(self) -> List[str]:
        """获取Calendar应用包名列表"""
        return ['com.transsion.calendar', 'com.android.calendar', 'com.sh.smart.calendar']

    def get_keywords(self) -> List[str]:
        """获取Calendar应用关键词列表"""
        return ['日历', 'calendar']
