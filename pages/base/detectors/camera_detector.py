"""
Camera Detector检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class CameraDetector(BaseAppDetector):
    """相机应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.CAMERA)
    
    def get_package_names(self) -> List[str]:
        """获取相机应用包名列表"""
        return [
            "com.android.camera",
            "com.google.android.GoogleCamera",
            "com.transsion.camera",
            "com.sec.android.app.camera",
            "com.huawei.camera",
            "com.xiaomi.camera",
            "com.oppo.camera",
            "com.vivo.camera",
        ]
    
    def get_keywords(self) -> List[str]:
        """获取相机应用关键词列表"""
        return ["camera", "cam", "photo", "相机", "拍照"]
