"""
Calendar检测器
Calendar应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class CarlcareDetector(BaseAppDetector):
    """Carlcare应用检测器"""

    def __init__(self):
        super().__init__(AppType.CALENDAR)

    def get_package_names(self) -> List[str]:
        """获取Calendar应用包名列表"""
        return ['com.transsion.carlcare']

    def get_keywords(self) -> List[str]:
        """获取carlcare应用关键词列表"""
        return ['carlcare']
