"""
Childmode检测器
Childmode应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class ChildmodeDetector(BaseAppDetector):
    """Childmode应用检测器"""

    def __init__(self):
        super().__init__(AppType.CHILDMODE)

    def get_package_names(self) -> List[str]:
        """获取Childmode应用包名列表"""
        return ['com.transsion.childmode', 'com.android.childmode', 'com.sh.smart.childmode']

    def get_keywords(self) -> List[str]:
        """获取Childmode应用关键词列表"""
        return ['儿童模式', 'childmode']
