"""
Contacts Detector检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class ContactsDetector(BaseAppDetector):
    """联系人应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.CONTACTS)
    
    def get_package_names(self) -> List[str]:
        """获取联系人应用包名列表"""
        return [
            "com.sh.smart.caller",
            "com.android.contacts",
            "com.google.android.contacts",
            "com.samsung.android.app.contacts",
            "com.huawei.contacts",
            "com.xiaomi.contacts",
            "com.oppo.contacts",
            "com.vivo.contacts",
            "com.android.dialer",
            "com.android.incallui"
        ]
    
    def get_keywords(self) -> List[str]:
        """获取联系人应用关键词列表"""
        return ["contacts", "contact", "people", "dialer", "联系人", "通讯录"]
