"""
检测器工具类
提供通用的检测功能和工具方法
"""
import subprocess
import re
import sys
import os
from typing import List, Dict, Optional, Tuple

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
project_root = os.path.dirname(parent_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入日志模块
try:
    from core.logger import log
except ImportError:
    # 简单的日志替代
    class SimpleLog:
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def debug(self, msg): print(f"DEBUG: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
    log = SimpleLog()


class DetectorUtils:
    """检测器工具类"""
    
    @staticmethod
    def execute_adb_command(command: List[str], timeout: int = 10) -> Tuple[bool, str]:
        """
        执行ADB命令
        
        Args:
            command: ADB命令列表
            timeout: 超时时间
            
        Returns:
            Tuple[bool, str]: (是否成功, 输出内容)
        """
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding='utf-8',
                errors='ignore'
            )
            
            success = result.returncode == 0
            output = result.stdout if success else result.stderr
            return success, output
            
        except subprocess.TimeoutExpired:
            log.warning(f"ADB命令超时: {' '.join(command)}")
            return False, "命令超时"
        except Exception as e:
            log.error(f"执行ADB命令失败: {e}")
            return False, str(e)
    
    @staticmethod
    def check_package_has_running_process(package_name: str) -> bool:
        """
        检查指定包名是否有运行中的进程
        
        Args:
            package_name: 应用包名
            
        Returns:
            bool: 是否有运行中的进程
        """
        try:
            # 方法1: 使用ps命令
            success, ps_output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "ps"], timeout=5
            )
            
            if success:
                lines = ps_output.split('\n')
                for line in lines:
                    if package_name in line and line.strip():
                        parts = line.split()
                        if len(parts) >= 8 and parts[-1] == package_name:
                            return True
                        elif package_name in line and any(status in line for status in ['S', 'R', 'D']):
                            return True
            
            # 方法2: 使用pidof命令
            success, pidof_output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "pidof", package_name], timeout=5
            )
            
            if success and pidof_output.strip():
                pid = pidof_output.strip()
                # 验证PID是否真实存在
                success, _ = DetectorUtils.execute_adb_command(
                    ["adb", "shell", "ls", f"/proc/{pid}"], timeout=3
                )
                return success
            
            return False
            
        except Exception as e:
            log.debug(f"检查包名运行进程失败: {e}")
            return False
    
    @staticmethod
    def verify_app_in_foreground(package_name: str) -> bool:
        """
        验证应用是否在前台
        
        Args:
            package_name: 应用包名
            
        Returns:
            bool: 应用是否在前台
        """
        try:
            # 方法1: 检查当前焦点窗口
            success, window_output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "dumpsys", "window", "windows"], timeout=5
            )
            
            if success:
                lines = window_output.split('\n')
                for line in lines:
                    if "mCurrentFocus=" in line:
                        if package_name in line and "null" not in line:
                            return True
                        break
            
            # 方法2: 检查应用的Activity状态
            success, package_output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "dumpsys", "activity", "package", package_name], timeout=5
            )
            
            if success and "state=RESUMED" in package_output:
                return True
            
            # 方法3: 检查最近任务的可见性
            success, recents_output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "dumpsys", "activity", "recents"], timeout=5
            )
            
            if success:
                lines = recents_output.split('\n')
                in_target_task = False
                for line in lines:
                    if package_name in line and "Task{" in line:
                        in_target_task = True
                    elif in_target_task and "visible=true" in line:
                        return True
                    elif in_target_task and "Task{" in line and package_name not in line:
                        in_target_task = False
            
            return False
            
        except Exception as e:
            log.debug(f"验证前台应用失败: {e}")
            return False
    
    @staticmethod
    def find_available_apps(app_keywords: List[str]) -> List[str]:
        """
        查找可用的特定类型应用
        
        Args:
            app_keywords: 应用关键词列表
            
        Returns:
            List[str]: 可用应用包名列表
        """
        try:
            success, packages_output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "pm", "list", "packages"], timeout=30
            )
            
            if not success:
                return []
            
            packages = packages_output.strip().split('\n')
            found_apps = []
            
            for package_line in packages:
                if package_line.startswith('package:'):
                    package_name = package_line.replace('package:', '')
                    
                    # 检查是否包含相关关键词
                    for keyword in app_keywords:
                        if keyword.lower() in package_name.lower():
                            found_apps.append(package_name)
                            break
            
            return found_apps
            
        except Exception as e:
            log.error(f"查找应用失败: {e}")
            return []
    
    @staticmethod
    def get_app_version(package_name: str) -> Optional[str]:
        """
        获取应用版本信息
        
        Args:
            package_name: 应用包名
            
        Returns:
            Optional[str]: 应用版本，如果获取失败则返回None
        """
        try:
            success, output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "dumpsys", "package", package_name], timeout=10
            )
            
            if success:
                # 查找版本信息
                version_pattern = r'versionName=([^\s]+)'
                match = re.search(version_pattern, output)
                if match:
                    return match.group(1)
            
            return None
            
        except Exception as e:
            log.debug(f"获取应用版本失败: {e}")
            return None
    
    @staticmethod
    def check_app_permissions(package_name: str, permission: str) -> bool:
        """
        检查应用权限状态
        
        Args:
            package_name: 应用包名
            permission: 权限名称
            
        Returns:
            bool: 是否有该权限
        """
        try:
            success, output = DetectorUtils.execute_adb_command(
                ["adb", "shell", "dumpsys", "package", package_name], timeout=10
            )
            
            if success:
                # 查找权限信息
                permission_indicators = [
                    f"{permission}",
                    f"{permission}: granted=true",
                    f"{permission.split('.')[-1]} permission"
                ]
                
                for indicator in permission_indicators:
                    if indicator in output:
                        return True
            
            return False
            
        except Exception as e:
            log.error(f"检查应用权限失败: {e}")
            return False
    
    @staticmethod
    def get_device_info() -> Dict[str, str]:
        """
        获取设备信息
        
        Returns:
            Dict[str, str]: 设备信息字典
        """
        device_info = {}
        
        try:
            # 获取设备型号
            success, model = DetectorUtils.execute_adb_command(
                ["adb", "shell", "getprop", "ro.product.model"], timeout=5
            )
            if success:
                device_info['model'] = model.strip()
            
            # 获取Android版本
            success, version = DetectorUtils.execute_adb_command(
                ["adb", "shell", "getprop", "ro.build.version.release"], timeout=5
            )
            if success:
                device_info['android_version'] = version.strip()
            
            # 获取设备品牌
            success, brand = DetectorUtils.execute_adb_command(
                ["adb", "shell", "getprop", "ro.product.brand"], timeout=5
            )
            if success:
                device_info['brand'] = brand.strip()
            
        except Exception as e:
            log.debug(f"获取设备信息失败: {e}")
        
        return device_info
