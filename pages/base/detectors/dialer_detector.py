"""
Dialer检测器
拨号盘应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class DialerDetector(BaseAppDetector):
    """拨号盘应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.DIALER)
    
    def get_package_names(self) -> List[str]:
        """获取拨号盘应用包名列表"""
        return [
            "com.sh.smart.caller",
            "com.android.dialer",
            "com.google.android.dialer",
            "com.samsung.android.dialer",
            "com.huawei.contacts",
            "com.xiaomi.contacts",
            "com.oppo.contacts",
            "com.vivo.contacts",
            "com.transsion.dialer",
        ]
    
    def get_keywords(self) -> List[str]:
        """获取拨号盘应用关键词列表"""
        return ["dialer", "call", "phone", "拨号", "通话", "电话"]
