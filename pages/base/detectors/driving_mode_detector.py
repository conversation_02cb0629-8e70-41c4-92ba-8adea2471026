"""
Driving Mode检测器
Driving Mode应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class DrivingModeDetector(BaseAppDetector):
    """Driving Mode应用检测器"""

    def __init__(self):
        super().__init__(AppType.DRIVING_MODE)

    def get_package_names(self) -> List[str]:
        """获取Driving Mode应用包名列表"""
        return ['com.transsion.drivingmode', 'com.android.drivingmode', 'com.sh.smart.drivingmode']

    def get_keywords(self) -> List[str]:
        """获取Driving Mode应用关键词列表"""
        return ['驾驶模式', 'driving mode', 'drivingmode', '支持ad10']
