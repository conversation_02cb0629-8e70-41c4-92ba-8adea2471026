"""
DynamicBar检测器
DynamicBar应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class DynamicBarDetector(BaseAppDetector):
    """DynamicBar应用检测器"""

    def __init__(self):
        super().__init__(AppType.DYNAMIC_BAR)

    def get_package_names(self) -> List[str]:
        """获取DynamicBar应用包名列表"""
        return ['com.transsion.dynamicbar', 'com.android.dynamicbar', 'com.sh.smart.dynamicbar']

    def get_keywords(self) -> List[str]:
        """获取DynamicBar应用关键词列表"""
        return ['灵动岛', 'dynamicbar']
