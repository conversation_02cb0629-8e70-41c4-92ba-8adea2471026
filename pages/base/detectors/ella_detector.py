"""
Ella检测器
智能助手Ella应用检测器 - 优化版本，区分前台应用和后台服务
"""
from typing import List
import subprocess
from pages.base.app_detector import BaseAppDetector, AppType
from core.logger import log


class EllaDetector(BaseAppDetector):
    """Ella智能助手检测器 - 优化版本"""

    def __init__(self):
        super().__init__(AppType.ELLA)

    def get_package_names(self) -> List[str]:
        """获取Ella应用包名列表"""
        return [
            "com.transsion.aivoiceassistant"
        ]

    def get_keywords(self) -> List[str]:
        """获取Ella应用关键词列表"""
        return ["ella",'floax','aivana', "assistant", "voice", "智能助手", "语音助手"]

    def check_app_opened(self) -> bool:
        """
        重写整个检测方法 - 针对Ella应用的特殊优化
        Ella应用有多个后台保活服务，需要区分真正的前台应用和后台服务
        """
        try:
            log.info(f"检查{self.app_type.value}应用状态（优化版本）")

            packages = self.get_package_names()

            for package in packages:
                # 1. 首先检查是否有真正的前台Activity
                if self._check_ella_foreground_activity(package):
                    log.info(f"✅ 检测到{self.app_type.value}前台Activity: {package}")
                    return True

                # 2. 检查是否有用户可见的UI界面
                if self._check_ella_visible_ui(package):
                    log.info(f"✅ 检测到{self.app_type.value}可见UI: {package}")
                    return True

                # 3. 检查焦点窗口（更严格）
                if self._check_ella_focus_window(package):
                    log.info(f"✅ 检测到{self.app_type.value}焦点窗口: {package}")
                    return True

            # 如果只有后台服务，不认为应用已启动
            log.info(f"❌ {self.app_type.value}仅有后台服务运行，不算应用启动")
            return False

        except Exception as e:
            log.error(f"检查{self.app_type.value}应用失败: {e}")
            return False

    def _check_process_status(self) -> bool:
        """
        重写进程检测方法 - 针对Ella应用的特殊优化
        这个方法现在被check_app_opened替代，但保留以防父类调用
        """
        return False  # 强制返回False，让主检测方法处理

    def _check_ella_foreground_activity(self, package: str) -> bool:
        """检查Ella是否有真正的前台Activity"""
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                output = result.stdout
                lines = output.split('\n')

                # 查找RESUMED状态的Activity
                for i, line in enumerate(lines):
                    if package in line and "ActivityRecord{" in line:
                        # 检查后续几行是否有RESUMED状态
                        for j in range(i, min(i + 10, len(lines))):
                            if "state=RESUMED" in lines[j]:
                                # 进一步验证这不是后台服务
                                activity_line = line
                                if not self._is_background_service_activity(activity_line):
                                    log.debug(f"找到RESUMED状态的前台Activity: {activity_line}")
                                    return True
                                else:
                                    log.debug(f"跳过后台服务Activity: {activity_line}")

            return False

        except Exception as e:
            log.debug(f"检查Ella前台Activity失败: {e}")
            return False

    def _check_ella_visible_ui(self, package: str) -> bool:
        """检查Ella是否有用户可见的UI界面"""
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                output = result.stdout
                lines = output.split('\n')

                for line in lines:
                    if package in line:
                        # 检查是否是用户可见的窗口
                        if any(keyword in line for keyword in [
                            "mCurrentFocus=", "TYPE_APPLICATION", "mVisible=true"
                        ]):
                            # 排除后台服务窗口
                            if not self._is_background_service_window(line):
                                log.debug(f"找到可见UI窗口: {line}")
                                return True
                            else:
                                log.debug(f"跳过后台服务窗口: {line}")

            return False

        except Exception as e:
            log.debug(f"检查Ella可见UI失败: {e}")
            return False

    def _check_ella_focus_window(self, package: str) -> bool:
        """检查Ella是否有焦点窗口（更严格的检查）"""
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                output = result.stdout
                lines = output.split('\n')

                for line in lines:
                    if "mCurrentFocus=" in line and package in line and "null" not in line:
                        # 排除后台服务窗口
                        if not self._is_background_service_window(line):
                            log.debug(f"找到焦点窗口: {line}")
                            return True
                        else:
                            log.debug(f"跳过后台服务焦点窗口: {line}")

            return False

        except Exception as e:
            log.debug(f"检查Ella焦点窗口失败: {e}")
            return False

    def _is_background_service_activity(self, activity_line: str) -> bool:
        """判断Activity是否为后台服务"""
        # Ella的后台服务Activity特征
        background_indicators = [
            "ZsService",  # 零屏服务
            "VoiceInteractionService",  # 语音交互服务
            ":zs",  # 零屏进程
            ":voiceinteractionservice",  # 语音交互服务进程
            "service",  # 通用服务标识
        ]

        activity_line_lower = activity_line.lower()
        return any(indicator.lower() in activity_line_lower for indicator in background_indicators)

    def _is_background_service_window(self, window_line: str) -> bool:
        """判断窗口是否为后台服务窗口"""
        # Ella的后台服务窗口特征
        background_indicators = [
            "ZsService",  # 零屏服务
            "VoiceInteractionService",  # 语音交互服务
            "TYPE_SYSTEM_OVERLAY",  # 系统覆盖层
            "TYPE_SYSTEM_ALERT",  # 系统警告
        ]

        window_line_lower = window_line.lower()
        return any(indicator.lower() in window_line_lower for indicator in background_indicators)
