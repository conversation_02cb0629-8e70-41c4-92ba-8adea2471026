"""
Facebook Detector检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class FacebookDetector(BaseAppDetector):
    """Facebook应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.FACEBOOK)
    
    def get_package_names(self) -> List[str]:
        """获取Facebook应用包名列表"""
        return [
            "com.facebook.katana",
            "com.facebook.orca",
            "com.facebook.lite",
            "com.facebook.mlite",
            "com.facebook.pages.app",
            "com.facebook.work",
        ]
    
    def get_keywords(self) -> List[str]:
        """获取Facebook应用关键词列表"""
        return ["facebook", "fb", "messenger"]
