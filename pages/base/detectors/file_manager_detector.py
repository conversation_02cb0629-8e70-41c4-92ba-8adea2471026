"""
FileManager检测器
文件管理器应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class FileManagerDetector(BaseAppDetector):
    """文件管理器应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.FILE_MANAGER)
    
    def get_package_names(self) -> List[str]:
        """获取文件管理器应用包名列表"""
        return [
            "com.android.documentsui",
            "com.google.android.documentsui",
            "com.transsion.filemanager",
            "com.android.filemanager",
            "com.samsung.android.app.files",
            "com.huawei.hidisk",
            "com.xiaomi.fileexplorer",
            "com.oppo.filemanager",
            "com.vivo.filemanager",
            "com.es.android.filemanager",
        ]
    
    def get_keywords(self) -> List[str]:
        """获取文件管理器应用关键词列表"""
        return ["filemanager", "files", "documents", "explorer", "文件管理", "文件浏览器"]
