"""
Finder检测器
Finder应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class FinderDetector(BaseAppDetector):
    """Finder应用检测器"""

    def __init__(self):
        super().__init__(AppType.FINDER)

    def get_package_names(self) -> List[str]:
        """获取Finder应用包名列表"""
        return ['com.google.android.gms', 'com.transsion.finder', 'com.android.finder', 'com.sh.smart.finder']

    def get_keywords(self) -> List[str]:
        """获取Finder应用关键词列表"""
        return ['查找设备', 'finder', '查找我的设备', 'googleapps']
