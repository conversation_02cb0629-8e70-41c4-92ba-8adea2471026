"""
FlipExternalScreen检测器
FlipExternalScreen应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class FlipExternalScreenDetector(BaseAppDetector):
    """FlipExternalScreen应用检测器"""

    def __init__(self):
        super().__init__(AppType.FLIP_EXTERNAL_SCREEN)

    def get_package_names(self) -> List[str]:
        """获取FlipExternalScreen应用包名列表"""
        return ['com.transsion.subsystemui', 'com.transsion.flipexternalscreen', 'com.android.flipexternalscreen', 'com.sh.smart.flipexternalscreen']

    def get_keywords(self) -> List[str]:
        """获取FlipExternalScreen应用关键词列表"""
        return ['小折叠外屏', 'flipexternalscreen', 'subsystemui']
