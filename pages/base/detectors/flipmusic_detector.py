"""
Flipmusic检测器
Flipmusic应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class FlipmusicDetector(BaseAppDetector):
    """Flipmusic应用检测器"""

    def __init__(self):
        super().__init__(AppType.FLIPMUSIC)

    def get_package_names(self) -> List[str]:
        """获取Flipmusic应用包名列表"""
        return ['com.transsion.flipmusic', 'com.android.flipmusic', 'com.sh.smart.flipmusic']

    def get_keywords(self) -> List[str]:
        """获取Flipmusic应用关键词列表"""
        return ['flip音乐播放器', 'flipmusic']
