"""
Folding Screen Zone检测器
Folding Screen Zone应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class FoldingScreenZoneDetector(BaseAppDetector):
    """Folding Screen Zone应用检测器"""

    def __init__(self):
        super().__init__(AppType.FOLDING_SCREEN_ZONE)

    def get_package_names(self) -> List[str]:
        """获取Folding Screen Zone应用包名列表"""
        return ['com.android.systemui', 'com.transsion.foldingscreenzone', 'com.android.foldingscreenzone', 'com.sh.smart.foldingscreenzone']

    def get_keywords(self) -> List[str]:
        """获取Folding Screen Zone应用关键词列表"""
        return ['平行视界', 'folding screen zone', 'foldingscreenzone', '折叠屏专区', 'works with', 'workswith', 'ams/wms、main screen display', 'ams/wms、mainscreendisplay']
