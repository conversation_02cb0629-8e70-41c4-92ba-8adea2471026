"""
GlobalSearch检测器
GlobalSearch应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class GlobalSearchDetector(BaseAppDetector):
    """GlobalSearch应用检测器"""

    def __init__(self):
        super().__init__(AppType.GLOBAL_SEARCH)

    def get_package_names(self) -> List[str]:
        """获取GlobalSearch应用包名列表"""
        return ['com.transsion.hilauncher', 'com.transsion.globalsearch', 'com.android.globalsearch', 'com.sh.smart.globalsearch']

    def get_keywords(self) -> List[str]:
        """获取GlobalSearch应用关键词列表"""
        return ['globalsearch', '全局搜索-本地化内容']
