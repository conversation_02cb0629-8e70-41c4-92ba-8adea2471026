"""
GlobalTheme检测器
GlobalTheme应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class GlobalThemeDetector(BaseAppDetector):
    """GlobalTheme应用检测器"""

    def __init__(self):
        super().__init__(AppType.GLOBAL_THEME)

    def get_package_names(self) -> List[str]:
        """获取GlobalTheme应用包名列表"""
        return ['com.transsion.theme', 'com.transsion.globaltheme', 'com.android.globaltheme', 'com.sh.smart.globaltheme']

    def get_keywords(self) -> List[str]:
        """获取GlobalTheme应用关键词列表"""
        return ['全局主题', 'globaltheme', 'theme']
