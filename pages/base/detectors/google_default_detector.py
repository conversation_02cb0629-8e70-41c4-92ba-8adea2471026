"""
Google Default检测器
Google Default应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class GoogleDefaultDetector(BaseAppDetector):
    """Google Default应用检测器"""

    def __init__(self):
        super().__init__(AppType.GOOGLE_DEFAULT)

    def get_package_names(self) -> List[str]:
        """获取Google Default应用包名列表"""
        return ['com.google.android.apps.restore', 'com.transsion.googledefault', 'com.android.googledefault', 'com.sh.smart.googledefault']

    def get_keywords(self) -> List[str]:
        """获取Google Default应用关键词列表"""
        return ['google default', 'googledefault', '原生设置', '备份与恢复', 'backupand restore', 'backupandrestore', 'google apps', 'googleapps']
