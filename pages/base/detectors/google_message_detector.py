"""
GoogleMessage检测器
GoogleMessage应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class GoogleMessageDetector(BaseAppDetector):
    """GoogleMessage应用检测器"""

    def __init__(self):
        super().__init__(AppType.GOOGLE_MESSAGE)

    def get_package_names(self) -> List[str]:
        """获取GoogleMessage应用包名列表"""
        return ['com.google.android.apps.messaging', 'com.transsion.googlemessage', 'com.android.googlemessage', 'com.sh.smart.googlemessage']

    def get_keywords(self) -> List[str]:
        """获取GoogleMessage应用关键词列表"""
        return ['google短信', 'googlemessage', '短信', 'googleapps']
