"""
GooglePhone检测器
GooglePhone应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class GooglePhoneDetector(BaseAppDetector):
    """GooglePhone应用检测器"""

    def __init__(self):
        super().__init__(AppType.GOOGLE_PHONE)

    def get_package_names(self) -> List[str]:
        """获取GooglePhone应用包名列表"""
        return ['com.google.android.dialer', 'com.transsion.googlephone', 'com.android.googlephone', 'com.sh.smart.googlephone']

    def get_keywords(self) -> List[str]:
        """获取GooglePhone应用关键词列表"""
        return ['google拨号盘', 'googlephone', '拨号盘', 'googleapps']
