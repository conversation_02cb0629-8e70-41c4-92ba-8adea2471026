"""
healthlife检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class HealthlifeDetector(BaseAppDetector):
    """healthlife检测器应用检测器"""

    def __init__(self):
        super().__init__(AppType.HEADSET_CONTROL)

    def get_package_names(self) -> List[str]:
        """获取healthlife应用包名列表"""
        return ['com.transsion.healthlife']

    def get_keywords(self) -> List[str]:
        """获取healthlife应用关键词列表"""
        return ['healthlife', 'my health']
