"""
Launcher检测器
桌面启动器应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class LauncherDetector(BaseAppDetector):
    """桌面启动器应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.LAUNCHER)
    
    def get_package_names(self) -> List[str]:
        """获取桌面启动器应用包名列表"""
        return [
            "com.transsion.hilauncher",
            "com.android.launcher3",
            "com.google.android.apps.nexuslauncher",
            "com.samsung.android.app.launcher",
            "com.huawei.android.launcher",
            "com.xiaomi.launcher",
            "com.oppo.launcher",
            "com.vivo.launcher",
            "com.transsion.launcher",
        ]
    
    def get_keywords(self) -> List[str]:
        """获取桌面启动器应用关键词列表"""
        return ["launcher", "home", "desktop", "桌面", "启动器", "主屏幕"]
