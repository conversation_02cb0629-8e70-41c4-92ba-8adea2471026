"""
Leathercase检测器
Leathercase应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class LeathercaseDetector(BaseAppDetector):
    """Leathercase应用检测器"""

    def __init__(self):
        super().__init__(AppType.LEATHERCASE)

    def get_package_names(self) -> List[str]:
        """获取Leathercase应用包名列表"""
        return ['com.transsion.tranhall', 'com.transsion.leathercase', 'com.android.leathercase', 'com.sh.smart.leathercase']

    def get_keywords(self) -> List[str]:
        """获取Leathercase应用关键词列表"""
        return ['皮套模式', 'leathercase']
