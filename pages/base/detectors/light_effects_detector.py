"""
Light Effects检测器
Light Effects应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class LightEffectsDetector(BaseAppDetector):
    """Light Effects应用检测器"""

    def __init__(self):
        super().__init__(AppType.LIGHT_EFFECTS)

    def get_package_names(self) -> List[str]:
        """获取Light Effects应用包名列表"""
        return ['com.android.settings', 'com.transsion.lighteffects', 'com.android.lighteffects', 'com.sh.smart.lighteffects']

    def get_keywords(self) -> List[str]:
        """获取Light Effects应用关键词列表"""
        return ['灯效', 'light effects', 'lighteffects', '指示灯', 'led']
