"""
MagazineService检测器
MagazineService应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class MagazineServiceDetector(BaseAppDetector):
    """MagazineService应用检测器"""

    def __init__(self):
        super().__init__(AppType.MAGAZINE_SERVICE)

    def get_package_names(self) -> List[str]:
        """获取MagazineService应用包名列表"""
        return ['com.transsion.magazineservice', 'com.android.magazineservice', 'com.sh.smart.magazineservice']

    def get_keywords(self) -> List[str]:
        """获取MagazineService应用关键词列表"""
        return ['杂志锁屏', 'magazineservice']
