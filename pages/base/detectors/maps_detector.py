"""
Maps Detector检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class MapsDetector(BaseAppDetector):
    """地图应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.MAPS)
    
    def get_package_names(self) -> List[str]:
        """获取地图应用包名列表"""
        return [
            "com.google.android.apps.maps",
            "com.baidu.BaiduMap",
            "com.autonavi.minimap",
            "com.tencent.map",
            "com.sogou.map.android.maps",
        ]
    
    def get_keywords(self) -> List[str]:
        """获取地图应用关键词列表"""
        return ["maps", "map", "navigation", "地图", "导航"]
