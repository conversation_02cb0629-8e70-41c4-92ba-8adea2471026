"""
MOL检测器
MOL应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class MolDetector(BaseAppDetector):
    """MOL应用检测器"""

    def __init__(self):
        super().__init__(AppType.MOL)

    def get_package_names(self) -> List[str]:
        """获取MOL应用包名列表"""
        return ['com.transsion.mol', 'com.android.mol', 'com.sh.smart.mol']

    def get_keywords(self) -> List[str]:
        """获取MOL应用关键词列表"""
        return ['ella翻译', 'mol', '语言大师']
