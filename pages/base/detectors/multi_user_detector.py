"""
Multi User检测器
Multi User应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class MultiUserDetector(BaseAppDetector):
    """Multi User应用检测器"""

    def __init__(self):
        super().__init__(AppType.MULTI_USER)

    def get_package_names(self) -> List[str]:
        """获取Multi User应用包名列表"""
        return ['com.transsion.multiuser', 'com.android.multiuser', 'com.sh.smart.multiuser']

    def get_keywords(self) -> List[str]:
        """获取Multi User应用关键词列表"""
        return ['多用户', 'multi user', 'multiuser', 'settings']
