"""
One-Tap Button检测器
One-Tap Button应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class OneTapButtonDetector(BaseAppDetector):
    """One-Tap Button应用检测器"""

    def __init__(self):
        super().__init__(AppType.ONE_TAP_BUTTON)

    def get_package_names(self) -> List[str]:
        """获取One-Tap Button应用包名列表"""
        return ['com.transsion.settings.flexbutton', 'com.transsion.onetapbutton', 'com.android.onetapbutton', 'com.sh.smart.onetapbutton']

    def get_keywords(self) -> List[str]:
        """获取One-Tap Button应用关键词列表"""
        return ['一键效率', 'one-tap button', 'one-tapbutton', 'onetapbutton']
