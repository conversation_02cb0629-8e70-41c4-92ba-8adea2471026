"""
OOBE检测器
OOBE应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class OobeDetector(BaseAppDetector):
    """OOBE应用检测器"""

    def __init__(self):
        super().__init__(AppType.OOBE)

    def get_package_names(self) -> List[str]:
        """获取OOBE应用包名列表"""
        return ['com.transsion.overlaysuw', 'com.transsion.oobe', 'com.android.oobe', 'com.sh.smart.oobe']

    def get_keywords(self) -> List[str]:
        """获取OOBE应用关键词列表"""
        return ['开机向导', 'oobe']
