"""
OSSettingsExt检测器
OSSettingsExt应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class OsSettingsExtDetector(BaseAppDetector):
    """OSSettingsExt应用检测器"""

    def __init__(self):
        super().__init__(AppType.OS_SETTINGS_EXT)

    def get_package_names(self) -> List[str]:
        """获取OSSettingsExt应用包名列表"""
        return ['com.transsion.ossettingsext', 'com.android.ossettingsext', 'com.sh.smart.ossettingsext']

    def get_keywords(self) -> List[str]:
        """获取OSSettingsExt应用关键词列表"""
        return ['ossettingsext', '锁屏设置', 'lockscreensetting']
