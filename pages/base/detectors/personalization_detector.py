"""
Personalization检测器
Personalization应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class PersonalizationDetector(BaseAppDetector):
    """Personalization应用检测器"""

    def __init__(self):
        super().__init__(AppType.PERSONALIZATION)

    def get_package_names(self) -> List[str]:
        """获取Personalization应用包名列表"""
        return ['com.transsion.os.typeface', 'com.transsion.personalization', 'com.android.personalization', 'com.sh.smart.personalization']

    def get_keywords(self) -> List[str]:
        """获取Personalization应用关键词列表"""
        return ['字体', 'personalization', '个性化', 'font']
