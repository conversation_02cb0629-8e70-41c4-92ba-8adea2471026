"""
Playstore Detector检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class PlayStoreDetector(BaseAppDetector):
    """Google Play Store应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.PLAYSTORE)
    
    def get_package_names(self) -> List[str]:
        """获取Play Store应用包名列表"""
        return [
            "com.android.vending",
        ]
    
    def get_keywords(self) -> List[str]:
        """获取Play Store应用关键词列表"""
        return ["vending", "playstore", "play store", "应用商店"]
