"""
Recorder检测器
Recorder应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class RecorderDetector(BaseAppDetector):
    """Recorder应用检测器"""

    def __init__(self):
        super().__init__(AppType.RECORDER)

    def get_package_names(self) -> List[str]:
        """获取Recorder应用包名列表"""
        return ['com.transsion.soundrecorder', 'com.transsion.recorder', 'com.android.recorder', 'com.sh.smart.recorder']

    def get_keywords(self) -> List[str]:
        """获取Recorder应用关键词列表"""
        return ['录音机', 'recorder', 'soundrecorder']
