"""
Removable special检测器
Removable special应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class RemovableSpecialDetector(BaseAppDetector):
    """Removable special应用检测器"""

    def __init__(self):
        super().__init__(AppType.REMOVABLE_SPECIAL)

    def get_package_names(self) -> List[str]:
        """获取Removable special应用包名列表"""
        return ['com.transsion.spacesaversdk', 'com.transsion.removablespecial', 'com.android.removablespecial', 'com.sh.smart.removablespecial']

    def get_keywords(self) -> List[str]:
        """获取Removable special应用关键词列表"""
        return ['可卸载恢复', 'removable special', 'removablespecial']
