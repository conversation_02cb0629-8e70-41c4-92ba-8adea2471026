"""
ScreenRecord检测器
ScreenRecord应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class ScreenRecordDetector(BaseAppDetector):
    """ScreenRecord应用检测器"""

    def __init__(self):
        super().__init__(AppType.SCREEN_RECORD)

    def get_package_names(self) -> List[str]:
        """获取ScreenRecord应用包名列表"""
        return ['com.transsion.screenrecorder', 'com.transsion.screenrecord', 'com.android.screenrecord', 'com.sh.smart.screenrecord']

    def get_keywords(self) -> List[str]:
        """获取ScreenRecord应用关键词列表"""
        return ['录屏', 'screenrecord','screen recording']
