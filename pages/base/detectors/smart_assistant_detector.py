"""
Smart Assistant检测器
Smart Assistant应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class SmartAssistantDetector(BaseAppDetector):
    """Smart Assistant应用检测器"""

    def __init__(self):
        super().__init__(AppType.SMART_ASSISTANT)

    def get_package_names(self) -> List[str]:
        """获取Smart Assistant应用包名列表"""
        return ['com.transsion.kolun.assistant', 'com.transsion.smartassistant', 'com.android.smartassistant', 'com.sh.smart.smartassistant']

    def get_keywords(self) -> List[str]:
        """获取Smart Assistant应用关键词列表"""
        return ['智能助手', 'smart assistant', 'smartassistant', '智能推荐', 'aitips']
