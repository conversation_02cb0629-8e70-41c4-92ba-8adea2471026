"""
Smart Hub检测器
Smart Hub应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class SmartHubDetector(BaseAppDetector):
    """Smart Hub应用检测器"""

    def __init__(self):
        super().__init__(AppType.SMART_HUB)

    def get_package_names(self) -> List[str]:
        """获取Smart Hub应用包名列表"""
        return ['com.transsion.smarthub', 'com.android.smarthub', 'com.sh.smart.smarthub']

    def get_keywords(self) -> List[str]:
        """获取Smart Hub应用关键词列表"""
        return ['跨应用拖拽', 'smart hub', 'smarthub', '中转站', 'dragdrop']
