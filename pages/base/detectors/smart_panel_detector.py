"""
Smart Panel检测器
Smart Panel应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class SmartPanelDetector(BaseAppDetector):
    """Smart Panel应用检测器"""

    def __init__(self):
        super().__init__(AppType.SMART_PANEL)

    def get_package_names(self) -> List[str]:
        """获取Smart Panel应用包名列表"""
        return ['com.transsion.smartpanel', 'com.android.smartpanel', 'com.sh.smart.smartpanel']

    def get_keywords(self) -> List[str]:
        """获取Smart Panel应用关键词列表"""
        return ['smartpanel', 'smart panel', '智慧面板']
