"""
SmartScanner检测器
SmartScanner应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class SmartScannerDetector(BaseAppDetector):
    """SmartScanner应用检测器"""

    def __init__(self):
        super().__init__(AppType.SMART_SCANNER)

    def get_package_names(self) -> List[str]:
        """获取SmartScanner应用包名列表"""
        return ['com.transsion.scanningrecharger', 'com.transsion.smartscanner', 'com.android.smartscanner', 'com.sh.smart.smartscanner']

    def get_keywords(self) -> List[str]:
        """获取SmartScanner应用关键词列表"""
        return ['智慧扫一扫', 'smartscanner']
