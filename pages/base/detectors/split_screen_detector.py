"""
SplitScreen检测器
SplitScreen应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class SplitScreenDetector(BaseAppDetector):
    """SplitScreen应用检测器"""

    def __init__(self):
        super().__init__(AppType.SPLIT_SCREEN)

    def get_package_names(self) -> List[str]:
        """获取SplitScreen应用包名列表"""
        return ['com.android.systemui', 'com.transsion.splitscreen', 'com.android.splitscreen', 'com.sh.smart.splitscreen']

    def get_keywords(self) -> List[str]:
        """获取SplitScreen应用关键词列表"""
        return ['分屏模式', 'splitscreen', '分屏']
