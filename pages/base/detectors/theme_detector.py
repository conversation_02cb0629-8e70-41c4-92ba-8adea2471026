"""
Theme检测器
Theme应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class ThemeDetector(BaseAppDetector):
    """Theme应用检测器"""

    def __init__(self):
        super().__init__(AppType.THEME)

    def get_package_names(self) -> List[str]:
        """获取Theme应用包名列表"""
        return ['com.transsion.theme', 'com.android.theme', 'com.sh.smart.theme']

    def get_keywords(self) -> List[str]:
        """获取Theme应用关键词列表"""
        return ['theme', '主题']
