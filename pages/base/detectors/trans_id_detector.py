"""
TransID检测器
TransID应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class TransIdDetector(BaseAppDetector):
    """TransID应用检测器"""

    def __init__(self):
        super().__init__(AppType.TRANS_ID)

    def get_package_names(self) -> List[str]:
        """获取TransID应用包名列表"""
        return ['com.transsion.cloudserver', 'com.transsion.transid', 'com.android.transid', 'com.sh.smart.transid']

    def get_keywords(self) -> List[str]:
        """获取TransID应用关键词列表"""
        return ['oscloud(云服务)', 'transid', '账号', 'oscloud']
