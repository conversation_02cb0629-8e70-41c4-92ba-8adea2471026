"""
TranssionTips检测器
TranssionTips应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class TranssionTipsDetector(BaseAppDetector):
    """TranssionTips应用检测器"""

    def __init__(self):
        super().__init__(AppType.TRANSSION_TIPS)

    def get_package_names(self) -> List[str]:
        """获取TranssionTips应用包名列表"""
        return ['com.transsion.tips', 'com.transsion.transsiontips', 'com.android.transsiontips', 'com.sh.smart.transsiontips']

    def get_keywords(self) -> List[str]:
        """获取TranssionTips应用关键词列表"""
        return ['transstips', 'transsiontips', '全局引导']
