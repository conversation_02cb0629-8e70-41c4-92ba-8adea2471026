"""
Vishaplayer检测器
Visha音乐播放器应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class VishaplayerDetector(BaseAppDetector):
    """Visha音乐播放器应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.VISHAPLAYER)
    
    def get_package_names(self) -> List[str]:
        """获取Visha音乐播放器应用包名列表"""
        return [
            "com.transsion.visha",
            "com.transsion.vishaplayer",
            "com.sh.smart.visha",
            "com.android.visha",
        ]
    
    def get_keywords(self) -> List[str]:
        """获取Visha音乐播放器应用关键词列表"""
        return ["visha", "vishaplayer", "music", "player", "音乐播放器"]
