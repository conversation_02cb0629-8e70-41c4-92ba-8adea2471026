"""
YouTube检测器
YouTube视频播放应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class YouTubeDetector(BaseAppDetector):
    """YouTube音乐播放器应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.YOUTUBE)
    
    def get_package_names(self) -> List[str]:
        """获取YouTube音乐播放器应用包名列表"""
        return [
            "com.google.android.youtube"
        ]
    
    def get_keywords(self) -> List[str]:
        """获取YouTube音乐播放器应用关键词列表"""
        return ["YouTube", "video", "player", "视频播放器"]
