"""
ZeroScreen检测器
ZeroScreen应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class ZeroScreenDetector(BaseAppDetector):
    """ZeroScreen应用检测器"""

    def __init__(self):
        super().__init__(AppType.ZERO_SCREEN)

    def get_package_names(self) -> List[str]:
        """获取ZeroScreen应用包名列表"""
        return ['com.transsion.hilauncher', 'com.transsion.zeroscreen', 'com.android.zeroscreen', 'com.sh.smart.zeroscreen']

    def get_keywords(self) -> List[str]:
        """获取ZeroScreen应用关键词列表"""
        return ['zeroscreen', '负一屏', 'zeroscreen移动互联']
