2025-08-05 22:35:40 | INFO | core.base_page:__init__:38 | 初始化页面: ella - dialogue_page
2025-08-05 22:35:40 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:179 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-05 22:35:40 | INFO | tools.adb_process_monitor:clear_all_running_processes:1015 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-05 22:35:40 | INFO | tools.adb_process_monitor:clear_all_running_processes:1031 | ⚡ 优先使用命令直接清理...
2025-08-05 22:35:43 | INFO | tools.adb_process_monitor:clear_all_running_processes:1037 | 💪 强制停止顽固应用...
2025-08-05 22:35:48 | INFO | tools.adb_process_monitor:clear_all_running_processes:1047 | 🎉 应用进程清理完成，共清理 30 个应用
2025-08-05 22:35:50 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:181 | ✅ 应用进程清理完成
2025-08-05 22:35:50 | INFO | pages.apps.ella.dialogue_page:start_app:130 | 启动Ella应用
2025-08-05 22:35:50 | INFO | pages.apps.ella.dialogue_page:start_app:138 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-05 22:35:53 | INFO | pages.apps.ella.dialogue_page:_check_app_started:196 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-08-05 22:35:53 | INFO | pages.apps.ella.dialogue_page:start_app:143 | ✅ Ella应用启动成功（指定Activity）
2025-08-05 22:35:53 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:217 | 等待Ella页面加载完成 (超时: 15秒)
2025-08-05 22:35:53 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-08-05 22:35:53 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-05 22:35:53 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-08-05 22:35:53 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:221 | ✅ 输入框已出现，页面加载完成
2025-08-05 22:35:53 | INFO | testcases.test_ella.base_ella_test:ella_app:210 | ✅ Ella应用启动成功
2025-08-05 22:35:53 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:238 | 初始状态None- 使用命令set folding screen zone，状态: 
2025-08-05 22:35:53 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:528 | 确保在对话页面...
2025-08-05 22:35:53 | INFO | pages.base.system_status_checker:ensure_ella_process:1953 | 检查当前进程是否是Ella...
2025-08-05 22:35:53 | INFO | pages.base.system_status_checker:ensure_ella_process:1960 | 当前应用: com.transsion.aivoiceassistant
2025-08-05 22:35:53 | INFO | pages.base.system_status_checker:ensure_ella_process:1961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-05 22:35:53 | INFO | pages.base.system_status_checker:ensure_ella_process:1970 | ✅ 当前在Ella应用进程
2025-08-05 22:35:53 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:539 | ✅ 已在对话页面
2025-08-05 22:35:53 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-08-05 22:35:53 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-08-05 22:35:53 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: set folding screen zone
2025-08-05 22:35:53 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-08-05 22:35:53 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-08-05 22:35:53 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: set folding screen zone
2025-08-05 22:35:54 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-08-05 22:35:54 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-05 22:35:54 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-08-05 22:35:54 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-08-05 22:35:54 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-08-05 22:35:54 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-05 22:35:54 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-08-05 22:35:54 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: set folding screen zone
2025-08-05 22:35:54 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-08-05 22:35:54 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-08-05 22:35:54 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-08-05 22:35:54 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-05 22:35:54 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-08-05 22:35:55 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-08-05 22:35:55 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-08-05 22:35:55 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-08-05 22:35:55 | INFO | testcases.test_ella.base_ella_test:_execute_command:725 | ✅ 成功执行命令: set folding screen zone
2025-08-05 22:35:55 | INFO | testcases.test_ella.base_ella_test:_handle_popup_after_command:58 | handle_popup_after_command:处理弹窗
2025-08-05 22:35:55 | INFO | core.popup_tool:detect_and_close_popup_once:735 | 执行单次弹窗检测和关闭
2025-08-05 22:35:56 | INFO | core.popup_tool:detect_and_close_popup_once:739 | 未检测到弹窗，无需处理
2025-08-05 22:35:56 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:35 | 等待AI响应，超时时间: 8秒
2025-08-05 22:35:56 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:60 | ✅ 通过TTS按钮检测到响应
2025-08-05 22:35:59 | INFO | testcases.test_ella.base_ella_test:_get_final_status_with_page_info:297 | 状态检查时当前应用包名: com.transsion.aivoiceassistant
2025-08-05 22:35:59 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:414 | 状态检查完成，现在获取响应文本
2025-08-05 22:35:59 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:418 | 第1次尝试确保在Ella页面以获取响应
2025-08-05 22:35:59 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:528 | 确保在对话页面...
2025-08-05 22:35:59 | INFO | pages.base.system_status_checker:ensure_ella_process:1953 | 检查当前进程是否是Ella...
2025-08-05 22:36:00 | INFO | pages.base.system_status_checker:ensure_ella_process:1960 | 当前应用: com.transsion.aivoiceassistant
2025-08-05 22:36:00 | INFO | pages.base.system_status_checker:ensure_ella_process:1961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-05 22:36:00 | INFO | pages.base.system_status_checker:ensure_ella_process:1970 | ✅ 当前在Ella应用进程
2025-08-05 22:36:00 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:539 | ✅ 已在对话页面
2025-08-05 22:36:00 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:422 | ✅ 已确认在Ella对话页面，可以获取响应
2025-08-05 22:36:00 | INFO | pages.base.system_status_checker:ensure_ella_process:1953 | 检查当前进程是否是Ella...
2025-08-05 22:36:00 | INFO | pages.base.system_status_checker:ensure_ella_process:1960 | 当前应用: com.transsion.aivoiceassistant
2025-08-05 22:36:00 | INFO | pages.base.system_status_checker:ensure_ella_process:1961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-05 22:36:00 | INFO | pages.base.system_status_checker:ensure_ella_process:1970 | ✅ 当前在Ella应用进程
2025-08-05 22:36:00 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:813 | 检查是否在Ella页面...
2025-08-05 22:36:00 | INFO | pages.base.system_status_checker:ensure_ella_process:1953 | 检查当前进程是否是Ella...
2025-08-05 22:36:00 | INFO | pages.base.system_status_checker:ensure_ella_process:1960 | 当前应用: com.transsion.aivoiceassistant
2025-08-05 22:36:00 | INFO | pages.base.system_status_checker:ensure_ella_process:1961 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-05 22:36:00 | INFO | pages.base.system_status_checker:ensure_ella_process:1970 | ✅ 当前在Ella应用进程
2025-08-05 22:36:00 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:822 | ✅ 当前在Ella页面
2025-08-05 22:36:00 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:140 | 获取AI响应文本
2025-08-05 22:36:01 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:300 | asr_txt文本不符合AI响应格式: set folding screen zone，已达到最大重试次数
2025-08-05 22:36:01 | INFO | pages.apps.ella.ella_response_handler:_is_ai_response:672 | 匹配到AI响应特征: 抱歉，当前系统语言无法查询到关于折叠屏专区的设置项，请保持和语音助手语言一致后再试。
2025-08-05 22:36:01 | INFO | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:292 | ✅ 从robot_text成功获取响应: 抱歉，当前系统语言无法查询到关于折叠屏专区的设置项，请保持和语音助手语言一致后再试。
2025-08-05 22:36:03 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | function_name节点不存在，已达到最大重试次数
2025-08-05 22:36:04 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | function_control节点不存在，已达到最大重试次数
2025-08-05 22:36:04 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:452 | 最终获取的AI响应: '['set folding screen zone', '抱歉，当前系统语言无法查询到关于折叠屏专区的设置项，请保持和语音助手语言一致后再试。', '', '']'
2025-08-05 22:36:04 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaSetFoldingScreenZone\test_completed.png
2025-08-05 22:36:04 | INFO | testcases.test_ella.base_ella_test:simple_command_test:1078 | 🎉 set folding screen zone 测试完成
2025-08-05 22:36:04 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:865 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['set folding screen zone', '抱歉，当前系统语言无法查询到关于折叠屏专区的设置项，请保持和语音助手语言一致后再试。', '', '']
2025-08-05 22:36:04 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:912 | ⚠️ 响应未包含期望内容: 'Sorry'
2025-08-05 22:36:04 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:919 | ❌ 部分期望内容未找到 (0/1)
2025-08-05 22:36:04 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:920 | 缺失内容: ['Sorry']
2025-08-05 22:36:04 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:921 | 搜索文本: 'set folding screen zone 抱歉，当前系统语言无法查询到关于折叠屏专区的设置项，请保持和语音助手语言一致后再试。'
2025-08-05 22:36:04 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaSetFoldingScreenZone\failure_test_set_folding_screen_zone_20250805_223604.png
2025-08-05 22:36:04 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaSetFoldingScreenZone\failure_test_set_folding_screen_zone_20250805_223604.png
2025-08-05 22:36:04 | INFO | pages.apps.ella.dialogue_page:stop_app:237 | 停止Ella应用
2025-08-05 22:36:06 | INFO | pages.apps.ella.dialogue_page:stop_app:248 | ✅ Ella应用已成功停止
