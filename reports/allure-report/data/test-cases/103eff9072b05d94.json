{"uid": "103eff9072b05d94", "name": "测试disable zonetouch master返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_zonetouch_master.TestEllaDisableZonetouchMaster#test_disable_zonetouch_master", "historyId": "d094a0b21c0bd532e6db707dcbab5564", "time": {"start": 1754452471155, "stop": 1754452485017, "duration": 13862}, "description": "验证disable zonetouch master指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable zonetouch master指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452458542, "stop": 1754452471153, "duration": 12611}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452471153, "stop": 1754452471153, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证disable zonetouch master指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable zonetouch master", "time": {"start": 1754452471155, "stop": 1754452484809, "duration": 13654}, "status": "passed", "steps": [{"name": "执行命令: disable zonetouch master", "time": {"start": 1754452471155, "stop": 1754452484613, "duration": 13458}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452484613, "stop": 1754452484809, "duration": 196}, "status": "passed", "steps": [], "attachments": [{"uid": "849c49bda636fecf", "name": "测试总结", "source": "849c49bda636fecf.txt", "type": "text/plain", "size": 234}, {"uid": "6f032c161fcf72fd", "name": "test_completed", "source": "6f032c161fcf72fd.png", "type": "image/png", "size": 481811}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452484809, "stop": 1754452484810, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452484810, "stop": 1754452485017, "duration": 207}, "status": "passed", "steps": [], "attachments": [{"uid": "a692872dab2f07f1", "name": "测试总结", "source": "a692872dab2f07f1.txt", "type": "text/plain", "size": 234}, {"uid": "92fdd0dcf707bd27", "name": "test_completed", "source": "92fdd0dcf707bd27.png", "type": "image/png", "size": 482190}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d915f2391a54d9fd", "name": "stdout", "source": "d915f2391a54d9fd.txt", "type": "text/plain", "size": 11344}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452485018, "stop": 1754452485018, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452485019, "stop": 1754452486352, "duration": 1333}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_zonetouch_master"}, {"name": "subSuite", "value": "TestEllaDisableZonetouchMaster"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_zonetouch_master"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "eddc069dcdd677ff", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402502346, "stop": 1754402515520, "duration": 13174}}], "categories": [], "tags": ["smoke"]}, "source": "103eff9072b05d94.json", "parameterValues": []}