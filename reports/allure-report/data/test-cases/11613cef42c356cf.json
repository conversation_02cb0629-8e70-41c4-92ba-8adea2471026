{"uid": "11613cef42c356cf", "name": "continue  screen recording能正常执行", "fullName": "testcases.test_ella.system_coupling.test_start_screen_recording.TestEllaStartScreenRecording#test_continue_screen_recording", "historyId": "454f04318d433db60e7e6f2de5790fc3", "time": {"start": 1754450294402, "stop": 1754450310643, "duration": 16241}, "description": "continue  screen recording", "descriptionHtml": "<p>continue  screen recording</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450281817, "stop": 1754450294401, "duration": 12584}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450294401, "stop": 1754450294401, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "continue  screen recording", "status": "passed", "steps": [{"name": "执行命令: continue screen recording", "time": {"start": 1754450294402, "stop": 1754450310420, "duration": 16018}, "status": "passed", "steps": [{"name": "执行命令: continue screen recording", "time": {"start": 1754450294402, "stop": 1754450310208, "duration": 15806}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450310208, "stop": 1754450310420, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "1876a3e09a31549e", "name": "测试总结", "source": "1876a3e09a31549e.txt", "type": "text/plain", "size": 199}, {"uid": "9394882d46c7e78f", "name": "test_completed", "source": "9394882d46c7e78f.png", "type": "image/png", "size": 613266}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450310420, "stop": 1754450310421, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754450310421, "stop": 1754450310421, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450310421, "stop": 1754450310643, "duration": 222}, "status": "passed", "steps": [], "attachments": [{"uid": "91e3a8df120027e7", "name": "测试总结", "source": "91e3a8df120027e7.txt", "type": "text/plain", "size": 199}, {"uid": "b1f4b43993cba67", "name": "test_completed", "source": "b1f4b43993cba67.png", "type": "image/png", "size": 613227}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "8750ed807b8e57f9", "name": "stdout", "source": "8750ed807b8e57f9.txt", "type": "text/plain", "size": 11928}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450310644, "stop": 1754450310644, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450310645, "stop": 1754450312040, "duration": 1395}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_start_screen_recording"}, {"name": "subSuite", "value": "TestEllaStartScreenRecording"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_start_screen_recording"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "8b0c15bf0ba50935", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording continued']\nassert False", "time": {"start": 1754400466252, "stop": 1754400479788, "duration": 13536}}], "categories": [], "tags": ["smoke"]}, "source": "11613cef42c356cf.json", "parameterValues": []}