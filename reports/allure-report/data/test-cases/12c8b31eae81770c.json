{"uid": "12c8b31eae81770c", "name": "测试disable magic voice changer能正常执行", "fullName": "testcases.test_ella.dialogue.test_disable_magic_voice_changer.TestEllaDisableMagicVoiceChanger#test_disable_magic_voice_changer", "historyId": "d6d97ebce763bf8ead601650bfb2383c", "time": {"start": 1754398074697, "stop": 1754398087507, "duration": 12810}, "description": "disable magic voice changer", "descriptionHtml": "<p>disable magic voice changer</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398061553, "stop": 1754398074696, "duration": 13143}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398074696, "stop": 1754398074696, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "disable magic voice changer", "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "time": {"start": 1754398074697, "stop": 1754398087181, "duration": 12484}, "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "time": {"start": 1754398074697, "stop": 1754398086909, "duration": 12212}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398086909, "stop": 1754398087180, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "889f846a7d6dc91d", "name": "测试总结", "source": "889f846a7d6dc91d.txt", "type": "text/plain", "size": 239}, {"uid": "551af9b7772bb314", "name": "test_completed", "source": "551af9b7772bb314.png", "type": "image/png", "size": 606488}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1754398087181, "stop": 1754398087186, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398087186, "stop": 1754398087506, "duration": 320}, "status": "passed", "steps": [], "attachments": [{"uid": "4c34379e42ce09e8", "name": "测试总结", "source": "4c34379e42ce09e8.txt", "type": "text/plain", "size": 239}, {"uid": "c9bb4b61788f7f76", "name": "test_completed", "source": "c9bb4b61788f7f76.png", "type": "image/png", "size": 606395}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "585db05a1f7d3f74", "name": "stdout", "source": "585db05a1f7d3f74.txt", "type": "text/plain", "size": 11501}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398087508, "stop": 1754398087508, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398087511, "stop": 1754398088773, "duration": 1262}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_disable_magic_voice_changer"}, {"name": "subSuite", "value": "TestEllaDisableMagicVoiceChanger"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_disable_magic_voice_changer"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "12c8b31eae81770c.json", "parameterValues": []}