{"uid": "14097408c33e2291", "name": "测试disable touch optimization返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_touch_optimization.TestEllaDisableTouchOptimization#test_disable_touch_optimization", "historyId": "0b659537bc9c9b47c2c23f702fadd56b", "time": {"start": 1754452414652, "stop": 1754452428842, "duration": 14190}, "description": "验证disable touch optimization指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable touch optimization指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452402158, "stop": 1754452414650, "duration": 12492}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452414650, "stop": 1754452414650, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证disable touch optimization指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable touch optimization", "time": {"start": 1754452414652, "stop": 1754452428677, "duration": 14025}, "status": "passed", "steps": [{"name": "执行命令: disable touch optimization", "time": {"start": 1754452414652, "stop": 1754452428473, "duration": 13821}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452428473, "stop": 1754452428675, "duration": 202}, "status": "passed", "steps": [], "attachments": [{"uid": "e9d3bc76dde0f85c", "name": "测试总结", "source": "e9d3bc76dde0f85c.txt", "type": "text/plain", "size": 240}, {"uid": "36d114ae37c94f4c", "name": "test_completed", "source": "36d114ae37c94f4c.png", "type": "image/png", "size": 492513}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452428677, "stop": 1754452428677, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452428678, "stop": 1754452428841, "duration": 163}, "status": "passed", "steps": [], "attachments": [{"uid": "cf3ba840a8bfbac8", "name": "测试总结", "source": "cf3ba840a8bfbac8.txt", "type": "text/plain", "size": 240}, {"uid": "3f226d5f49897454", "name": "test_completed", "source": "3f226d5f49897454.png", "type": "image/png", "size": 491250}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "ea61b08ffa93067a", "name": "stdout", "source": "ea61b08ffa93067a.txt", "type": "text/plain", "size": 11372}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452428843, "stop": 1754452428843, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452428844, "stop": 1754452430252, "duration": 1408}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_touch_optimization"}, {"name": "subSuite", "value": "TestEllaDisableTouchOptimization"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_touch_optimization"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "c11c3638efcf1ad4", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402450853, "stop": 1754402462134, "duration": 11281}}], "categories": [], "tags": ["smoke"]}, "source": "14097408c33e2291.json", "parameterValues": []}