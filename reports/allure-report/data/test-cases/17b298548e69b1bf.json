{"uid": "17b298548e69b1bf", "name": "测试open dialer能正常执行", "fullName": "testcases.test_ella.component_coupling.test_open_dialer.TestEllaCommandConcise#test_open_dialer", "historyId": "936ae2bf6db744b69d4acf28b22f7646", "time": {"start": 1754446944051, "stop": 1754446966739, "duration": 22688}, "description": "open dialer", "descriptionHtml": "<p>open dialer</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754446931607, "stop": 1754446944050, "duration": 12443}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754446944050, "stop": 1754446944050, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "open dialer", "status": "passed", "steps": [{"name": "执行命令: open dialer", "time": {"start": 1754446944051, "stop": 1754446966510, "duration": 22459}, "status": "passed", "steps": [{"name": "执行命令: open dialer", "time": {"start": 1754446944051, "stop": 1754446966300, "duration": 22249}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446966300, "stop": 1754446966510, "duration": 210}, "status": "passed", "steps": [], "attachments": [{"uid": "fd91173918f552a0", "name": "测试总结", "source": "fd91173918f552a0.txt", "type": "text/plain", "size": 206}, {"uid": "55a12a5e1fd5d245", "name": "test_completed", "source": "55a12a5e1fd5d245.png", "type": "image/png", "size": 563559}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1754446966510, "stop": 1754446966511, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446966511, "stop": 1754446966738, "duration": 227}, "status": "passed", "steps": [], "attachments": [{"uid": "c5c0a8c9430623f3", "name": "测试总结", "source": "c5c0a8c9430623f3.txt", "type": "text/plain", "size": 206}, {"uid": "9ca153e57e03ad9f", "name": "test_completed", "source": "9ca153e57e03ad9f.png", "type": "image/png", "size": 563818}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "1e8b926210ca168e", "name": "stdout", "source": "1e8b926210ca168e.txt", "type": "text/plain", "size": 14363}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754446966740, "stop": 1754446968103, "duration": 1363}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754446966740, "stop": 1754446966740, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_dialer"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_dialer"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "e4f52714d79f9e15", "status": "passed", "time": {"start": 1754397246702, "stop": 1754397270687, "duration": 23985}}], "categories": [], "tags": ["smoke"]}, "source": "17b298548e69b1bf.json", "parameterValues": []}