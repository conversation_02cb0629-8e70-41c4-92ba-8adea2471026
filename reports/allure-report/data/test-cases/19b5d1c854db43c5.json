{"uid": "19b5d1c854db43c5", "name": "测试why is my phone not ringing on incoming calls能正常执行", "fullName": "testcases.test_ella.dialogue.test_why_is_my_phone_not_ringing_on_incoming_calls.TestEllaWhyIsMyPhoneNotRingingIncomingCalls#test_why_is_my_phone_not_ringing_on_incoming_calls", "historyId": "cf0ebfd1b4e2ab43e2f516ad6a1a6917", "time": {"start": 1754449223326, "stop": 1754449245649, "duration": 22323}, "description": "why is my phone not ringing on incoming calls", "descriptionHtml": "<p>why is my phone not ringing on incoming calls</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449210841, "stop": 1754449223324, "duration": 12483}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449223324, "stop": 1754449223324, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "why is my phone not ringing on incoming calls", "status": "passed", "steps": [{"name": "执行命令: why is my phone not ringing on incoming calls", "time": {"start": 1754449223326, "stop": 1754449245456, "duration": 22130}, "status": "passed", "steps": [{"name": "执行命令: why is my phone not ringing on incoming calls", "time": {"start": 1754449223326, "stop": 1754449245258, "duration": 21932}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449245258, "stop": 1754449245456, "duration": 198}, "status": "passed", "steps": [], "attachments": [{"uid": "4b42ac498266d88d", "name": "测试总结", "source": "4b42ac498266d88d.txt", "type": "text/plain", "size": 686}, {"uid": "5e3ae2048f4e802c", "name": "test_completed", "source": "5e3ae2048f4e802c.png", "type": "image/png", "size": 677366}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754449245456, "stop": 1754449245458, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449245458, "stop": 1754449245649, "duration": 191}, "status": "passed", "steps": [], "attachments": [{"uid": "a7936cb7302ff4ca", "name": "测试总结", "source": "a7936cb7302ff4ca.txt", "type": "text/plain", "size": 686}, {"uid": "342eeb3c786d1206", "name": "test_completed", "source": "342eeb3c786d1206.png", "type": "image/png", "size": 677004}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d7815ee310d3252d", "name": "stdout", "source": "d7815ee310d3252d.txt", "type": "text/plain", "size": 13246}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449245650, "stop": 1754449245650, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449245651, "stop": 1754449247067, "duration": 1416}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_why_is_my_phone_not_ringing_on_incoming_calls"}, {"name": "subSuite", "value": "TestEllaWhyIsMyPhoneNotRingingIncomingCalls"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_why_is_my_phone_not_ringing_on_incoming_calls"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "610dd20e7374d3c8", "status": "passed", "time": {"start": 1754399424224, "stop": 1754399446307, "duration": 22083}}], "categories": [], "tags": ["smoke"]}, "source": "19b5d1c854db43c5.json", "parameterValues": []}