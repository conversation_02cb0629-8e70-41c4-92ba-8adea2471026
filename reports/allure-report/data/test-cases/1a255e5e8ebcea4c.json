{"uid": "1a255e5e8ebcea4c", "name": "测试jump to notifications and status bar settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_notifications_and_status_bar_settings.TestEllaJumpNotificationsStatusBarSettings#test_jump_to_notifications_and_status_bar_settings", "historyId": "d8bd499fa9e4e04741c5c255fac9036d", "time": {"start": 1754453396189, "stop": 1754453412199, "duration": 16010}, "description": "验证jump to notifications and status bar settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to notifications and status bar settings指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453384296, "stop": 1754453396188, "duration": 11892}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453396188, "stop": 1754453396188, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证jump to notifications and status bar settings指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: jump to notifications and status bar settings", "time": {"start": 1754453396189, "stop": 1754453411988, "duration": 15799}, "status": "passed", "steps": [{"name": "执行命令: jump to notifications and status bar settings", "time": {"start": 1754453396189, "stop": 1754453411805, "duration": 15616}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453411806, "stop": 1754453411987, "duration": 181}, "status": "passed", "steps": [], "attachments": [{"uid": "6ccce76b131696e1", "name": "测试总结", "source": "6ccce76b131696e1.txt", "type": "text/plain", "size": 290}, {"uid": "284aa57922adda7c", "name": "test_completed", "source": "284aa57922adda7c.png", "type": "image/png", "size": 530929}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453411988, "stop": 1754453411990, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453411990, "stop": 1754453412199, "duration": 209}, "status": "passed", "steps": [], "attachments": [{"uid": "172b835405ebf3d5", "name": "测试总结", "source": "172b835405ebf3d5.txt", "type": "text/plain", "size": 290}, {"uid": "6fb44e66cc7de7df", "name": "test_completed", "source": "6fb44e66cc7de7df.png", "type": "image/png", "size": 530507}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "87f9c0226412fd89", "name": "stdout", "source": "87f9c0226412fd89.txt", "type": "text/plain", "size": 12107}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453412200, "stop": 1754453412200, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453412201, "stop": 1754453413508, "duration": 1307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_notifications_and_status_bar_settings"}, {"name": "subSuite", "value": "TestEllaJumpNotificationsStatusBarSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_notifications_and_status_bar_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "b4475b9055e1a3e8", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403414558, "stop": 1754403431585, "duration": 17027}}], "categories": [], "tags": ["smoke"]}, "source": "1a255e5e8ebcea4c.json", "parameterValues": []}