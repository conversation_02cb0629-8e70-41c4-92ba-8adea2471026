{"uid": "1a47f528b9b87329", "name": "测试continue music能正常执行", "fullName": "testcases.test_ella.component_coupling.test_continue_music.TestEllaContinueMusic#test_continue_music", "historyId": "87f3dc53ab72c729262e053c16a3dbcb", "time": {"start": 1754446642140, "stop": 1754446655684, "duration": 13544}, "description": "continue music", "descriptionHtml": "<p>continue music</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754446630495, "stop": 1754446642140, "duration": 11645}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754446642140, "stop": 1754446642140, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "continue music", "status": "passed", "steps": [{"name": "执行命令: continue music", "time": {"start": 1754446642140, "stop": 1754446655493, "duration": 13353}, "status": "passed", "steps": [{"name": "执行命令: continue music", "time": {"start": 1754446642140, "stop": 1754446655279, "duration": 13139}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446655279, "stop": 1754446655492, "duration": 213}, "status": "passed", "steps": [], "attachments": [{"uid": "96a8f2c35a1eb79", "name": "测试总结", "source": "96a8f2c35a1eb79.txt", "type": "text/plain", "size": 185}, {"uid": "fb9f049b0b538de3", "name": "test_completed", "source": "fb9f049b0b538de3.png", "type": "image/png", "size": 579309}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754446655493, "stop": 1754446655494, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446655494, "stop": 1754446655684, "duration": 190}, "status": "passed", "steps": [], "attachments": [{"uid": "8e611c4df0810d67", "name": "测试总结", "source": "8e611c4df0810d67.txt", "type": "text/plain", "size": 185}, {"uid": "eef06b58b0b81c00", "name": "test_completed", "source": "eef06b58b0b81c00.png", "type": "image/png", "size": 579425}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "7928a436f3f52d9c", "name": "stdout", "source": "7928a436f3f52d9c.txt", "type": "text/plain", "size": 11444}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754446655685, "stop": 1754446655685, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754446655686, "stop": 1754446657003, "duration": 1317}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_continue_music"}, {"name": "subSuite", "value": "TestEllaContinueMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_continue_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "927966aed0675c11", "status": "passed", "time": {"start": 1754396953010, "stop": 1754396965571, "duration": 12561}}], "categories": [], "tags": ["smoke"]}, "source": "1a47f528b9b87329.json", "parameterValues": []}