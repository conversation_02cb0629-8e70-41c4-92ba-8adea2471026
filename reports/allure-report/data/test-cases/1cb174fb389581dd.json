{"uid": "1cb174fb389581dd", "name": "测试screen record能正常执行", "fullName": "testcases.test_ella.system_coupling.test_screen_record.TestEllaScreenRecord#test_screen_record", "historyId": "18fb8c43c609a9825fe52e528761fd1b", "time": {"start": 1754450037016, "stop": 1754450055449, "duration": 18433}, "description": "screen record", "descriptionHtml": "<p>screen record</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450024376, "stop": 1754450037015, "duration": 12639}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450037015, "stop": 1754450037015, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "screen record", "status": "passed", "steps": [{"name": "执行命令: screen record", "time": {"start": 1754450037016, "stop": 1754450055230, "duration": 18214}, "status": "passed", "steps": [{"name": "执行命令: screen record", "time": {"start": 1754450037016, "stop": 1754450055044, "duration": 18028}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450055044, "stop": 1754450055230, "duration": 186}, "status": "passed", "steps": [], "attachments": [{"uid": "49670c43b41eda07", "name": "测试总结", "source": "49670c43b41eda07.txt", "type": "text/plain", "size": 174}, {"uid": "94a3a9152b583616", "name": "test_completed", "source": "94a3a9152b583616.png", "type": "image/png", "size": 588653}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450055230, "stop": 1754450055231, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754450055231, "stop": 1754450055231, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450055231, "stop": 1754450055449, "duration": 218}, "status": "passed", "steps": [], "attachments": [{"uid": "778864ea50d037c4", "name": "测试总结", "source": "778864ea50d037c4.txt", "type": "text/plain", "size": 174}, {"uid": "95a75d52550479c", "name": "test_completed", "source": "95a75d52550479c.png", "type": "image/png", "size": 589005}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "76c963ac24a9ab55", "name": "stdout", "source": "76c963ac24a9ab55.txt", "type": "text/plain", "size": 11614}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450055450, "stop": 1754450055450, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450055451, "stop": 1754450056894, "duration": 1443}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_screen_record"}, {"name": "subSuite", "value": "TestEllaScreenRecord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_screen_record"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "a41258ab5dd976d3", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording started']\nassert False", "time": {"start": 1754400219939, "stop": 1754400236945, "duration": 17006}}], "categories": [], "tags": ["smoke"]}, "source": "1cb174fb389581dd.json", "parameterValues": []}