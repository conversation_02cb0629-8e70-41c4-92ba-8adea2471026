{"uid": "1ddf4917c0fa3e0f", "name": "测试stop playing", "fullName": "testcases.test_ella.component_coupling.test_stop_playing.TestEllaOpenYoutube#test_stop_playing", "historyId": "329fa4b06eb0b0d769c2c418ed03dab7", "time": {"start": 1754447470350, "stop": 1754447485362, "duration": 15012}, "description": "测试stop playing指令", "descriptionHtml": "<p>测试stop playing指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447457721, "stop": 1754447470349, "duration": 12628}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447470349, "stop": 1754447470349, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试stop playing指令", "status": "passed", "steps": [{"name": "执行命令: stop playing", "time": {"start": 1754447470350, "stop": 1754447485143, "duration": 14793}, "status": "passed", "steps": [{"name": "执行命令: stop playing", "time": {"start": 1754447470350, "stop": 1754447484950, "duration": 14600}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447484950, "stop": 1754447485143, "duration": 193}, "status": "passed", "steps": [], "attachments": [{"uid": "5213271e943a8d33", "name": "测试总结", "source": "5213271e943a8d33.txt", "type": "text/plain", "size": 183}, {"uid": "f51567284237f06a", "name": "test_completed", "source": "f51567284237f06a.png", "type": "image/png", "size": 617095}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447485143, "stop": 1754447485144, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447485144, "stop": 1754447485362, "duration": 218}, "status": "passed", "steps": [], "attachments": [{"uid": "e58829c675397cc3", "name": "测试总结", "source": "e58829c675397cc3.txt", "type": "text/plain", "size": 183}, {"uid": "87871e0a9ad6c5a4", "name": "test_completed", "source": "87871e0a9ad6c5a4.png", "type": "image/png", "size": 617440}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "27453cff6e788a9f", "name": "stdout", "source": "27453cff6e788a9f.txt", "type": "text/plain", "size": 11696}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447485363, "stop": 1754447485363, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447485364, "stop": 1754447486761, "duration": 1397}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_stop_playing"}, {"name": "subSuite", "value": "TestEllaOpenYoutube"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_stop_playing"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "51c874c21ac94a95", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['No music playing in the background']\nassert False", "time": {"start": 1754397750420, "stop": 1754397765026, "duration": 14606}}], "categories": [], "tags": ["smoke"]}, "source": "1ddf4917c0fa3e0f.json", "parameterValues": []}