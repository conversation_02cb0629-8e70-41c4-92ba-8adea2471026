{"uid": "1dfd12c1bd7d35e2", "name": "测试open camera", "fullName": "testcases.test_ella.open_app.test_open_camera.TestEllaOpenCamera#test_open_camera", "historyId": "cf4d81285cd0bfb49bf81dabe5eaa538", "time": {"start": 1754399517612, "stop": 1754399534201, "duration": 16589}, "description": "测试open camera指令", "descriptionHtml": "<p>测试open camera指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399504880, "stop": 1754399517611, "duration": 12731}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399517611, "stop": 1754399517611, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试open camera指令", "status": "passed", "steps": [{"name": "执行命令: open camera", "time": {"start": 1754399517612, "stop": 1754399533905, "duration": 16293}, "status": "passed", "steps": [{"name": "执行命令: open camera", "time": {"start": 1754399517612, "stop": 1754399533589, "duration": 15977}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399533590, "stop": 1754399533904, "duration": 314}, "status": "passed", "steps": [], "attachments": [{"uid": "119baaa80372190", "name": "测试总结", "source": "119baaa80372190.txt", "type": "text/plain", "size": 306}, {"uid": "a517f31c22792bbe", "name": "test_completed", "source": "a517f31c22792bbe.png", "type": "image/png", "size": 522726}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399533905, "stop": 1754399533910, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证camera已打开", "time": {"start": 1754399533910, "stop": 1754399533910, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399533910, "stop": 1754399534200, "duration": 290}, "status": "passed", "steps": [], "attachments": [{"uid": "4364698fa45d199a", "name": "测试总结", "source": "4364698fa45d199a.txt", "type": "text/plain", "size": 306}, {"uid": "cc949958bc8d3098", "name": "test_completed", "source": "cc949958bc8d3098.png", "type": "image/png", "size": 522818}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "962d267beb746d8f", "name": "stdout", "source": "962d267beb746d8f.txt", "type": "text/plain", "size": 14640}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399534203, "stop": 1754399534203, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399534205, "stop": 1754399535488, "duration": 1283}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_camera"}, {"name": "subSuite", "value": "TestEllaOpenCamera"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_camera"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "1dfd12c1bd7d35e2.json", "parameterValues": []}