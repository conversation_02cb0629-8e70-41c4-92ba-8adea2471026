{"uid": "1e11879944dfe296", "name": "测试turn on bluetooth能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_bluetooth.TestEllaTurnBluetooth#test_turn_on_bluetooth", "historyId": "aff947fee562ec2636c3ce68a270b88d", "time": {"start": 1754450977331, "stop": 1754450990939, "duration": 13608}, "description": "turn on bluetooth", "descriptionHtml": "<p>turn on bluetooth</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450964893, "stop": 1754450977330, "duration": 12437}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450977330, "stop": 1754450977330, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "turn on bluetooth", "status": "passed", "steps": [{"name": "执行命令: turn on bluetooth", "time": {"start": 1754450977331, "stop": 1754450990744, "duration": 13413}, "status": "passed", "steps": [{"name": "执行命令: turn on bluetooth", "time": {"start": 1754450977331, "stop": 1754450990554, "duration": 13223}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450990554, "stop": 1754450990744, "duration": 190}, "status": "passed", "steps": [], "attachments": [{"uid": "e33051b53128c51a", "name": "测试总结", "source": "e33051b53128c51a.txt", "type": "text/plain", "size": 192}, {"uid": "dee27d0b5bc24957", "name": "test_completed", "source": "dee27d0b5bc24957.png", "type": "image/png", "size": 541194}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450990744, "stop": 1754450990745, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754450990745, "stop": 1754450990745, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450990745, "stop": 1754450990938, "duration": 193}, "status": "passed", "steps": [], "attachments": [{"uid": "8853f428b223476f", "name": "测试总结", "source": "8853f428b223476f.txt", "type": "text/plain", "size": 192}, {"uid": "c8570e5898e242aa", "name": "test_completed", "source": "c8570e5898e242aa.png", "type": "image/png", "size": 540838}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "464e36a41f954a2e", "name": "stdout", "source": "464e36a41f954a2e.txt", "type": "text/plain", "size": 12708}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450990940, "stop": 1754450990940, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450990940, "stop": 1754450992348, "duration": 1408}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_bluetooth"}, {"name": "subSuite", "value": "TestEllaTurnBluetooth"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_bluetooth"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "554b736d8a926fdb", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Bluetooth is turned on now']\nassert False", "time": {"start": 1754401132691, "stop": 1754401145172, "duration": 12481}}], "categories": [], "tags": ["smoke"]}, "source": "1e11879944dfe296.json", "parameterValues": []}