{"uid": "1e2f8e6f0326fd85", "name": "测试jump to call notifications返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_call_notifications.TestEllaJumpCallNotifications#test_jump_to_call_notifications", "historyId": "540cff5d6d552c22ec37f66efd17315f", "time": {"start": 1754453260837, "stop": 1754453283520, "duration": 22683}, "description": "验证jump to call notifications指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to call notifications指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453248181, "stop": 1754453260836, "duration": 12655}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453260836, "stop": 1754453260836, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证jump to call notifications指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: jump to call notifications", "time": {"start": 1754453260837, "stop": 1754453283334, "duration": 22497}, "status": "passed", "steps": [{"name": "执行命令: jump to call notifications", "time": {"start": 1754453260837, "stop": 1754453283147, "duration": 22310}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453283147, "stop": 1754453283334, "duration": 187}, "status": "passed", "steps": [], "attachments": [{"uid": "6587df8dc4b89fc8", "name": "测试总结", "source": "6587df8dc4b89fc8.txt", "type": "text/plain", "size": 246}, {"uid": "a05bf7e2855eacb1", "name": "test_completed", "source": "a05bf7e2855eacb1.png", "type": "image/png", "size": 497432}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453283334, "stop": 1754453283334, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453283334, "stop": 1754453283519, "duration": 185}, "status": "passed", "steps": [], "attachments": [{"uid": "b25486c1d0e19eb0", "name": "测试总结", "source": "b25486c1d0e19eb0.txt", "type": "text/plain", "size": 246}, {"uid": "a5d6a30959a37cae", "name": "test_completed", "source": "a5d6a30959a37cae.png", "type": "image/png", "size": 497983}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "225ae9014a8bc055", "name": "stdout", "source": "225ae9014a8bc055.txt", "type": "text/plain", "size": 11783}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453283520, "stop": 1754453283520, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453283521, "stop": 1754453284896, "duration": 1375}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_call_notifications"}, {"name": "subSuite", "value": "TestEllaJumpCallNotifications"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_call_notifications"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "84d4f841cbe28d47", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403274939, "stop": 1754403299198, "duration": 24259}}], "categories": [], "tags": ["smoke"]}, "source": "1e2f8e6f0326fd85.json", "parameterValues": []}