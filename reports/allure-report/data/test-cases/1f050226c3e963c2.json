{"uid": "1f050226c3e963c2", "name": "open clock", "fullName": "testcases.test_ella.component_coupling.test_open_clock.TestEllaCommandConcise#test_open_clock", "historyId": "169e5b613c0fec2cebd053175998bf17", "time": {"start": 1754446845200, "stop": 1754446865875, "duration": 20675}, "description": "使用open clock命令，验证响应包含Done且实际打开clock命令", "descriptionHtml": "<p>使用open clock命令，验证响应包含Done且实际打开clock命令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754446832786, "stop": 1754446845199, "duration": 12413}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754446845199, "stop": 1754446845199, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "使用open clock命令，验证响应包含Done且实际打开clock命令", "status": "passed", "steps": [{"name": "执行命令: open clock", "time": {"start": 1754446845200, "stop": 1754446865656, "duration": 20456}, "status": "passed", "steps": [{"name": "执行命令: open clock", "time": {"start": 1754446845200, "stop": 1754446865440, "duration": 20240}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446865441, "stop": 1754446865656, "duration": 215}, "status": "passed", "steps": [], "attachments": [{"uid": "b10fa054215dce5b", "name": "测试总结", "source": "b10fa054215dce5b.txt", "type": "text/plain", "size": 299}, {"uid": "dea8c4b1d068de1b", "name": "test_completed", "source": "dea8c4b1d068de1b.png", "type": "image/png", "size": 557318}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含Done", "time": {"start": 1754446865656, "stop": 1754446865657, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证clock已打开", "time": {"start": 1754446865657, "stop": 1754446865657, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446865657, "stop": 1754446865875, "duration": 218}, "status": "passed", "steps": [], "attachments": [{"uid": "f14b00e92c936447", "name": "测试总结", "source": "f14b00e92c936447.txt", "type": "text/plain", "size": 299}, {"uid": "7cea0b39f623154", "name": "test_completed", "source": "7cea0b39f623154.png", "type": "image/png", "size": 557396}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "aaf4a4f3f6b6dcc2", "name": "stdout", "source": "aaf4a4f3f6b6dcc2.txt", "type": "text/plain", "size": 14585}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754446865876, "stop": 1754446867240, "duration": 1364}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754446865876, "stop": 1754446865876, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "open clock"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_clock"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_clock"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "246dc31e75d9eca5", "status": "passed", "time": {"start": 1754397151722, "stop": 1754397171255, "duration": 19533}}], "categories": [], "tags": ["smoke"]}, "source": "1f050226c3e963c2.json", "parameterValues": []}