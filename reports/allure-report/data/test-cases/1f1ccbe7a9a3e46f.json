{"uid": "1f1ccbe7a9a3e46f", "name": "测试jump to nfc settings", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_nfc_settings.TestEllaOpenPlayPoliticalNews#test_play_political_news", "historyId": "d927afc2c4987ea6c13c14173a35bc45", "time": {"start": 1754453361932, "stop": 1754453382979, "duration": 21047}, "description": "测试jump to nfc settings指令", "descriptionHtml": "<p>测试jump to nfc settings指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453349945, "stop": 1754453361931, "duration": 11986}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453361931, "stop": 1754453361931, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试jump to nfc settings指令", "status": "passed", "steps": [{"name": "执行命令: jump to nfc settings", "time": {"start": 1754453361932, "stop": 1754453382761, "duration": 20829}, "status": "passed", "steps": [{"name": "执行命令: jump to nfc settings", "time": {"start": 1754453361932, "stop": 1754453382542, "duration": 20610}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453382542, "stop": 1754453382761, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "f9d8275e0e277521", "name": "测试总结", "source": "f9d8275e0e277521.txt", "type": "text/plain", "size": 412}, {"uid": "ef97dfe97a988f83", "name": "test_completed", "source": "ef97dfe97a988f83.png", "type": "image/png", "size": 450450}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754453382761, "stop": 1754453382763, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证settings已打开", "time": {"start": 1754453382763, "stop": 1754453382763, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453382763, "stop": 1754453382978, "duration": 215}, "status": "passed", "steps": [], "attachments": [{"uid": "da288740960e6797", "name": "测试总结", "source": "da288740960e6797.txt", "type": "text/plain", "size": 412}, {"uid": "1012d47fb1987a82", "name": "test_completed", "source": "1012d47fb1987a82.png", "type": "image/png", "size": 450450}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "c83d6cffa6fc532d", "name": "stdout", "source": "c83d6cffa6fc532d.txt", "type": "text/plain", "size": 14943}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453382979, "stop": 1754453382979, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453382980, "stop": 1754453384279, "duration": 1299}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_nfc_settings"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_nfc_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "ea1bfb9e3930e5e", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754403379376, "stop": 1754403399823, "duration": 20447}}], "categories": [], "tags": ["smoke"]}, "source": "1f1ccbe7a9a3e46f.json", "parameterValues": []}