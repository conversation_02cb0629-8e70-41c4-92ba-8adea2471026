{"uid": "1f38cfd1433da4f", "name": "测试change your language to chinese能正常执行", "fullName": "testcases.test_ella.system_coupling.test_change_your_language_to_chinese.TestEllaChangeYourLanguageChinese#test_change_your_language_to_chinese", "historyId": "9b6faa79e3fe09fed639b1092082745b", "time": {"start": 1754399576019, "stop": 1754399587350, "duration": 11331}, "description": "change your language to chinese", "descriptionHtml": "<p>change your language to chinese</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399562802, "stop": 1754399576018, "duration": 13216}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399576019, "stop": 1754399576019, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "change your language to chinese", "status": "passed", "steps": [{"name": "执行命令: change your language to chinese", "time": {"start": 1754399576020, "stop": 1754399587050, "duration": 11030}, "status": "passed", "steps": [{"name": "执行命令: change your language to chinese", "time": {"start": 1754399576020, "stop": 1754399586779, "duration": 10759}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399586779, "stop": 1754399587049, "duration": 270}, "status": "passed", "steps": [], "attachments": [{"uid": "7cc6a98cd294bc66", "name": "测试总结", "source": "7cc6a98cd294bc66.txt", "type": "text/plain", "size": 285}, {"uid": "6bc887298e1d3a22", "name": "test_completed", "source": "6bc887298e1d3a22.png", "type": "image/png", "size": 569673}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "记录测试结果", "time": {"start": 1754399587050, "stop": 1754399587349, "duration": 299}, "status": "passed", "steps": [], "attachments": [{"uid": "2f6ae2175e8f047", "name": "测试总结", "source": "2f6ae2175e8f047.txt", "type": "text/plain", "size": 285}, {"uid": "ee91d99385b9ff17", "name": "test_completed", "source": "ee91d99385b9ff17.png", "type": "image/png", "size": 569673}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d054f26c54e7e858", "name": "stdout", "source": "d054f26c54e7e858.txt", "type": "text/plain", "size": 11141}], "parameters": [], "attachmentStep": false, "stepsCount": 4, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399587351, "stop": 1754399587351, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399587354, "stop": 1754399588632, "duration": 1278}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_change_your_language_to_chinese"}, {"name": "subSuite", "value": "TestEllaChangeYourLanguageChinese"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_change_your_language_to_chinese"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "1f38cfd1433da4f.json", "parameterValues": []}