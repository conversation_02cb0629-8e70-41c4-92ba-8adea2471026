{"uid": "21072eabbe5acafe", "name": "测试set Battery Saver setting能正常执行", "fullName": "testcases.test_ella.system_coupling.test_set_battery_saver_setting.TestEllaSetBatterySaverSetting#test_set_battery_saver_setting", "historyId": "92c8c8e017b096314ffde2f610a6791e", "time": {"start": 1754450102193, "stop": 1754450125586, "duration": 23393}, "description": "set Battery Saver setting", "descriptionHtml": "<p>set Battery Saver setting</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450089619, "stop": 1754450102192, "duration": 12573}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450102192, "stop": 1754450102192, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "set Battery Saver setting", "status": "passed", "steps": [{"name": "执行命令: set Battery Saver setting", "time": {"start": 1754450102193, "stop": 1754450125386, "duration": 23193}, "status": "passed", "steps": [{"name": "执行命令: set Battery Saver setting", "time": {"start": 1754450102193, "stop": 1754450125161, "duration": 22968}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450125161, "stop": 1754450125386, "duration": 225}, "status": "passed", "steps": [], "attachments": [{"uid": "c1248e8101255c59", "name": "测试总结", "source": "c1248e8101255c59.txt", "type": "text/plain", "size": 239}, {"uid": "24672becc75cc5ae", "name": "test_completed", "source": "24672becc75cc5ae.png", "type": "image/png", "size": 629528}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450125386, "stop": 1754450125387, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450125387, "stop": 1754450125586, "duration": 199}, "status": "passed", "steps": [], "attachments": [{"uid": "1714270dee3c1f1c", "name": "测试总结", "source": "1714270dee3c1f1c.txt", "type": "text/plain", "size": 239}, {"uid": "9a7bd611bab4253b", "name": "test_completed", "source": "9a7bd611bab4253b.png", "type": "image/png", "size": 629289}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "1accead197dc881d", "name": "stdout", "source": "1accead197dc881d.txt", "type": "text/plain", "size": 11900}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450125586, "stop": 1754450125586, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450125587, "stop": 1754450127000, "duration": 1413}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_set_battery_saver_setting"}, {"name": "subSuite", "value": "TestEllaSetBatterySaverSetting"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_set_battery_saver_setting"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "93d302602f21d652", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754400282194, "stop": 1754400303281, "duration": 21087}}], "categories": [], "tags": ["smoke"]}, "source": "21072eabbe5acafe.json", "parameterValues": []}