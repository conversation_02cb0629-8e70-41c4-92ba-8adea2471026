{"uid": "21927a811f40206b", "name": "测试reset phone返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_reset_phone.TestEllaResetPhone#test_reset_phone", "historyId": "1615e8617cafbed9e30baf38018d96b0", "time": {"start": 1754453972515, "stop": 1754453986298, "duration": 13783}, "description": "验证reset phone指令返回预期的不支持响应", "descriptionHtml": "<p>验证reset phone指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453960030, "stop": 1754453972513, "duration": 12483}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453972514, "stop": 1754453972514, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证reset phone指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: reset phone", "time": {"start": 1754453972515, "stop": 1754453986117, "duration": 13602}, "status": "passed", "steps": [{"name": "执行命令: reset phone", "time": {"start": 1754453972515, "stop": 1754453985916, "duration": 13401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453985916, "stop": 1754453986116, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "95d64ffbf2f9e94", "name": "测试总结", "source": "95d64ffbf2f9e94.txt", "type": "text/plain", "size": 207}, {"uid": "1df68ecb23ba44ae", "name": "test_completed", "source": "1df68ecb23ba44ae.png", "type": "image/png", "size": 460869}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453986117, "stop": 1754453986119, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453986119, "stop": 1754453986298, "duration": 179}, "status": "passed", "steps": [], "attachments": [{"uid": "ea29671e0cce0999", "name": "测试总结", "source": "ea29671e0cce0999.txt", "type": "text/plain", "size": 207}, {"uid": "d01be4b8b78224af", "name": "test_completed", "source": "d01be4b8b78224af.png", "type": "image/png", "size": 460962}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "32fa12b2980ddc8c", "name": "stdout", "source": "32fa12b2980ddc8c.txt", "type": "text/plain", "size": 11200}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453986299, "stop": 1754453986299, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453986300, "stop": 1754453987673, "duration": 1373}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_reset_phone"}, {"name": "subSuite", "value": "TestEllaResetPhone"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_reset_phone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "af1298ad3bbd6a52", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403984773, "stop": 1754404001334, "duration": 16561}}], "categories": [], "tags": ["smoke"]}, "source": "21927a811f40206b.json", "parameterValues": []}