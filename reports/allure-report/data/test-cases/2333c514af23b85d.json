{"uid": "2333c514af23b85d", "name": "测试open folax能正常执行", "fullName": "testcases.test_ella.component_coupling.test_open_folax.TestEllaCommandConcise#test_open_folax", "historyId": "9da64d3434f91a12d693ed9c71b62e87", "time": {"start": 1754447006883, "stop": 1754447019377, "duration": 12494}, "description": "open folax", "descriptionHtml": "<p>open folax</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754446994437, "stop": 1754447006882, "duration": 12445}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447006882, "stop": 1754447006882, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "open folax", "status": "passed", "steps": [{"name": "执行命令: open folax", "time": {"start": 1754447006883, "stop": 1754447019161, "duration": 12278}, "status": "passed", "steps": [{"name": "执行命令: open folax", "time": {"start": 1754447006883, "stop": 1754447018951, "duration": 12068}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447018951, "stop": 1754447019161, "duration": 210}, "status": "passed", "steps": [], "attachments": [{"uid": "7d0d63669abd5602", "name": "测试总结", "source": "7d0d63669abd5602.txt", "type": "text/plain", "size": 147}, {"uid": "8dda73c3b4816334", "name": "test_completed", "source": "8dda73c3b4816334.png", "type": "image/png", "size": 562094}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1754447019161, "stop": 1754447019162, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447019162, "stop": 1754447019377, "duration": 215}, "status": "passed", "steps": [], "attachments": [{"uid": "d91317fa0e6d4da3", "name": "测试总结", "source": "d91317fa0e6d4da3.txt", "type": "text/plain", "size": 147}, {"uid": "180043d042ced548", "name": "test_completed", "source": "180043d042ced548.png", "type": "image/png", "size": 562020}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "81ec81c8ae94dba9", "name": "stdout", "source": "81ec81c8ae94dba9.txt", "type": "text/plain", "size": 11248}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447019377, "stop": 1754447019377, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447019378, "stop": 1754447020754, "duration": 1376}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_folax"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_folax"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "878c21bfb8fab771", "status": "passed", "time": {"start": 1754397312634, "stop": 1754397325399, "duration": 12765}}], "categories": [], "tags": ["smoke"]}, "source": "2333c514af23b85d.json", "parameterValues": []}