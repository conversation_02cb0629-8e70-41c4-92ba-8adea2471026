{"uid": "246dc31e75d9eca5", "name": "open clock", "fullName": "testcases.test_ella.component_coupling.test_open_clock.TestEllaCommandConcise#test_open_clock", "historyId": "169e5b613c0fec2cebd053175998bf17", "time": {"start": 1754397151722, "stop": 1754397171255, "duration": 19533}, "description": "使用open clock命令，验证响应包含Done且实际打开clock命令", "descriptionHtml": "<p>使用open clock命令，验证响应包含Done且实际打开clock命令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397139289, "stop": 1754397151721, "duration": 12432}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397151721, "stop": 1754397151721, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "使用open clock命令，验证响应包含Done且实际打开clock命令", "status": "passed", "steps": [{"name": "执行命令: open clock", "time": {"start": 1754397151723, "stop": 1754397170937, "duration": 19214}, "status": "passed", "steps": [{"name": "执行命令: open clock", "time": {"start": 1754397151723, "stop": 1754397170611, "duration": 18888}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397170611, "stop": 1754397170934, "duration": 323}, "status": "passed", "steps": [], "attachments": [{"uid": "7f77b6328d443d48", "name": "测试总结", "source": "7f77b6328d443d48.txt", "type": "text/plain", "size": 237}, {"uid": "c6c3fd7f8cfa2a21", "name": "test_completed", "source": "c6c3fd7f8cfa2a21.png", "type": "image/png", "size": 595701}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含Done", "time": {"start": 1754397170937, "stop": 1754397170942, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证clock已打开", "time": {"start": 1754397170942, "stop": 1754397170942, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397170942, "stop": 1754397171253, "duration": 311}, "status": "passed", "steps": [], "attachments": [{"uid": "ab2769e8c48e6de3", "name": "测试总结", "source": "ab2769e8c48e6de3.txt", "type": "text/plain", "size": 237}, {"uid": "ca69aff81c917cc2", "name": "test_completed", "source": "ca69aff81c917cc2.png", "type": "image/png", "size": 595701}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "8ee94dc5c8792925", "name": "stdout", "source": "8ee94dc5c8792925.txt", "type": "text/plain", "size": 14437}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397171258, "stop": 1754397171258, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397171261, "stop": 1754397172503, "duration": 1242}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "open clock"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_clock"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_clock"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "246dc31e75d9eca5.json", "parameterValues": []}