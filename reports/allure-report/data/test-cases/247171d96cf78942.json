{"uid": "247171d96cf78942", "name": "测试set special function返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_special_function.TestEllaSetSpecialFunction#test_set_special_function", "historyId": "c046a1c6e6cc8effa10641e329b1cfad", "time": {"start": 1754455075910, "stop": 1754455089646, "duration": 13736}, "description": "验证set special function指令返回预期的不支持响应", "descriptionHtml": "<p>验证set special function指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455063506, "stop": 1754455075909, "duration": 12403}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455075910, "stop": 1754455075910, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set special function指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set special function", "time": {"start": 1754455075910, "stop": 1754455089482, "duration": 13572}, "status": "passed", "steps": [{"name": "执行命令: set special function", "time": {"start": 1754455075910, "stop": 1754455089301, "duration": 13391}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455089301, "stop": 1754455089481, "duration": 180}, "status": "passed", "steps": [], "attachments": [{"uid": "578f0c1939c043b", "name": "测试总结", "source": "578f0c1939c043b.txt", "type": "text/plain", "size": 230}, {"uid": "9dc998c940e02018", "name": "test_completed", "source": "9dc998c940e02018.png", "type": "image/png", "size": 489396}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455089482, "stop": 1754455089482, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455089482, "stop": 1754455089645, "duration": 163}, "status": "passed", "steps": [], "attachments": [{"uid": "6bc8a40b968156d1", "name": "测试总结", "source": "6bc8a40b968156d1.txt", "type": "text/plain", "size": 230}, {"uid": "b36f00b03b505ea4", "name": "test_completed", "source": "b36f00b03b505ea4.png", "type": "image/png", "size": 489481}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "8d113bc15340cce8", "name": "stdout", "source": "8d113bc15340cce8.txt", "type": "text/plain", "size": 11312}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455089646, "stop": 1754455089646, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455089647, "stop": 1754455090991, "duration": 1344}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_special_function"}, {"name": "subSuite", "value": "TestEllaSetSpecialFunction"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_special_function"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "5aa37400ba297f61", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405098798, "stop": 1754405115554, "duration": 16756}}], "categories": [], "tags": ["smoke"]}, "source": "247171d96cf78942.json", "parameterValues": []}