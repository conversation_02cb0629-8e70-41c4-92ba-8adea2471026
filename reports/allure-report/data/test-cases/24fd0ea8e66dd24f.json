{"uid": "24fd0ea8e66dd24f", "name": "测试set floating windows返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_floating_windows.TestEllaSetFloatingWindows#test_set_floating_windows", "historyId": "ff945a5d436679bddd13261b231955ec", "time": {"start": 1754454523765, "stop": 1754454537535, "duration": 13770}, "description": "验证set floating windows指令返回预期的不支持响应", "descriptionHtml": "<p>验证set floating windows指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454511395, "stop": 1754454523763, "duration": 12368}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454523764, "stop": 1754454523764, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set floating windows指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set floating windows", "time": {"start": 1754454523765, "stop": 1754454537340, "duration": 13575}, "status": "passed", "steps": [{"name": "执行命令: set floating windows", "time": {"start": 1754454523765, "stop": 1754454537154, "duration": 13389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454537154, "stop": 1754454537340, "duration": 186}, "status": "passed", "steps": [], "attachments": [{"uid": "6944302f864668a1", "name": "测试总结", "source": "6944302f864668a1.txt", "type": "text/plain", "size": 225}, {"uid": "c0266c125b7d5f5a", "name": "test_completed", "source": "c0266c125b7d5f5a.png", "type": "image/png", "size": 487942}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454537340, "stop": 1754454537342, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454537342, "stop": 1754454537534, "duration": 192}, "status": "passed", "steps": [], "attachments": [{"uid": "f020ca8a2a5b7455", "name": "测试总结", "source": "f020ca8a2a5b7455.txt", "type": "text/plain", "size": 225}, {"uid": "27fb33f496a4b853", "name": "test_completed", "source": "27fb33f496a4b853.png", "type": "image/png", "size": 487571}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "527cda29f341a04d", "name": "stdout", "source": "527cda29f341a04d.txt", "type": "text/plain", "size": 11297}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454537536, "stop": 1754454537536, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454537537, "stop": 1754454538942, "duration": 1405}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_floating_windows"}, {"name": "subSuite", "value": "TestEllaSetFloatingWindows"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_floating_windows"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "5274bb6eade20550", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404523008, "stop": 1754404538860, "duration": 15852}}], "categories": [], "tags": ["smoke"]}, "source": "24fd0ea8e66dd24f.json", "parameterValues": []}