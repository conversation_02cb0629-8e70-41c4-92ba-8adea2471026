{"uid": "253154c273af4b74", "name": "测试open facebook能正常执行", "fullName": "testcases.test_ella.third_coupling.test_open_facebook.TestEllaCommandConcise#test_open_facebook", "historyId": "4933b925ec694ecfcd17b3423ac28184", "time": {"start": 1754451557799, "stop": 1754451575932, "duration": 18133}, "description": "open facebook", "descriptionHtml": "<p>open facebook</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451545202, "stop": 1754451557798, "duration": 12596}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451557798, "stop": 1754451557798, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "open facebook", "status": "passed", "steps": [{"name": "执行命令: open facebook", "time": {"start": 1754451557799, "stop": 1754451575729, "duration": 17930}, "status": "passed", "steps": [{"name": "执行命令: open facebook", "time": {"start": 1754451557799, "stop": 1754451575519, "duration": 17720}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451575519, "stop": 1754451575729, "duration": 210}, "status": "passed", "steps": [], "attachments": [{"uid": "84017193ad6daffd", "name": "测试总结", "source": "84017193ad6daffd.txt", "type": "text/plain", "size": 400}, {"uid": "8b1e86e10ed7e32", "name": "test_completed", "source": "8b1e86e10ed7e32.png", "type": "image/png", "size": 440487}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1754451575729, "stop": 1754451575731, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451575731, "stop": 1754451575932, "duration": 201}, "status": "passed", "steps": [], "attachments": [{"uid": "2440bea971ff4ed7", "name": "测试总结", "source": "2440bea971ff4ed7.txt", "type": "text/plain", "size": 400}, {"uid": "8ff9a6f5e68792aa", "name": "test_completed", "source": "8ff9a6f5e68792aa.png", "type": "image/png", "size": 440472}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "a1c843588d6d2002", "name": "stdout", "source": "a1c843588d6d2002.txt", "type": "text/plain", "size": 14867}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451575933, "stop": 1754451575933, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451575934, "stop": 1754451577310, "duration": 1376}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_open_facebook"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_open_facebook"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "cff36dac1ac2240", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754401705877, "stop": 1754401723302, "duration": 17425}}], "categories": [], "tags": ["smoke"]}, "source": "253154c273af4b74.json", "parameterValues": []}