{"uid": "262d606376ce8700", "name": "stop  screen recording能正常执行", "fullName": "testcases.test_ella.system_coupling.test_stop_recording.TestEllaTurnScreenRecord#test_stop_recording", "historyId": "3bafa6d8eb5b49bc5b77f1784275285e", "time": {"start": 1754450389334, "stop": 1754450403774, "duration": 14440}, "description": "stop  screen recording", "descriptionHtml": "<p>stop  screen recording</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450376857, "stop": 1754450389332, "duration": 12475}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450389332, "stop": 1754450389332, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "stop  screen recording", "status": "passed", "steps": [{"name": "执行命令: stop recording", "time": {"start": 1754450389334, "stop": 1754450403560, "duration": 14226}, "status": "passed", "steps": [{"name": "执行命令: stop recording", "time": {"start": 1754450389334, "stop": 1754450403344, "duration": 14010}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450403344, "stop": 1754450403560, "duration": 216}, "status": "passed", "steps": [], "attachments": [{"uid": "f32bee62417a716", "name": "测试总结", "source": "f32bee62417a716.txt", "type": "text/plain", "size": 176}, {"uid": "7149f14f7d5f134e", "name": "test_completed", "source": "7149f14f7d5f134e.png", "type": "image/png", "size": 625793}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450403560, "stop": 1754450403562, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754450403562, "stop": 1754450403562, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证文件存在", "time": {"start": 1754450403562, "stop": 1754450403562, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450403562, "stop": 1754450403774, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "e55c40edcb1b9341", "name": "测试总结", "source": "e55c40edcb1b9341.txt", "type": "text/plain", "size": 176}, {"uid": "e5a5daf817b4aaf9", "name": "test_completed", "source": "e5a5daf817b4aaf9.png", "type": "image/png", "size": 627061}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "61b94a26043baf7c", "name": "stdout", "source": "61b94a26043baf7c.txt", "type": "text/plain", "size": 12232}], "parameters": [], "attachmentStep": false, "stepsCount": 7, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450403775, "stop": 1754450403775, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450403776, "stop": 1754450405149, "duration": 1373}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_stop_recording"}, {"name": "subSuite", "value": "TestEllaTurnScreenRecord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_stop_recording"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "57fc86b27e49842c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording finished']\nassert False", "time": {"start": 1754400558088, "stop": 1754400569708, "duration": 11620}}], "categories": [], "tags": ["smoke"]}, "source": "262d606376ce8700.json", "parameterValues": []}