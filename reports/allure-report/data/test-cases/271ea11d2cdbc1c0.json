{"uid": "271ea11d2cdbc1c0", "name": "测试close whatsapp能正常执行", "fullName": "testcases.test_ella.dialogue.test_close_whatsapp.TestEllaCloseWhatsapp#test_close_whatsapp", "historyId": "876e77318cece5d1079b726f0c97bc45", "time": {"start": 1754398017912, "stop": 1754398032366, "duration": 14454}, "description": "close whatsapp", "descriptionHtml": "<p>close whatsapp</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398004922, "stop": 1754398017911, "duration": 12989}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398017911, "stop": 1754398017911, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "close whatsapp", "status": "passed", "steps": [{"name": "执行命令: close whatsapp", "time": {"start": 1754398017912, "stop": 1754398032133, "duration": 14221}, "status": "passed", "steps": [{"name": "执行命令: close whatsapp", "time": {"start": 1754398017912, "stop": 1754398031877, "duration": 13965}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398031877, "stop": 1754398032131, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "31f57a80c11e0746", "name": "测试总结", "source": "31f57a80c11e0746.txt", "type": "text/plain", "size": 154}, {"uid": "442624b1c25fcfae", "name": "test_completed", "source": "442624b1c25fcfae.png", "type": "image/png", "size": 496813}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398032133, "stop": 1754398032137, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398032137, "stop": 1754398032365, "duration": 228}, "status": "passed", "steps": [], "attachments": [{"uid": "d8b0bcf35e16b523", "name": "测试总结", "source": "d8b0bcf35e16b523.txt", "type": "text/plain", "size": 154}, {"uid": "c4285720bce8c54d", "name": "test_completed", "source": "c4285720bce8c54d.png", "type": "image/png", "size": 495911}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d5080765a31c2ff4", "name": "stdout", "source": "d5080765a31c2ff4.txt", "type": "text/plain", "size": 11460}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398032368, "stop": 1754398032368, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398032370, "stop": 1754398033592, "duration": 1222}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_close_whatsapp"}, {"name": "subSuite", "value": "TestEllaCloseWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_close_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "271ea11d2cdbc1c0.json", "parameterValues": []}