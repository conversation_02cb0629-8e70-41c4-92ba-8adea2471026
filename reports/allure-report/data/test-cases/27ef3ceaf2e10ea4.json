{"uid": "27ef3ceaf2e10ea4", "name": "测试switch to power saving mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_switch_to_power_saving_mode.TestEllaSwitchPowerSavingMode#test_switch_to_power_saving_mode", "historyId": "a0efcebc4cee6024e690bd290b4f3fbb", "time": {"start": 1754455296573, "stop": 1754455311690, "duration": 15117}, "description": "验证switch to power saving mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证switch to power saving mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455284080, "stop": 1754455296572, "duration": 12492}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455296572, "stop": 1754455296572, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证switch to power saving mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "time": {"start": 1754455296573, "stop": 1754455311501, "duration": 14928}, "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "time": {"start": 1754455296573, "stop": 1754455311282, "duration": 14709}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455311282, "stop": 1754455311500, "duration": 218}, "status": "passed", "steps": [], "attachments": [{"uid": "b01bb408bceafc7f", "name": "测试总结", "source": "b01bb408bceafc7f.txt", "type": "text/plain", "size": 251}, {"uid": "f347e2598f19fb7a", "name": "test_completed", "source": "f347e2598f19fb7a.png", "type": "image/png", "size": 502925}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455311501, "stop": 1754455311503, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455311503, "stop": 1754455311689, "duration": 186}, "status": "passed", "steps": [], "attachments": [{"uid": "904fd4f3e9ec0333", "name": "测试总结", "source": "904fd4f3e9ec0333.txt", "type": "text/plain", "size": 251}, {"uid": "971ed3ada8616933", "name": "test_completed", "source": "971ed3ada8616933.png", "type": "image/png", "size": 503311}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "343c35f4a737aac2", "name": "stdout", "source": "343c35f4a737aac2.txt", "type": "text/plain", "size": 11932}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455311690, "stop": 1754455311690, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455311691, "stop": 1754455313071, "duration": 1380}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switch_to_power_saving_mode"}, {"name": "subSuite", "value": "TestEllaSwitchPowerSavingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switch_to_power_saving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "f76ef410d43dcd0", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405338253, "stop": 1754405349424, "duration": 11171}}], "categories": [], "tags": ["smoke"]}, "source": "27ef3ceaf2e10ea4.json", "parameterValues": []}