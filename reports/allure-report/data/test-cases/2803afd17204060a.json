{"uid": "2803afd17204060a", "name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_ai_wallpaper_generator_settings.TestEllaJumpAiWallpaperGeneratorSettings#test_jump_to_ai_wallpaper_generator_settings", "historyId": "f1bf796cd6804ca9b19a3e3f949a04ba", "time": {"start": 1754453129165, "stop": 1754453152341, "duration": 23176}, "description": "验证jump to ai wallpaper generator settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to ai wallpaper generator settings指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453117078, "stop": 1754453129163, "duration": 12085}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453129163, "stop": 1754453129163, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证jump to ai wallpaper generator settings指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: jump to ai wallpaper generator settings", "time": {"start": 1754453129165, "stop": 1754453152148, "duration": 22983}, "status": "passed", "steps": [{"name": "执行命令: jump to ai wallpaper generator settings", "time": {"start": 1754453129165, "stop": 1754453151954, "duration": 22789}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453151954, "stop": 1754453152148, "duration": 194}, "status": "passed", "steps": [], "attachments": [{"uid": "297b5026719e3495", "name": "测试总结", "source": "297b5026719e3495.txt", "type": "text/plain", "size": 276}, {"uid": "e0d2cac281b3eaa3", "name": "test_completed", "source": "e0d2cac281b3eaa3.png", "type": "image/png", "size": 521605}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453152148, "stop": 1754453152149, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453152149, "stop": 1754453152341, "duration": 192}, "status": "passed", "steps": [], "attachments": [{"uid": "102c7af97b2d3de1", "name": "测试总结", "source": "102c7af97b2d3de1.txt", "type": "text/plain", "size": 276}, {"uid": "bc9d4d0b2fa7bb32", "name": "test_completed", "source": "bc9d4d0b2fa7bb32.png", "type": "image/png", "size": 521958}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "738079a5d59ec83d", "name": "stdout", "source": "738079a5d59ec83d.txt", "type": "text/plain", "size": 11942}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453152342, "stop": 1754453152342, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453152343, "stop": 1754453153671, "duration": 1328}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_ai_wallpaper_generator_settings"}, {"name": "subSuite", "value": "TestEllaJumpAiWallpaperGeneratorSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_ai_wallpaper_generator_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "b7c0a432afe503a5", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403147273, "stop": 1754403168331, "duration": 21058}}], "categories": [], "tags": ["smoke"]}, "source": "2803afd17204060a.json", "parameterValues": []}