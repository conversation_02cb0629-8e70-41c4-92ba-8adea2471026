{"uid": "2847f36547f4565c", "name": "测试Adjustment the brightness to 50%能正常执行", "fullName": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to.TestEllaAdjustmentBrightness#test_adjustment_the_brightness_to", "historyId": "ad18e983dce31052b87b7404f3b347ce", "time": {"start": 1754449351011, "stop": 1754449364997, "duration": 13986}, "description": "Adjustment the brightness to 50%", "descriptionHtml": "<p>Adjustment the brightness to 50%</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449338286, "stop": 1754449351010, "duration": 12724}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449351010, "stop": 1754449351010, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "Adjustment the brightness to 50%", "status": "passed", "steps": [{"name": "执行命令: Adjustment the brightness to 50%", "time": {"start": 1754449351011, "stop": 1754449364804, "duration": 13793}, "status": "passed", "steps": [{"name": "执行命令: Adjustment the brightness to 50%", "time": {"start": 1754449351011, "stop": 1754449364598, "duration": 13587}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449364598, "stop": 1754449364804, "duration": 206}, "status": "passed", "steps": [], "attachments": [{"uid": "18e0fd22f0f4c740", "name": "测试总结", "source": "18e0fd22f0f4c740.txt", "type": "text/plain", "size": 207}, {"uid": "5a0b31fefe02f781", "name": "test_completed", "source": "5a0b31fefe02f781.png", "type": "image/png", "size": 532422}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754449364804, "stop": 1754449364806, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754449364806, "stop": 1754449364806, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449364806, "stop": 1754449364996, "duration": 190}, "status": "passed", "steps": [], "attachments": [{"uid": "640cb79175db26af", "name": "测试总结", "source": "640cb79175db26af.txt", "type": "text/plain", "size": 207}, {"uid": "e10f3e5b71604539", "name": "test_completed", "source": "e10f3e5b71604539.png", "type": "image/png", "size": 532528}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "69f07e583bcbb5df", "name": "stdout", "source": "69f07e583bcbb5df.txt", "type": "text/plain", "size": 12143}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449364997, "stop": 1754449364997, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449364999, "stop": 1754449366428, "duration": 1429}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_adjustment_the_brightness_to"}, {"name": "subSuite", "value": "TestEllaAdjustmentBrightness"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "33fac653fa84c3ee", "status": "passed", "time": {"start": 1754399548504, "stop": 1754399561536, "duration": 13032}}], "categories": [], "tags": ["smoke"]}, "source": "2847f36547f4565c.json", "parameterValues": []}