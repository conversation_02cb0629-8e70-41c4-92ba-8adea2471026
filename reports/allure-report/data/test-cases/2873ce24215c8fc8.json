{"uid": "2873ce24215c8fc8", "name": "测试help me write an email能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_write_an_email.TestEllaHelpMeWriteAnEmail#test_help_me_write_an_email", "historyId": "70c9bd8c4aab57e96eb06acb93ca2223", "time": {"start": 1754402960040, "stop": 1754402973051, "duration": 13011}, "description": "help me write an email", "descriptionHtml": "<p>help me write an email</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754402947250, "stop": 1754402960039, "duration": 12789}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754402960039, "stop": 1754402960039, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "help me write an email", "status": "passed", "steps": [{"name": "执行命令: help me write an email", "time": {"start": 1754402960040, "stop": 1754402972748, "duration": 12708}, "status": "passed", "steps": [{"name": "执行命令: help me write an email", "time": {"start": 1754402960040, "stop": 1754402972463, "duration": 12423}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754402972463, "stop": 1754402972747, "duration": 284}, "status": "passed", "steps": [], "attachments": [{"uid": "26f18acc98821b95", "name": "测试总结", "source": "26f18acc98821b95.txt", "type": "text/plain", "size": 679}, {"uid": "c3e59129a06fbc9a", "name": "test_completed", "source": "c3e59129a06fbc9a.png", "type": "image/png", "size": 737792}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754402972748, "stop": 1754402972752, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754402972752, "stop": 1754402973050, "duration": 298}, "status": "passed", "steps": [], "attachments": [{"uid": "4157382c1d7ad448", "name": "测试总结", "source": "4157382c1d7ad448.txt", "type": "text/plain", "size": 679}, {"uid": "2d9dfbe5f9d40fdf", "name": "test_completed", "source": "2d9dfbe5f9d40fdf.png", "type": "image/png", "size": 738100}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "bb2e26e29d16ec11", "name": "stdout", "source": "bb2e26e29d16ec11.txt", "type": "text/plain", "size": 14341}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754402973053, "stop": 1754402973053, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754402973056, "stop": 1754402974349, "duration": 1293}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_write_an_email"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnEmail"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_write_an_email"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "2873ce24215c8fc8.json", "parameterValues": []}