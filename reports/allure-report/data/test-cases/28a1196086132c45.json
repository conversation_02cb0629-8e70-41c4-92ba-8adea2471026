{"uid": "28a1196086132c45", "name": "测试switch to power saving mode能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switch_to_power_saving_mode.TestEllaSwitchToPowerSavingMode#test_switch_to_power_saving_mode", "historyId": "5dff8ffa0041df33df80919398086e48", "time": {"start": 1754450686274, "stop": 1754450701168, "duration": 14894}, "description": "switch to power saving mode", "descriptionHtml": "<p>switch to power saving mode</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450673459, "stop": 1754450686274, "duration": 12815}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450686274, "stop": 1754450686274, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "switch to power saving mode", "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "time": {"start": 1754450686274, "stop": 1754450700963, "duration": 14689}, "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "time": {"start": 1754450686275, "stop": 1754450700752, "duration": 14477}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450700752, "stop": 1754450700963, "duration": 211}, "status": "passed", "steps": [], "attachments": [{"uid": "214cb242e6e42e89", "name": "测试总结", "source": "214cb242e6e42e89.txt", "type": "text/plain", "size": 251}, {"uid": "5e16117347dc49ad", "name": "test_completed", "source": "5e16117347dc49ad.png", "type": "image/png", "size": 649196}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1754450700963, "stop": 1754450700964, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450700964, "stop": 1754450701168, "duration": 204}, "status": "passed", "steps": [], "attachments": [{"uid": "3adae3c61887e65c", "name": "测试总结", "source": "3adae3c61887e65c.txt", "type": "text/plain", "size": 251}, {"uid": "b1cd1746ec63c407", "name": "test_completed", "source": "b1cd1746ec63c407.png", "type": "image/png", "size": 649271}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "4ccef32df45ea1d", "name": "stdout", "source": "4ccef32df45ea1d.txt", "type": "text/plain", "size": 12065}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450701168, "stop": 1754450701168, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450701170, "stop": 1754450702585, "duration": 1415}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_power_saving_mode"}, {"name": "subSuite", "value": "TestEllaSwitchToPowerSavingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_power_saving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "cf90ef5fa79481d6", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754400847767, "stop": 1754400859116, "duration": 11349}}], "categories": [], "tags": ["smoke"]}, "source": "28a1196086132c45.json", "parameterValues": []}