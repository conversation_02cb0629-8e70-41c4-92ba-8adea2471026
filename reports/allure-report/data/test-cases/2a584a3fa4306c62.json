{"uid": "2a584a3fa4306c62", "name": "测试navigation to the address in thie image能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_navigation_to_the_address_in_the_image.TestEllaNavigationAddressTheImage#test_navigation_to_the_address_in_the_image", "historyId": "488c24d02f5d5348f35881280e505f32", "time": {"start": 1754403506408, "stop": 1754403520174, "duration": 13766}, "description": "navigation to the address in thie image", "descriptionHtml": "<p>navigation to the address in thie image</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754403493164, "stop": 1754403506407, "duration": 13243}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754403506407, "stop": 1754403506407, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "navigation to the address in thie image", "status": "passed", "steps": [{"name": "执行命令: navigation to the address in thie image", "time": {"start": 1754403506408, "stop": 1754403519866, "duration": 13458}, "status": "passed", "steps": [{"name": "执行命令: navigation to the address in thie image", "time": {"start": 1754403506408, "stop": 1754403519595, "duration": 13187}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754403519595, "stop": 1754403519865, "duration": 270}, "status": "passed", "steps": [], "attachments": [{"uid": "e8986d18b51d6d0a", "name": "测试总结", "source": "e8986d18b51d6d0a.txt", "type": "text/plain", "size": 1350}, {"uid": "d139ab438f7d9273", "name": "test_completed", "source": "d139ab438f7d9273.png", "type": "image/png", "size": 698473}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证GoogleMap应用已打开", "time": {"start": 1754403519866, "stop": 1754403519866, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754403519866, "stop": 1754403520173, "duration": 307}, "status": "passed", "steps": [], "attachments": [{"uid": "43764f802ab3d74a", "name": "测试总结", "source": "43764f802ab3d74a.txt", "type": "text/plain", "size": 1350}, {"uid": "966c7933b197080", "name": "test_completed", "source": "966c7933b197080.png", "type": "image/png", "size": 698077}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "cc1e6f311d41ed84", "name": "stdout", "source": "cc1e6f311d41ed84.txt", "type": "text/plain", "size": 16301}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754403520175, "stop": 1754403520175, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754403520177, "stop": 1754403521455, "duration": 1278}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_navigation_to_the_address_in_the_image"}, {"name": "subSuite", "value": "TestEllaNavigationAddressTheImage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_navigation_to_the_address_in_the_image"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "2a584a3fa4306c62.json", "parameterValues": []}