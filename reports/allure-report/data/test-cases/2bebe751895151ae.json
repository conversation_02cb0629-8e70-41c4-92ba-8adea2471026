{"uid": "2bebe751895151ae", "name": "测试What languages do you support能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_languages_do_you_support.TestEllaWhatLanguagesDoYouSupport#test_what_languages_do_you_support", "historyId": "0c44c94f08feed70addcec44e96bda5a", "time": {"start": 1754399141966, "stop": 1754399155267, "duration": 13301}, "description": "What languages do you support", "descriptionHtml": "<p>What languages do you support</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399129033, "stop": 1754399141965, "duration": 12932}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399141965, "stop": 1754399141965, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "What languages do you support", "status": "passed", "steps": [{"name": "执行命令: What languages do you support", "time": {"start": 1754399141966, "stop": 1754399154954, "duration": 12988}, "status": "passed", "steps": [{"name": "执行命令: What languages do you support", "time": {"start": 1754399141966, "stop": 1754399154633, "duration": 12667}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399154633, "stop": 1754399154951, "duration": 318}, "status": "passed", "steps": [], "attachments": [{"uid": "3e25627fc07ed4e2", "name": "测试总结", "source": "3e25627fc07ed4e2.txt", "type": "text/plain", "size": 322}, {"uid": "22fd9cca910c3114", "name": "test_completed", "source": "22fd9cca910c3114.png", "type": "image/png", "size": 586117}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399154954, "stop": 1754399154964, "duration": 10}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399154964, "stop": 1754399155266, "duration": 302}, "status": "passed", "steps": [], "attachments": [{"uid": "77d5f71244ea178d", "name": "测试总结", "source": "77d5f71244ea178d.txt", "type": "text/plain", "size": 322}, {"uid": "2b1988f4135a8e36", "name": "test_completed", "source": "2b1988f4135a8e36.png", "type": "image/png", "size": 585425}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d139b700b7a34138", "name": "stdout", "source": "d139b700b7a34138.txt", "type": "text/plain", "size": 11766}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399155268, "stop": 1754399155268, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399155271, "stop": 1754399156563, "duration": 1292}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_languages_do_you_support"}, {"name": "subSuite", "value": "TestEllaWhatLanguagesDoYouSupport"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_languages_do_you_support"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "2bebe751895151ae.json", "parameterValues": []}