{"uid": "2c63a150dba92152", "name": "测试open contact命令", "fullName": "testcases.test_ella.component_coupling.test_open_phone.TestEllaContactCommandConcise#test_open_phone", "historyId": "acdb323f998d6127fbebbc545f6e8a59", "time": {"start": 1754447033146, "stop": 1754447056127, "duration": 22981}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447020763, "stop": 1754447033145, "duration": 12382}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447033145, "stop": 1754447033145, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "passed", "steps": [{"name": "执行命令: open phone", "time": {"start": 1754447033147, "stop": 1754447055902, "duration": 22755}, "status": "passed", "steps": [{"name": "执行命令: open phone", "time": {"start": 1754447033147, "stop": 1754447055658, "duration": 22511}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447055658, "stop": 1754447055901, "duration": 243}, "status": "passed", "steps": [], "attachments": [{"uid": "94763025d2ba8566", "name": "测试总结", "source": "94763025d2ba8566.txt", "type": "text/plain", "size": 204}, {"uid": "7c9fc41c34c3f6c3", "name": "test_completed", "source": "7c9fc41c34c3f6c3.png", "type": "image/png", "size": 556776}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含Done", "time": {"start": 1754447055902, "stop": 1754447055903, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证Dalier应用已打开", "time": {"start": 1754447055903, "stop": 1754447055903, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447055903, "stop": 1754447056127, "duration": 224}, "status": "passed", "steps": [], "attachments": [{"uid": "8bc5d78e7a0c3d0a", "name": "测试总结", "source": "8bc5d78e7a0c3d0a.txt", "type": "text/plain", "size": 204}, {"uid": "9a55dbd9c2319d14", "name": "test_completed", "source": "9a55dbd9c2319d14.png", "type": "image/png", "size": 557406}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "1af7e5b4f768a149", "name": "stdout", "source": "1af7e5b4f768a149.txt", "type": "text/plain", "size": 14368}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447056128, "stop": 1754447056128, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447056129, "stop": 1754447057536, "duration": 1407}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "联系人控制命令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_phone"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_phone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "c0a8be39dfce2faf", "status": "passed", "time": {"start": 1754397339907, "stop": 1754397363132, "duration": 23225}}], "categories": [], "tags": ["smoke"]}, "source": "2c63a150dba92152.json", "parameterValues": []}