{"uid": "2c78a1a091cf6fe5", "name": "测试the battery of the mobile phone is too low能正常执行", "fullName": "testcases.test_ella.system_coupling.test_the_battery_of_the_mobile_phone_is_too_low.TestEllaBatteryMobilePhoneIsTooLow#test_the_battery_of_the_mobile_phone_is_too_low", "historyId": "2ecac23eb3f511651fafc6ba6a3725f2", "time": {"start": 1754450861763, "stop": 1754450877680, "duration": 15917}, "description": "the battery of the mobile phone is too low", "descriptionHtml": "<p>the battery of the mobile phone is too low</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450849390, "stop": 1754450861762, "duration": 12372}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450861762, "stop": 1754450861762, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "the battery of the mobile phone is too low", "status": "passed", "steps": [{"name": "执行命令: the battery of the mobile phone is too low", "time": {"start": 1754450861763, "stop": 1754450877466, "duration": 15703}, "status": "passed", "steps": [{"name": "执行命令: the battery of the mobile phone is too low", "time": {"start": 1754450861763, "stop": 1754450877247, "duration": 15484}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450877247, "stop": 1754450877466, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "5c8ba99565524e0", "name": "测试总结", "source": "5c8ba99565524e0.txt", "type": "text/plain", "size": 333}, {"uid": "8d1f00de03d363bc", "name": "test_completed", "source": "8d1f00de03d363bc.png", "type": "image/png", "size": 622525}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450877467, "stop": 1754450877468, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证PhoneMaster应用已打开", "time": {"start": 1754450877468, "stop": 1754450877468, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450877468, "stop": 1754450877680, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "ad00800afeea77c2", "name": "测试总结", "source": "ad00800afeea77c2.txt", "type": "text/plain", "size": 333}, {"uid": "77f66325a31ac26f", "name": "test_completed", "source": "77f66325a31ac26f.png", "type": "image/png", "size": 622525}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "7fe917224acca36c", "name": "stdout", "source": "7fe917224acca36c.txt", "type": "text/plain", "size": 14856}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450877681, "stop": 1754450877681, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450877682, "stop": 1754450879000, "duration": 1318}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_the_battery_of_the_mobile_phone_is_too_low"}, {"name": "subSuite", "value": "TestEllaBatteryMobilePhoneIsTooLow"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_the_battery_of_the_mobile_phone_is_too_low"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "c20cff1223132437", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['done']\nassert False", "time": {"start": 1754401013093, "stop": 1754401037140, "duration": 24047}}], "categories": [], "tags": ["smoke"]}, "source": "2c78a1a091cf6fe5.json", "parameterValues": []}