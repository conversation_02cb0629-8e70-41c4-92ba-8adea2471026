{"uid": "2cb8ed69ebde6522", "name": "测试what's the weather like in shanghai today能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_s_the_weather_like_in_shanghai_today.TestEllaWhatSWeatherLikeShanghaiToday#test_what_s_the_weather_like_in_shanghai_today", "historyId": "b60d4c80cd1df80873da3fea78736e6a", "time": {"start": 1754399169453, "stop": 1754399189388, "duration": 19935}, "description": "what's the weather like in shanghai today", "descriptionHtml": "<p>what's the weather like in shanghai today</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399156568, "stop": 1754399169451, "duration": 12883}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399169451, "stop": 1754399169451, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "what's the weather like in shanghai today", "status": "passed", "steps": [{"name": "执行命令: what's the weather like in shanghai today", "time": {"start": 1754399169453, "stop": 1754399189113, "duration": 19660}, "status": "passed", "steps": [{"name": "执行命令: what's the weather like in shanghai today", "time": {"start": 1754399169453, "stop": 1754399188880, "duration": 19427}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399188880, "stop": 1754399189112, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "3e7bdf2389653638", "name": "测试总结", "source": "3e7bdf2389653638.txt", "type": "text/plain", "size": 276}, {"uid": "737884a00a61d0f3", "name": "test_completed", "source": "737884a00a61d0f3.png", "type": "image/png", "size": 502502}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399189113, "stop": 1754399189117, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399189117, "stop": 1754399189387, "duration": 270}, "status": "passed", "steps": [], "attachments": [{"uid": "474b3632dcc8159f", "name": "测试总结", "source": "474b3632dcc8159f.txt", "type": "text/plain", "size": 276}, {"uid": "7b3a98e5222758a8", "name": "test_completed", "source": "7b3a98e5222758a8.png", "type": "image/png", "size": 502502}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "7de7b45394e5c597", "name": "stdout", "source": "7de7b45394e5c597.txt", "type": "text/plain", "size": 11960}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399189389, "stop": 1754399189389, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399189392, "stop": 1754399190624, "duration": 1232}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_s_the_weather_like_in_shanghai_today"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherLikeShanghaiToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_s_the_weather_like_in_shanghai_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "2cb8ed69ebde6522.json", "parameterValues": []}