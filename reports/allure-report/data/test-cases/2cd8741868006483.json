{"uid": "2cd8741868006483", "name": "测试hi能正常执行", "fullName": "testcases.test_ella.dialogue.test_hi.TestEllaHi#test_hi", "historyId": "18415b75388fbfdac9a7e4232373c000", "time": {"start": 1754447955355, "stop": 1754447970221, "duration": 14866}, "description": "hi", "descriptionHtml": "<p>hi</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447942690, "stop": 1754447955354, "duration": 12664}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447955354, "stop": 1754447955354, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "hi", "status": "passed", "steps": [{"name": "执行命令: hi", "time": {"start": 1754447955355, "stop": 1754447970036, "duration": 14681}, "status": "passed", "steps": [{"name": "执行命令: hi", "time": {"start": 1754447955355, "stop": 1754447969838, "duration": 14483}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447969838, "stop": 1754447970036, "duration": 198}, "status": "passed", "steps": [], "attachments": [{"uid": "2a1fa06c09245c64", "name": "测试总结", "source": "2a1fa06c09245c64.txt", "type": "text/plain", "size": 527}, {"uid": "b79a27c7eef74f5b", "name": "test_completed", "source": "b79a27c7eef74f5b.png", "type": "image/png", "size": 566498}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447970036, "stop": 1754447970038, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447970038, "stop": 1754447970221, "duration": 183}, "status": "passed", "steps": [], "attachments": [{"uid": "66b43b1209fb70b2", "name": "测试总结", "source": "66b43b1209fb70b2.txt", "type": "text/plain", "size": 527}, {"uid": "5221f79ed13ee1df", "name": "test_completed", "source": "5221f79ed13ee1df.png", "type": "image/png", "size": 566718}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "acd88b6898d0b536", "name": "stdout", "source": "acd88b6898d0b536.txt", "type": "text/plain", "size": 13325}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447970222, "stop": 1754447970222, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447970227, "stop": 1754447971632, "duration": 1405}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_hi"}, {"name": "subSuite", "value": "TestEllaHi"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_hi"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "3c57688e4eca8e05", "status": "passed", "time": {"start": 1754398228333, "stop": 1754398242164, "duration": 13831}}], "categories": [], "tags": ["smoke"]}, "source": "2cd8741868006483.json", "parameterValues": []}