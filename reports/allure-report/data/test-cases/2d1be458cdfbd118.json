{"uid": "2d1be458cdfbd118", "name": "测试set phone number返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_phone_number.TestEllaSetPhoneNumber#test_set_phone_number", "historyId": "8e367b8da758818b9a0fe21deca7ec48", "time": {"start": 1754454829931, "stop": 1754454843704, "duration": 13773}, "description": "验证set phone number指令返回预期的不支持响应", "descriptionHtml": "<p>验证set phone number指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454817942, "stop": 1754454829931, "duration": 11989}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454829931, "stop": 1754454829931, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set phone number指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set phone number", "time": {"start": 1754454829933, "stop": 1754454843529, "duration": 13596}, "status": "passed", "steps": [{"name": "执行命令: set phone number", "time": {"start": 1754454829933, "stop": 1754454843329, "duration": 13396}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454843329, "stop": 1754454843529, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "e2c3bc874c96c844", "name": "测试总结", "source": "e2c3bc874c96c844.txt", "type": "text/plain", "size": 223}, {"uid": "43ccb9e02782807", "name": "test_completed", "source": "43ccb9e02782807.png", "type": "image/png", "size": 487795}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454843529, "stop": 1754454843530, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454843530, "stop": 1754454843704, "duration": 174}, "status": "passed", "steps": [], "attachments": [{"uid": "97c93ae9d8760f36", "name": "测试总结", "source": "97c93ae9d8760f36.txt", "type": "text/plain", "size": 223}, {"uid": "f5e26039797c4d8f", "name": "test_completed", "source": "f5e26039797c4d8f.png", "type": "image/png", "size": 487913}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "dc6207474db48d48", "name": "stdout", "source": "dc6207474db48d48.txt", "type": "text/plain", "size": 11271}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454843705, "stop": 1754454843705, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454843706, "stop": 1754454845010, "duration": 1304}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_phone_number"}, {"name": "subSuite", "value": "TestEllaSetPhoneNumber"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_phone_number"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "32cf0933330af47f", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404834369, "stop": 1754404845296, "duration": 10927}}], "categories": [], "tags": ["smoke"]}, "source": "2d1be458cdfbd118.json", "parameterValues": []}