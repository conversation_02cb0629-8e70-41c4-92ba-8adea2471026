{"uid": "2d3253e0c7e1606d", "name": "测试close ella能正常执行", "fullName": "testcases.test_ella.component_coupling.test_close_ella.TestEllaCloseElla#test_close_ella", "historyId": "54b47105d42d2a9f18eec071fba40c73", "time": {"start": 1754446522764, "stop": 1754446555185, "duration": 32421}, "description": "close ella", "descriptionHtml": "<p>close ella</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754446510301, "stop": 1754446522763, "duration": 12462}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754446522763, "stop": 1754446522763, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "close ella", "status": "passed", "steps": [{"name": "执行命令: close ella", "time": {"start": 1754446522764, "stop": 1754446554979, "duration": 32215}, "status": "passed", "steps": [{"name": "执行命令: close ella", "time": {"start": 1754446522764, "stop": 1754446554745, "duration": 31981}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446554745, "stop": 1754446554979, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "546d25c268852b20", "name": "测试总结", "source": "546d25c268852b20.txt", "type": "text/plain", "size": 669}, {"uid": "461b529c60ebb361", "name": "test_completed", "source": "461b529c60ebb361.png", "type": "image/png", "size": 502620}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证已打开", "time": {"start": 1754446554979, "stop": 1754446554979, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446554979, "stop": 1754446555185, "duration": 206}, "status": "passed", "steps": [], "attachments": [{"uid": "a53b9743f160f7ea", "name": "测试总结", "source": "a53b9743f160f7ea.txt", "type": "text/plain", "size": 669}, {"uid": "745e0530d8886e16", "name": "test_completed", "source": "745e0530d8886e16.png", "type": "image/png", "size": 502580}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "936aeb2c7376972f", "name": "stdout", "source": "936aeb2c7376972f.txt", "type": "text/plain", "size": 18965}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754446555186, "stop": 1754446555186, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754446555187, "stop": 1754446556585, "duration": 1398}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_ella"}, {"name": "subSuite", "value": "TestEllaClose<PERSON>lla"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_ella"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "ca1a3155aaeee8a9", "status": "passed", "time": {"start": 1754396835533, "stop": 1754396865403, "duration": 29870}}], "categories": [], "tags": ["smoke"]}, "source": "2d3253e0c7e1606d.json", "parameterValues": []}