{"uid": "2d413365c9e8c32f", "name": "测试hello hello能正常执行", "fullName": "testcases.test_ella.dialogue.test_hello_hello.TestEllaHelloHello#test_hello_hello", "historyId": "b3fa1d22b59def5f059cd9b0eefbe2b0", "time": {"start": 1754447898223, "stop": 1754447913407, "duration": 15184}, "description": "hello hello", "descriptionHtml": "<p>hello hello</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447885410, "stop": 1754447898222, "duration": 12812}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447898222, "stop": 1754447898222, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "hello hello", "status": "passed", "steps": [{"name": "执行命令: hello hello", "time": {"start": 1754447898223, "stop": 1754447913190, "duration": 14967}, "status": "passed", "steps": [{"name": "执行命令: hello hello", "time": {"start": 1754447898223, "stop": 1754447912960, "duration": 14737}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447912960, "stop": 1754447913190, "duration": 230}, "status": "passed", "steps": [], "attachments": [{"uid": "1e02b24ad308da7c", "name": "测试总结", "source": "1e02b24ad308da7c.txt", "type": "text/plain", "size": 554}, {"uid": "cf2e968b405e54f2", "name": "test_completed", "source": "cf2e968b405e54f2.png", "type": "image/png", "size": 576044}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447913190, "stop": 1754447913192, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447913192, "stop": 1754447913407, "duration": 215}, "status": "passed", "steps": [], "attachments": [{"uid": "92c99c2715ef74ff", "name": "测试总结", "source": "92c99c2715ef74ff.txt", "type": "text/plain", "size": 554}, {"uid": "d92349585fa5668b", "name": "test_completed", "source": "d92349585fa5668b.png", "type": "image/png", "size": 574997}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "2fbcba54a16ec340", "name": "stdout", "source": "2fbcba54a16ec340.txt", "type": "text/plain", "size": 13433}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447913408, "stop": 1754447913408, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447913409, "stop": 1754447914792, "duration": 1383}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_hello_hello"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_hello_hello"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "596722b4afad1463", "status": "passed", "time": {"start": 1754398173047, "stop": 1754398186506, "duration": 13459}}], "categories": [], "tags": ["smoke"]}, "source": "2d413365c9e8c32f.json", "parameterValues": []}