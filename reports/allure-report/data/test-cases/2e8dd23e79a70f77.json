{"uid": "2e8dd23e79a70f77", "name": "测试summarize what i'm reading能正常执行", "fullName": "testcases.test_ella.dialogue.test_summarize_what_i_m_reading.TestEllaSummarizeWhatIMReading#test_summarize_what_i_m_reading", "historyId": "edb3a77ed85c79b290dd8cce24f372c0", "time": {"start": 1754398937975, "stop": 1754398951009, "duration": 13034}, "description": "summarize what i'm reading", "descriptionHtml": "<p>summarize what i'm reading</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398925027, "stop": 1754398937974, "duration": 12947}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398937974, "stop": 1754398937974, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "summarize what i'm reading", "status": "passed", "steps": [{"name": "执行命令: summarize what i'm reading", "time": {"start": 1754398937975, "stop": 1754398950742, "duration": 12767}, "status": "passed", "steps": [{"name": "执行命令: summarize what i'm reading", "time": {"start": 1754398937975, "stop": 1754398950453, "duration": 12478}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398950453, "stop": 1754398950740, "duration": 287}, "status": "passed", "steps": [], "attachments": [{"uid": "344906105f89f844", "name": "测试总结", "source": "344906105f89f844.txt", "type": "text/plain", "size": 269}, {"uid": "281ef663da490635", "name": "test_completed", "source": "281ef663da490635.png", "type": "image/png", "size": 615245}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398950742, "stop": 1754398950744, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398950744, "stop": 1754398951007, "duration": 263}, "status": "passed", "steps": [], "attachments": [{"uid": "e4553e572462e351", "name": "测试总结", "source": "e4553e572462e351.txt", "type": "text/plain", "size": 269}, {"uid": "a0399e7904561cfe", "name": "test_completed", "source": "a0399e7904561cfe.png", "type": "image/png", "size": 615280}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "b9789221b2e3930", "name": "stdout", "source": "b9789221b2e3930.txt", "type": "text/plain", "size": 11469}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398951010, "stop": 1754398951010, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398951011, "stop": 1754398952336, "duration": 1325}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_summarize_what_i_m_reading"}, {"name": "subSuite", "value": "TestEllaSummarizeWhatIMReading"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_summarize_what_i_m_reading"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "2e8dd23e79a70f77.json", "parameterValues": []}