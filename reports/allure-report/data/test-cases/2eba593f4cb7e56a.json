{"uid": "2eba593f4cb7e56a", "name": "测试cannot login in google email box能正常执行", "fullName": "testcases.test_ella.dialogue.test_cannot_login_in_google_email_box.TestEllaCannotLoginGoogleEmailBox#test_cannot_login_in_google_email_box", "historyId": "********************************", "time": {"start": 1754447692028, "stop": 1754447706712, "duration": 14684}, "description": "cannot login in google email box", "descriptionHtml": "<p>cannot login in google email box</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447679149, "stop": 1754447692027, "duration": 12878}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447692027, "stop": 1754447692027, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "cannot login in google email box", "status": "passed", "steps": [{"name": "执行命令: cannot login in google email box", "time": {"start": 1754447692029, "stop": 1754447706305, "duration": 14276}, "status": "passed", "steps": [{"name": "执行命令: cannot login in google email box", "time": {"start": 1754447692029, "stop": 1754447705861, "duration": 13832}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447705861, "stop": 1754447706304, "duration": 443}, "status": "passed", "steps": [], "attachments": [{"uid": "282dc88ae271f8cd", "name": "测试总结", "source": "282dc88ae271f8cd.txt", "type": "text/plain", "size": 226}, {"uid": "df7fd75cfb05be14", "name": "test_completed", "source": "df7fd75cfb05be14.png", "type": "image/png", "size": 546008}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447706305, "stop": 1754447706308, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447706308, "stop": 1754447706711, "duration": 403}, "status": "passed", "steps": [], "attachments": [{"uid": "7063eb8d7b1c2408", "name": "测试总结", "source": "7063eb8d7b1c2408.txt", "type": "text/plain", "size": 226}, {"uid": "7598bbe3a924f200", "name": "test_completed", "source": "7598bbe3a924f200.png", "type": "image/png", "size": 546129}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "14bf02913b2bcac", "name": "stdout", "source": "14bf02913b2bcac.txt", "type": "text/plain", "size": 11401}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447706713, "stop": 1754447706713, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447706716, "stop": 1754447708124, "duration": 1408}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_cannot_login_in_google_email_box"}, {"name": "subSuite", "value": "TestEllaCannotLoginGoogleEmailBox"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_cannot_login_in_google_email_box"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "3892ac10f825702f", "status": "passed", "time": {"start": 1754397962237, "stop": 1754397974946, "duration": 12709}}], "categories": [], "tags": ["smoke"]}, "source": "2eba593f4cb7e56a.json", "parameterValues": []}