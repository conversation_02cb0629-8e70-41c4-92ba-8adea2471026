{"uid": "2ed5255e29720bb5", "name": "测试enable running lock返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_running_lock.TestEllaEnableRunningLock#test_enable_running_lock", "historyId": "57a9b2e6f318afd186b838ed42ebd55c", "time": {"start": 1754452766436, "stop": 1754452781916, "duration": 15480}, "description": "验证enable running lock指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable running lock指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452754400, "stop": 1754452766434, "duration": 12034}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452766434, "stop": 1754452766434, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证enable running lock指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable running lock", "time": {"start": 1754452766436, "stop": 1754452781734, "duration": 15298}, "status": "passed", "steps": [{"name": "执行命令: enable running lock", "time": {"start": 1754452766436, "stop": 1754452781538, "duration": 15102}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452781538, "stop": 1754452781733, "duration": 195}, "status": "passed", "steps": [], "attachments": [{"uid": "e66bfd982d69a348", "name": "测试总结", "source": "e66bfd982d69a348.txt", "type": "text/plain", "size": 225}, {"uid": "e5b1d8551436e36b", "name": "test_completed", "source": "e5b1d8551436e36b.png", "type": "image/png", "size": 493148}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452781734, "stop": 1754452781735, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452781735, "stop": 1754452781915, "duration": 180}, "status": "passed", "steps": [], "attachments": [{"uid": "81ebb18610fae4d2", "name": "测试总结", "source": "81ebb18610fae4d2.txt", "type": "text/plain", "size": 225}, {"uid": "d2e8772deb15ce4c", "name": "test_completed", "source": "d2e8772deb15ce4c.png", "type": "image/png", "size": 494244}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "b1b2df9c4c28ae17", "name": "stdout", "source": "b1b2df9c4c28ae17.txt", "type": "text/plain", "size": 11727}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452781916, "stop": 1754452781916, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452781918, "stop": 1754452783269, "duration": 1351}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_running_lock"}, {"name": "subSuite", "value": "TestEllaEnableRunningLock"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_running_lock"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "deb3d4f1094cfcce", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402797120, "stop": 1754402814529, "duration": 17409}}], "categories": [], "tags": ["smoke"]}, "source": "2ed5255e29720bb5.json", "parameterValues": []}