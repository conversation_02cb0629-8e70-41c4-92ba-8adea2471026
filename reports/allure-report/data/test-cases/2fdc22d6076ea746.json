{"uid": "2fdc22d6076ea746", "name": "测试say hello能正常执行", "fullName": "testcases.test_ella.dialogue.test_say_hello.TestEllaSayHello#test_say_hello", "historyId": "f416bca94fc67372d77ac2dd1f3e4517", "time": {"start": 1754398683994, "stop": 1754398697666, "duration": 13672}, "description": "say hello", "descriptionHtml": "<p>say hello</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398671239, "stop": 1754398683992, "duration": 12753}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398683992, "stop": 1754398683992, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "say hello", "status": "passed", "steps": [{"name": "执行命令: say hello", "time": {"start": 1754398683994, "stop": 1754398697383, "duration": 13389}, "status": "passed", "steps": [{"name": "执行命令: say hello", "time": {"start": 1754398683994, "stop": 1754398697131, "duration": 13137}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398697131, "stop": 1754398697383, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "e3d7e2780dd1f610", "name": "测试总结", "source": "e3d7e2780dd1f610.txt", "type": "text/plain", "size": 478}, {"uid": "3a325435b7b6654f", "name": "test_completed", "source": "3a325435b7b6654f.png", "type": "image/png", "size": 530680}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398697383, "stop": 1754398697388, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398697388, "stop": 1754398697665, "duration": 277}, "status": "passed", "steps": [], "attachments": [{"uid": "52c5be2512427866", "name": "测试总结", "source": "52c5be2512427866.txt", "type": "text/plain", "size": 478}, {"uid": "9a45e005121af845", "name": "test_completed", "source": "9a45e005121af845.png", "type": "image/png", "size": 530358}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "a61f48e9d9bf3f93", "name": "stdout", "source": "a61f48e9d9bf3f93.txt", "type": "text/plain", "size": 13118}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398697669, "stop": 1754398697669, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398697671, "stop": 1754398698961, "duration": 1290}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_say_hello"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_say_hello"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "2fdc22d6076ea746.json", "parameterValues": []}