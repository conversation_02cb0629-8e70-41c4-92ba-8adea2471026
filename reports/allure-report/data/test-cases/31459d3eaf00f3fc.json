{"uid": "31459d3eaf00f3fc", "name": "测试Enable Call Rejection返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_call_rejection.TestEllaEnableCallRejection#test_enable_call_rejection", "historyId": "ff8d76af98b9fdfaa206acbf87daa843", "time": {"start": 1754452703528, "stop": 1754452725928, "duration": 22400}, "description": "验证Enable Call Rejection指令返回预期的不支持响应", "descriptionHtml": "<p>验证Enable Call Rejection指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452691418, "stop": 1754452703525, "duration": 12107}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452703525, "stop": 1754452703525, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证Enable Call Rejection指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: Enable Call Rejection", "time": {"start": 1754452703528, "stop": 1754452725731, "duration": 22203}, "status": "passed", "steps": [{"name": "执行命令: Enable Call Rejection", "time": {"start": 1754452703528, "stop": 1754452725543, "duration": 22015}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452725543, "stop": 1754452725731, "duration": 188}, "status": "passed", "steps": [], "attachments": [{"uid": "7d7516608ff01bf9", "name": "测试总结", "source": "7d7516608ff01bf9.txt", "type": "text/plain", "size": 236}, {"uid": "c7994edbc4ecfc83", "name": "test_completed", "source": "c7994edbc4ecfc83.png", "type": "image/png", "size": 492020}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452725731, "stop": 1754452725732, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452725732, "stop": 1754452725928, "duration": 196}, "status": "passed", "steps": [], "attachments": [{"uid": "f50bde5407716a8a", "name": "测试总结", "source": "f50bde5407716a8a.txt", "type": "text/plain", "size": 236}, {"uid": "475abdf8d0d9cbcb", "name": "test_completed", "source": "475abdf8d0d9cbcb.png", "type": "image/png", "size": 492024}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "f9c76d8718d820f8", "name": "stdout", "source": "f9c76d8718d820f8.txt", "type": "text/plain", "size": 11734}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452725929, "stop": 1754452725929, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452725931, "stop": 1754452727229, "duration": 1298}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_call_rejection"}, {"name": "subSuite", "value": "TestEllaEnableCallRejection"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_call_rejection"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "28728c838647beb0", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402732328, "stop": 1754402756724, "duration": 24396}}], "categories": [], "tags": ["smoke"]}, "source": "31459d3eaf00f3fc.json", "parameterValues": []}