{"uid": "3209a336e36bcb0f", "name": "测试pause music能正常执行", "fullName": "testcases.test_ella.component_coupling.test_pause_music.TestEllaPauseMusic#test_pause_music", "historyId": "464c8ea2a15f7ff86b8e0a347a821945", "time": {"start": 1754397406124, "stop": 1754397419872, "duration": 13748}, "description": "pause music", "descriptionHtml": "<p>pause music</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397393134, "stop": 1754397406122, "duration": 12988}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397406123, "stop": 1754397406123, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "pause music", "status": "passed", "steps": [{"name": "执行命令: pause music", "time": {"start": 1754397406124, "stop": 1754397419567, "duration": 13443}, "status": "passed", "steps": [{"name": "执行命令: pause music", "time": {"start": 1754397406124, "stop": 1754397419184, "duration": 13060}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397419184, "stop": 1754397419564, "duration": 380}, "status": "passed", "steps": [], "attachments": [{"uid": "ec6085817f51e328", "name": "测试总结", "source": "ec6085817f51e328.txt", "type": "text/plain", "size": 179}, {"uid": "58286f1348659299", "name": "test_completed", "source": "58286f1348659299.png", "type": "image/png", "size": 594001}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754397419567, "stop": 1754397419571, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397419571, "stop": 1754397419871, "duration": 300}, "status": "passed", "steps": [], "attachments": [{"uid": "b2889aa7860f8185", "name": "测试总结", "source": "b2889aa7860f8185.txt", "type": "text/plain", "size": 179}, {"uid": "5ba0b317fd7e6295", "name": "test_completed", "source": "5ba0b317fd7e6295.png", "type": "image/png", "size": 595519}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "9eb197120e8321a6", "name": "stdout", "source": "9eb197120e8321a6.txt", "type": "text/plain", "size": 11411}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397419874, "stop": 1754397419874, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397419875, "stop": 1754397421122, "duration": 1247}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_pause_music"}, {"name": "subSuite", "value": "TestEllaPauseMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_pause_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "3209a336e36bcb0f.json", "parameterValues": []}