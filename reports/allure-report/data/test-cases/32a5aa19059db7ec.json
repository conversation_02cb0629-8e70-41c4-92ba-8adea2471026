{"uid": "32a5aa19059db7ec", "name": "测试start run能正常执行", "fullName": "testcases.test_ella.component_coupling.test_start_run.TestEllaStartRun#test_start_run", "historyId": "92553bffc13049b2d4fa1afe9cf89498", "time": {"start": 1754447430794, "stop": 1754447456077, "duration": 25283}, "description": "start run", "descriptionHtml": "<p>start run</p>\n", "status": "failed", "statusMessage": "AssertionError: 初始=None, 最终=None, 响应='['start run', 'Done!', '', '', '[com.transsion.healthlife页面] 未获取到文本内容']'\nassert None", "statusTrace": "self = <testcases.test_ella.component_coupling.test_start_run.TestEllaStartRun object at 0x000001E3892B3AD0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E389AEE5D0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_start_run(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert  final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=None, 最终=None, 响应='['start run', 'Done!', '', '', '[com.transsion.healthlife页面] 未获取到文本内容']'\nE           assert None\n\ntestcases\\test_ella\\component_coupling\\test_start_run.py:36: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447418287, "stop": 1754447430793, "duration": 12506}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447430793, "stop": 1754447430793, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "start run", "status": "failed", "statusMessage": "AssertionError: 初始=None, 最终=None, 响应='['start run', 'Done!', '', '', '[com.transsion.healthlife页面] 未获取到文本内容']'\nassert None", "statusTrace": "self = <testcases.test_ella.component_coupling.test_start_run.TestEllaStartRun object at 0x000001E3892B3AD0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E389AEE5D0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_start_run(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert  final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=None, 最终=None, 响应='['start run', 'Done!', '', '', '[com.transsion.healthlife页面] 未获取到文本内容']'\nE           assert None\n\ntestcases\\test_ella\\component_coupling\\test_start_run.py:36: AssertionError", "steps": [{"name": "执行命令: start run", "time": {"start": 1754447430794, "stop": 1754447456075, "duration": 25281}, "status": "passed", "steps": [{"name": "执行命令: start run", "time": {"start": 1754447430794, "stop": 1754447455853, "duration": 25059}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447455853, "stop": 1754447456075, "duration": 222}, "status": "passed", "steps": [], "attachments": [{"uid": "6c2dd0fb484703ef", "name": "测试总结", "source": "6c2dd0fb484703ef.txt", "type": "text/plain", "size": 206}, {"uid": "db2f4ab64e318537", "name": "test_completed", "source": "db2f4ab64e318537.png", "type": "image/png", "size": 542811}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447456075, "stop": 1754447456076, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754447456076, "stop": 1754447456076, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: 初始=None, 最终=None, 响应='['start run', 'Done!', '', '', '[com.transsion.healthlife页面] 未获取到文本内容']'\nassert None\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\component_coupling\\test_start_run.py\", line 36, in test_start_run\n    assert  final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "71abd31a2f5578c", "name": "stdout", "source": "71abd31a2f5578c.txt", "type": "text/plain", "size": 17584}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447456081, "stop": 1754447456301, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "5a121db7a4111d53", "name": "失败截图-TestEllaStartRun", "source": "5a121db7a4111d53.png", "type": "image/png", "size": 543372}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754447456303, "stop": 1754447457712, "duration": 1409}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_start_run"}, {"name": "subSuite", "value": "TestEllaStartRun"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_start_run"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "80ad1f560a8132", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['start run', 'Done!', '', '', '[com.transsion.healthlife页面内容] Do exercise scientifically and live healthily']'\nassert None", "time": {"start": 1754397720302, "stop": 1754397735155, "duration": 14853}}], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "32a5aa19059db7ec.json", "parameterValues": []}