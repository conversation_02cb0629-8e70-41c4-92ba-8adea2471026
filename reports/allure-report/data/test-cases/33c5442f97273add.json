{"uid": "33c5442f97273add", "name": "测试find a restaurant near me能正常执行", "fullName": "testcases.test_ella.third_coupling.test_find_a_restaurant_near_me.TestEllaFindRestaurantNearMe#test_find_a_restaurant_near_me", "historyId": "918c52f1eb9803594ff76c724b43d5f8", "time": {"start": 1754451386783, "stop": 1754451411771, "duration": 24988}, "description": "find a restaurant near me", "descriptionHtml": "<p>find a restaurant near me</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451374228, "stop": 1754451386782, "duration": 12554}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451386782, "stop": 1754451386782, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "find a restaurant near me", "status": "passed", "steps": [{"name": "执行命令: find a restaurant near me", "time": {"start": 1754451386783, "stop": 1754451411612, "duration": 24829}, "status": "passed", "steps": [{"name": "执行命令: find a restaurant near me", "time": {"start": 1754451386783, "stop": 1754451411403, "duration": 24620}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451411403, "stop": 1754451411612, "duration": 209}, "status": "passed", "steps": [], "attachments": [{"uid": "7a11bb45604d7a75", "name": "测试总结", "source": "7a11bb45604d7a75.txt", "type": "text/plain", "size": 1000}, {"uid": "2f98e996d86ccd15", "name": "test_completed", "source": "2f98e996d86ccd15.png", "type": "image/png", "size": 423038}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证已打开", "time": {"start": 1754451411612, "stop": 1754451411612, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451411612, "stop": 1754451411771, "duration": 159}, "status": "passed", "steps": [], "attachments": [{"uid": "a8a5c502442fd5e1", "name": "测试总结", "source": "a8a5c502442fd5e1.txt", "type": "text/plain", "size": 1000}, {"uid": "17abd91080faf30c", "name": "test_completed", "source": "17abd91080faf30c.png", "type": "image/png", "size": 422628}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "f5e30186977647ff", "name": "stdout", "source": "f5e30186977647ff.txt", "type": "text/plain", "size": 17363}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451411772, "stop": 1754451411772, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451411773, "stop": 1754451413150, "duration": 1377}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_find_a_restaurant_near_me"}, {"name": "subSuite", "value": "TestEllaFindRestaurantNearMe"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_find_a_restaurant_near_me"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "d69eace3c8b6238f", "status": "passed", "time": {"start": 1754401538293, "stop": 1754401562244, "duration": 23951}}], "categories": [], "tags": ["smoke"]}, "source": "33c5442f97273add.json", "parameterValues": []}