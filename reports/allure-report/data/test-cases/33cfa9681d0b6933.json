{"uid": "33cfa9681d0b6933", "name": "测试help me take a long screenshot能正常执行", "fullName": "testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot.TestEllaHelpMeTakeLongScreenshot#test_help_me_take_a_long_screenshot", "historyId": "fe3d09fe0bad56e7804ef2f5ea49d283", "time": {"start": 1754399884489, "stop": 1754399901283, "duration": 16794}, "description": "help me take a long screenshot", "descriptionHtml": "<p>help me take a long screenshot</p>\n", "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False", "statusTrace": "self = <testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot.TestEllaHelpMeTakeLongScreenshot object at 0x00000240FF0FD3D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000240817C1F10>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_help_me_take_a_long_screenshot(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=True\n            )\n    \n        with allure.step(f\"验证文件存在\"):\n>           assert files_status, f\"文件不存在！\"\nE           AssertionError: 文件不存在！\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_help_me_take_a_long_screenshot.py:32: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399871435, "stop": 1754399884488, "duration": 13053}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399884489, "stop": 1754399884489, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "help me take a long screenshot", "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False", "statusTrace": "self = <testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot.TestEllaHelpMeTakeLongScreenshot object at 0x00000240FF0FD3D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000240817C1F10>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_help_me_take_a_long_screenshot(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=True\n            )\n    \n        with allure.step(f\"验证文件存在\"):\n>           assert files_status, f\"文件不存在！\"\nE           AssertionError: 文件不存在！\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_help_me_take_a_long_screenshot.py:32: AssertionError", "steps": [{"name": "执行命令: help me take a long screenshot", "time": {"start": 1754399884490, "stop": 1754399901281, "duration": 16791}, "status": "passed", "steps": [{"name": "执行命令: help me take a long screenshot", "time": {"start": 1754399884490, "stop": 1754399900976, "duration": 16486}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399900976, "stop": 1754399901280, "duration": 304}, "status": "passed", "steps": [], "attachments": [{"uid": "19206e10376658f3", "name": "测试总结", "source": "19206e10376658f3.txt", "type": "text/plain", "size": 555}, {"uid": "242af947215f4d5", "name": "test_completed", "source": "242af947215f4d5.png", "type": "image/png", "size": 586904}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证文件存在", "time": {"start": 1754399901282, "stop": 1754399901282, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\system_coupling\\test_help_me_take_a_long_screenshot.py\", line 32, in test_help_me_take_a_long_screenshot\n    assert files_status, f\"文件不存在！\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "95493c30d212c221", "name": "stdout", "source": "95493c30d212c221.txt", "type": "text/plain", "size": 15090}], "parameters": [], "attachmentStep": false, "stepsCount": 4, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399901294, "stop": 1754399901582, "duration": 288}, "status": "passed", "steps": [], "attachments": [{"uid": "7c364c4e5ac67e9c", "name": "失败截图-TestEllaHelpMeTakeLongScreenshot", "source": "7c364c4e5ac67e9c.png", "type": "image/png", "size": 586609}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754399901585, "stop": 1754399902911, "duration": 1326}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_help_me_take_a_long_screenshot"}, {"name": "subSuite", "value": "TestEllaHelpMeTakeLongScreenshot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "33cfa9681d0b6933.json", "parameterValues": []}