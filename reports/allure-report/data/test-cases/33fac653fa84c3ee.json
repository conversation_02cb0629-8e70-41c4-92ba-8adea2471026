{"uid": "33fac653fa84c3ee", "name": "测试Adjustment the brightness to 50%能正常执行", "fullName": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to.TestEllaAdjustmentBrightness#test_adjustment_the_brightness_to", "historyId": "ad18e983dce31052b87b7404f3b347ce", "time": {"start": 1754399548504, "stop": 1754399561536, "duration": 13032}, "description": "Adjustment the brightness to 50%", "descriptionHtml": "<p>Adjustment the brightness to 50%</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399535498, "stop": 1754399548503, "duration": 13005}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399548503, "stop": 1754399548503, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "Adjustment the brightness to 50%", "status": "passed", "steps": [{"name": "执行命令: Adjustment the brightness to 50%", "time": {"start": 1754399548504, "stop": 1754399561281, "duration": 12777}, "status": "passed", "steps": [{"name": "执行命令: Adjustment the brightness to 50%", "time": {"start": 1754399548504, "stop": 1754399561033, "duration": 12529}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399561033, "stop": 1754399561280, "duration": 247}, "status": "passed", "steps": [], "attachments": [{"uid": "2addc3247f780494", "name": "测试总结", "source": "2addc3247f780494.txt", "type": "text/plain", "size": 207}, {"uid": "bb5f577176e9dbe3", "name": "test_completed", "source": "bb5f577176e9dbe3.png", "type": "image/png", "size": 486513}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399561281, "stop": 1754399561284, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754399561284, "stop": 1754399561284, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399561284, "stop": 1754399561535, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "907091c254e11515", "name": "测试总结", "source": "907091c254e11515.txt", "type": "text/plain", "size": 207}, {"uid": "b2bb06cd9d90fc5c", "name": "test_completed", "source": "b2bb06cd9d90fc5c.png", "type": "image/png", "size": 486691}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "170cf96e1df02849", "name": "stdout", "source": "170cf96e1df02849.txt", "type": "text/plain", "size": 12143}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399561538, "stop": 1754399561538, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399561540, "stop": 1754399562796, "duration": 1256}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_adjustment_the_brightness_to"}, {"name": "subSuite", "value": "TestEllaAdjustmentBrightness"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "33fac653fa84c3ee.json", "parameterValues": []}