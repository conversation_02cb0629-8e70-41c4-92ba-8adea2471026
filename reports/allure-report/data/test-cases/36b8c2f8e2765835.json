{"uid": "36b8c2f8e2765835", "name": "测试pause screen recording能正常执行", "fullName": "testcases.test_ella.system_coupling.test_start_screen_recording.TestEllaStartScreenRecording#test_pause_screen_recording", "historyId": "1ab60ce22774c460e557aaa3b3f9120a", "time": {"start": 1754450264491, "stop": 1754450280445, "duration": 15954}, "description": "pause screen recording", "descriptionHtml": "<p>pause screen recording</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450251738, "stop": 1754450264490, "duration": 12752}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450264490, "stop": 1754450264490, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "pause screen recording", "status": "passed", "steps": [{"name": "执行命令: pause screen recording", "time": {"start": 1754450264491, "stop": 1754450280245, "duration": 15754}, "status": "passed", "steps": [{"name": "执行命令: pause screen recording", "time": {"start": 1754450264491, "stop": 1754450280055, "duration": 15564}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450280055, "stop": 1754450280245, "duration": 190}, "status": "passed", "steps": [], "attachments": [{"uid": "256ea9c45cc6ce2a", "name": "测试总结", "source": "256ea9c45cc6ce2a.txt", "type": "text/plain", "size": 191}, {"uid": "d77c66205f9e351e", "name": "test_completed", "source": "d77c66205f9e351e.png", "type": "image/png", "size": 611387}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450280245, "stop": 1754450280246, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754450280246, "stop": 1754450280246, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450280246, "stop": 1754450280445, "duration": 199}, "status": "passed", "steps": [], "attachments": [{"uid": "327b3d4579762114", "name": "测试总结", "source": "327b3d4579762114.txt", "type": "text/plain", "size": 191}, {"uid": "79a0bb010c626db3", "name": "test_completed", "source": "79a0bb010c626db3.png", "type": "image/png", "size": 611046}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "337d3a073513eee3", "name": "stdout", "source": "337d3a073513eee3.txt", "type": "text/plain", "size": 11893}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450280446, "stop": 1754450280446, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450280446, "stop": 1754450281813, "duration": 1367}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_start_screen_recording"}, {"name": "subSuite", "value": "TestEllaStartScreenRecording"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_start_screen_recording"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "e763eb3d638654b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording on hold']\nassert False", "time": {"start": 1754400437392, "stop": 1754400451546, "duration": 14154}}], "categories": [], "tags": ["smoke"]}, "source": "36b8c2f8e2765835.json", "parameterValues": []}