{"uid": "3798453d42ddef7a", "name": "测试give me some money能正常执行", "fullName": "testcases.test_ella.dialogue.test_give_me_some_money.TestEllaGiveMeSomeMoney#test_give_me_some_money", "historyId": "2060dd1cfd03194548c0456a10798266", "time": {"start": 1754398101857, "stop": 1754398116736, "duration": 14879}, "description": "give me some money", "descriptionHtml": "<p>give me some money</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398088782, "stop": 1754398101856, "duration": 13074}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398101856, "stop": 1754398101856, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "give me some money", "status": "passed", "steps": [{"name": "执行命令: give me some money", "time": {"start": 1754398101857, "stop": 1754398116451, "duration": 14594}, "status": "passed", "steps": [{"name": "执行命令: give me some money", "time": {"start": 1754398101857, "stop": 1754398116120, "duration": 14263}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398116120, "stop": 1754398116450, "duration": 330}, "status": "passed", "steps": [], "attachments": [{"uid": "8e9ce789a7d5158e", "name": "测试总结", "source": "8e9ce789a7d5158e.txt", "type": "text/plain", "size": 185}, {"uid": "542a25cb0b58ff03", "name": "test_completed", "source": "542a25cb0b58ff03.png", "type": "image/png", "size": 538449}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398116451, "stop": 1754398116457, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398116457, "stop": 1754398116735, "duration": 278}, "status": "passed", "steps": [], "attachments": [{"uid": "567e2fbe5c833bdf", "name": "测试总结", "source": "567e2fbe5c833bdf.txt", "type": "text/plain", "size": 185}, {"uid": "81fc19fc58fff10e", "name": "test_completed", "source": "81fc19fc58fff10e.png", "type": "image/png", "size": 539023}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "66cdff939a4b13de", "name": "stdout", "source": "66cdff939a4b13de.txt", "type": "text/plain", "size": 11451}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398116738, "stop": 1754398116738, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398116741, "stop": 1754398118014, "duration": 1273}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_give_me_some_money"}, {"name": "subSuite", "value": "TestEllaGiveMeSomeMoney"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_give_me_some_money"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "3798453d42ddef7a.json", "parameterValues": []}