{"uid": "38124d92f03aa953", "name": "测试what is apec?能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_is_apec.TestEllaWhatIsApec#test_what_is_apec", "historyId": "0c4bd81bf0dbac094265e3ac47550bbd", "time": {"start": 1754399112436, "stop": 1754399127749, "duration": 15313}, "description": "what is apec?", "descriptionHtml": "<p>what is apec?</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399099429, "stop": 1754399112435, "duration": 13006}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399112435, "stop": 1754399112435, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "what is apec?", "status": "passed", "steps": [{"name": "执行命令: what is apec?", "time": {"start": 1754399112436, "stop": 1754399127438, "duration": 15002}, "status": "passed", "steps": [{"name": "执行命令: what is apec?", "time": {"start": 1754399112436, "stop": 1754399127166, "duration": 14730}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399127166, "stop": 1754399127437, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "f231494020a8bd41", "name": "测试总结", "source": "f231494020a8bd41.txt", "type": "text/plain", "size": 765}, {"uid": "92460b340f5ac578", "name": "test_completed", "source": "92460b340f5ac578.png", "type": "image/png", "size": 654297}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399127438, "stop": 1754399127440, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399127440, "stop": 1754399127748, "duration": 308}, "status": "passed", "steps": [], "attachments": [{"uid": "8b624f59a53f4af7", "name": "测试总结", "source": "8b624f59a53f4af7.txt", "type": "text/plain", "size": 765}, {"uid": "3498f687a95708c6", "name": "test_completed", "source": "3498f687a95708c6.png", "type": "image/png", "size": 654358}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d817e648ee2f4b9c", "name": "stdout", "source": "d817e648ee2f4b9c.txt", "type": "text/plain", "size": 14273}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399127749, "stop": 1754399127750, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399127751, "stop": 1754399129024, "duration": 1273}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_is_apec"}, {"name": "subSuite", "value": "TestEllaWhatIsApec"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_is_apec"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "38124d92f03aa953.json", "parameterValues": []}