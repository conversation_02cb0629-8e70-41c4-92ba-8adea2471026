{"uid": "3814b093ab5b35ad", "name": "测试help me take a screenshot能正常执行", "fullName": "testcases.test_ella.system_coupling.test_help_me_take_a_screenshot.TestEllaHelpMeTakeScreenshot#test_help_me_take_a_screenshot", "historyId": "459c099a876d1129ddcb7cb28663b756", "time": {"start": 1754449732216, "stop": 1754449748925, "duration": 16709}, "description": "help me take a screenshot", "descriptionHtml": "<p>help me take a screenshot</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449719325, "stop": 1754449732214, "duration": 12889}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449732215, "stop": 1754449732215, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "help me take a screenshot", "status": "passed", "steps": [{"name": "执行命令: help me take a screenshot", "time": {"start": 1754449732216, "stop": 1754449748770, "duration": 16554}, "status": "passed", "steps": [{"name": "执行命令: help me take a screenshot", "time": {"start": 1754449732216, "stop": 1754449748564, "duration": 16348}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449748564, "stop": 1754449748770, "duration": 206}, "status": "passed", "steps": [], "attachments": [{"uid": "b604834c5c0f723e", "name": "测试总结", "source": "b604834c5c0f723e.txt", "type": "text/plain", "size": 487}, {"uid": "a15c392862af51fd", "name": "test_completed", "source": "a15c392862af51fd.png", "type": "image/png", "size": 550602}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证文件存在", "time": {"start": 1754449748770, "stop": 1754449748770, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449748770, "stop": 1754449748925, "duration": 155}, "status": "passed", "steps": [], "attachments": [{"uid": "59541e48013a9b8", "name": "测试总结", "source": "59541e48013a9b8.txt", "type": "text/plain", "size": 487}, {"uid": "7ee89382860b2ebe", "name": "test_completed", "source": "7ee89382860b2ebe.png", "type": "image/png", "size": 550114}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "1202ed2dcb20051b", "name": "stdout", "source": "1202ed2dcb20051b.txt", "type": "text/plain", "size": 13449}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449748926, "stop": 1754449748926, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449748927, "stop": 1754449750377, "duration": 1450}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_help_me_take_a_screenshot"}, {"name": "subSuite", "value": "TestEllaHelpMeTakeScreenshot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_help_me_take_a_screenshot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "6827be8ffca997a7", "status": "passed", "time": {"start": 1754399916015, "stop": 1754399931170, "duration": 15155}}], "categories": [], "tags": ["smoke"]}, "source": "3814b093ab5b35ad.json", "parameterValues": []}