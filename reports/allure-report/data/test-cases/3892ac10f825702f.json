{"uid": "3892ac10f825702f", "name": "测试cannot login in google email box能正常执行", "fullName": "testcases.test_ella.dialogue.test_cannot_login_in_google_email_box.TestEllaCannotLoginGoogleEmailBox#test_cannot_login_in_google_email_box", "historyId": "********************************", "time": {"start": 1754397962237, "stop": 1754397974946, "duration": 12709}, "description": "cannot login in google email box", "descriptionHtml": "<p>cannot login in google email box</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397949181, "stop": 1754397962236, "duration": 13055}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397962236, "stop": 1754397962236, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "cannot login in google email box", "status": "passed", "steps": [{"name": "执行命令: cannot login in google email box", "time": {"start": 1754397962237, "stop": 1754397974702, "duration": 12465}, "status": "passed", "steps": [{"name": "执行命令: cannot login in google email box", "time": {"start": 1754397962237, "stop": 1754397974455, "duration": 12218}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397974455, "stop": 1754397974702, "duration": 247}, "status": "passed", "steps": [], "attachments": [{"uid": "ca87803bb75860e7", "name": "测试总结", "source": "ca87803bb75860e7.txt", "type": "text/plain", "size": 224}, {"uid": "ae4ae6587c771a92", "name": "test_completed", "source": "ae4ae6587c771a92.png", "type": "image/png", "size": 501106}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754397974702, "stop": 1754397974705, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397974705, "stop": 1754397974945, "duration": 240}, "status": "passed", "steps": [], "attachments": [{"uid": "4cc9217676eb6fc", "name": "测试总结", "source": "4cc9217676eb6fc.txt", "type": "text/plain", "size": 224}, {"uid": "5fab553bc91adc39", "name": "test_completed", "source": "5fab553bc91adc39.png", "type": "image/png", "size": 501422}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "7144b6c8a6987b51", "name": "stdout", "source": "7144b6c8a6987b51.txt", "type": "text/plain", "size": 11586}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397974947, "stop": 1754397974947, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397974950, "stop": 1754397976217, "duration": 1267}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_cannot_login_in_google_email_box"}, {"name": "subSuite", "value": "TestEllaCannotLoginGoogleEmailBox"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_cannot_login_in_google_email_box"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "3892ac10f825702f.json", "parameterValues": []}