{"uid": "38d6821ea604d751", "name": "测试turn off show battery percentage返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_turn_off_show_battery_percentage.TestEllaTurnOffShowBatteryPercentage#test_turn_off_show_battery_percentage", "historyId": "4bda544c08fa4bc5494c7dda01d4cc77", "time": {"start": 1754455406018, "stop": 1754455420840, "duration": 14822}, "description": "验证turn off show battery percentage指令返回预期的不支持响应", "descriptionHtml": "<p>验证turn off show battery percentage指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455394340, "stop": 1754455406016, "duration": 11676}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455406017, "stop": 1754455406017, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证turn off show battery percentage指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: turn off show battery percentage", "time": {"start": 1754455406018, "stop": 1754455420625, "duration": 14607}, "status": "passed", "steps": [{"name": "执行命令: turn off show battery percentage", "time": {"start": 1754455406018, "stop": 1754455420446, "duration": 14428}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455420446, "stop": 1754455420625, "duration": 179}, "status": "passed", "steps": [], "attachments": [{"uid": "cbba1bfe065e3ca1", "name": "测试总结", "source": "cbba1bfe065e3ca1.txt", "type": "text/plain", "size": 256}, {"uid": "2c4b3dfaf2092b4c", "name": "test_completed", "source": "2c4b3dfaf2092b4c.png", "type": "image/png", "size": 507843}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455420625, "stop": 1754455420626, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455420626, "stop": 1754455420840, "duration": 214}, "status": "passed", "steps": [], "attachments": [{"uid": "a453f5cb48e65881", "name": "测试总结", "source": "a453f5cb48e65881.txt", "type": "text/plain", "size": 256}, {"uid": "90ed78173227beaa", "name": "test_completed", "source": "90ed78173227beaa.png", "type": "image/png", "size": 507423}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "5f16cd423946e5b3", "name": "stdout", "source": "5f16cd423946e5b3.txt", "type": "text/plain", "size": 11976}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455420841, "stop": 1754455420841, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455420842, "stop": 1754455422154, "duration": 1312}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_off_show_battery_percentage"}, {"name": "subSuite", "value": "TestEllaTurnOffShowBatteryPercentage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_off_show_battery_percentage"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "cde328be425470a7", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405448464, "stop": 1754405459826, "duration": 11362}}], "categories": [], "tags": ["smoke"]}, "source": "38d6821ea604d751.json", "parameterValues": []}