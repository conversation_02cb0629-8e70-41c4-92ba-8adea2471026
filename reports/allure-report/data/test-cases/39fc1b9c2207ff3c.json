{"uid": "39fc1b9c2207ff3c", "name": "测试clear junk files命令", "fullName": "testcases.test_ella.system_coupling.test_clear_junk_files.TestEllaClearJunkFiles#test_clear_junk_files", "historyId": "7413abdce214459d0e44671ef65b660b", "time": {"start": 1754399637231, "stop": 1754399665281, "duration": 28050}, "description": "使用简化的测试框架测试phonemaster开启命令，验证响应包含Done且实际打开PhoneMaster", "descriptionHtml": "<p>使用简化的测试框架测试phonemaster开启命令，验证响应包含Done且实际打开PhoneMaster</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应未包含期望内容: ['done']\nassert False", "statusTrace": "self = <testcases.test_ella.system_coupling.test_clear_junk_files.TestEllaClearJunkFiles object at 0x00000240FF0E8C50>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002408158CE90>\n\n    @allure.title(\"测试clear junk files命令\")\n    @allure.description(\"使用简化的测试框架测试phonemaster开启命令，验证响应包含Done且实际打开PhoneMaster\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_clear_junk_files(self, ella_app):\n        \"\"\"测试clear junk files命令 - 简洁版本\"\"\"\n        command = \"clear junk files\"\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含Done\"):\n>           result = self.verify_expected_in_response(\"done\", response_text)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntestcases\\test_ella\\system_coupling\\test_clear_junk_files.py:26: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <testcases.test_ella.system_coupling.test_clear_junk_files.TestEllaClearJunkFiles object at 0x00000240FF0E8C50>\nexpected_text = 'done'\nresponse_text = ['clear junk files', '已执行!', '', '', '[com.transsion.phonemaster页面内容] Junk | Cache junk | 553 MB / 553 MB | Residual junk | 0 B / 0 B | Clean Up Packages | 162 MB / 162 MB | System junk']\n\n    def verify_expected_in_response(self, expected_text, response_text):\n        \"\"\"\n        验证期望内容是否在响应中\n    \n        Args:\n            expected_text: 期望的文本内容，可以是字符串或字符串列表\n            response_text: 响应文本，可以是字符串或字符串列表\n    \n        Returns:\n            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含\n        \"\"\"\n        log.info(f\"verify_expected_in_response 响应类型: {type(response_text)}, 内容: {response_text}\")\n    \n        # 处理 expected_text 参数\n        if isinstance(expected_text, str):\n            expected_list = [expected_text]\n        elif isinstance(expected_text, list):\n            expected_list = expected_text\n        else:\n            log.error(f\"❌ expected_text类型错误: {type(expected_text)}\")\n            return False\n    \n        # 处理 response_text 参数，统一转换为字符串进行搜索\n        if isinstance(response_text, str):\n            # 如果是字符串，直接使用\n            search_text = response_text\n            log.debug(f\"响应文本(字符串): {search_text}\")\n        elif isinstance(response_text, list):\n            # 如果是列表，过滤空值并合并为一个字符串\n            filtered_texts = [text for text in response_text if text and text.strip()]\n            search_text = \" \".join(filtered_texts)\n            log.debug(f\"响应文本(列表转换): 原始列表={response_text}, 过滤后={filtered_texts}, 合并后={search_text}\")\n        else:\n            log.error(f\"❌ response_text类型错误: {type(response_text)}\")\n            return False\n    \n        # 如果合并后的文本为空，记录警告\n        if not search_text or not search_text.strip():\n            log.warning(\"⚠️ 响应文本为空或只包含空白字符\")\n            search_text = \"\"\n    \n        # 记录所有验证结果\n        all_found = True\n        found_items = []\n        missing_items = []\n    \n        # 遍历所有期望内容\n        for expected_item in expected_list:\n            if not expected_item or not expected_item.strip():\n                log.warning(f\"⚠️ 跳过空的期望内容: '{expected_item}'\")\n                continue\n    \n            # 在合并的文本中搜索\n            if expected_item.lower() in search_text.lower():\n                found_items.append(expected_item)\n                log.info(f\"✅ 响应包含期望内容: '{expected_item}'\")\n            else:\n                missing_items.append(expected_item)\n                log.warning(f\"⚠️ 响应未包含期望内容: '{expected_item}'\")\n                all_found = False\n    \n        # 输出总结\n        if all_found:\n            log.info(f\"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})\")\n        else:\n            log.warning(f\"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})\")\n            log.warning(f\"缺失内容: {missing_items}\")\n            log.warning(f\"搜索文本: '{search_text}'\")\n    \n>       assert all_found, f\"响应未包含期望内容: {missing_items}\"\nE       AssertionError: 响应未包含期望内容: ['done']\nE       assert False\n\ntestcases\\test_ella\\base_ella_test.py:923: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399624167, "stop": 1754399637230, "duration": 13063}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399637231, "stop": 1754399637231, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "使用简化的测试框架测试phonemaster开启命令，验证响应包含Done且实际打开PhoneMaster", "status": "failed", "statusMessage": "AssertionError: 响应未包含期望内容: ['done']\nassert False", "statusTrace": "self = <testcases.test_ella.system_coupling.test_clear_junk_files.TestEllaClearJunkFiles object at 0x00000240FF0E8C50>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002408158CE90>\n\n    @allure.title(\"测试clear junk files命令\")\n    @allure.description(\"使用简化的测试框架测试phonemaster开启命令，验证响应包含Done且实际打开PhoneMaster\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_clear_junk_files(self, ella_app):\n        \"\"\"测试clear junk files命令 - 简洁版本\"\"\"\n        command = \"clear junk files\"\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含Done\"):\n>           result = self.verify_expected_in_response(\"done\", response_text)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntestcases\\test_ella\\system_coupling\\test_clear_junk_files.py:26: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <testcases.test_ella.system_coupling.test_clear_junk_files.TestEllaClearJunkFiles object at 0x00000240FF0E8C50>\nexpected_text = 'done'\nresponse_text = ['clear junk files', '已执行!', '', '', '[com.transsion.phonemaster页面内容] Junk | Cache junk | 553 MB / 553 MB | Residual junk | 0 B / 0 B | Clean Up Packages | 162 MB / 162 MB | System junk']\n\n    def verify_expected_in_response(self, expected_text, response_text):\n        \"\"\"\n        验证期望内容是否在响应中\n    \n        Args:\n            expected_text: 期望的文本内容，可以是字符串或字符串列表\n            response_text: 响应文本，可以是字符串或字符串列表\n    \n        Returns:\n            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含\n        \"\"\"\n        log.info(f\"verify_expected_in_response 响应类型: {type(response_text)}, 内容: {response_text}\")\n    \n        # 处理 expected_text 参数\n        if isinstance(expected_text, str):\n            expected_list = [expected_text]\n        elif isinstance(expected_text, list):\n            expected_list = expected_text\n        else:\n            log.error(f\"❌ expected_text类型错误: {type(expected_text)}\")\n            return False\n    \n        # 处理 response_text 参数，统一转换为字符串进行搜索\n        if isinstance(response_text, str):\n            # 如果是字符串，直接使用\n            search_text = response_text\n            log.debug(f\"响应文本(字符串): {search_text}\")\n        elif isinstance(response_text, list):\n            # 如果是列表，过滤空值并合并为一个字符串\n            filtered_texts = [text for text in response_text if text and text.strip()]\n            search_text = \" \".join(filtered_texts)\n            log.debug(f\"响应文本(列表转换): 原始列表={response_text}, 过滤后={filtered_texts}, 合并后={search_text}\")\n        else:\n            log.error(f\"❌ response_text类型错误: {type(response_text)}\")\n            return False\n    \n        # 如果合并后的文本为空，记录警告\n        if not search_text or not search_text.strip():\n            log.warning(\"⚠️ 响应文本为空或只包含空白字符\")\n            search_text = \"\"\n    \n        # 记录所有验证结果\n        all_found = True\n        found_items = []\n        missing_items = []\n    \n        # 遍历所有期望内容\n        for expected_item in expected_list:\n            if not expected_item or not expected_item.strip():\n                log.warning(f\"⚠️ 跳过空的期望内容: '{expected_item}'\")\n                continue\n    \n            # 在合并的文本中搜索\n            if expected_item.lower() in search_text.lower():\n                found_items.append(expected_item)\n                log.info(f\"✅ 响应包含期望内容: '{expected_item}'\")\n            else:\n                missing_items.append(expected_item)\n                log.warning(f\"⚠️ 响应未包含期望内容: '{expected_item}'\")\n                all_found = False\n    \n        # 输出总结\n        if all_found:\n            log.info(f\"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})\")\n        else:\n            log.warning(f\"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})\")\n            log.warning(f\"缺失内容: {missing_items}\")\n            log.warning(f\"搜索文本: '{search_text}'\")\n    \n>       assert all_found, f\"响应未包含期望内容: {missing_items}\"\nE       AssertionError: 响应未包含期望内容: ['done']\nE       assert False\n\ntestcases\\test_ella\\base_ella_test.py:923: AssertionError", "steps": [{"name": "执行命令: clear junk files", "time": {"start": 1754399637231, "stop": 1754399665272, "duration": 28041}, "status": "passed", "steps": [{"name": "执行命令: clear junk files", "time": {"start": 1754399637231, "stop": 1754399665012, "duration": 27781}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399665012, "stop": 1754399665271, "duration": 259}, "status": "passed", "steps": [], "attachments": [{"uid": "5cab86c13f422c6f", "name": "测试总结", "source": "5cab86c13f422c6f.txt", "type": "text/plain", "size": 324}, {"uid": "88a10778ba8d8a78", "name": "test_completed", "source": "88a10778ba8d8a78.png", "type": "image/png", "size": 509165}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含Done", "time": {"start": 1754399665272, "stop": 1754399665278, "duration": 6}, "status": "failed", "statusMessage": "AssertionError: 响应未包含期望内容: ['done']\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\system_coupling\\test_clear_junk_files.py\", line 26, in test_clear_junk_files\n    result = self.verify_expected_in_response(\"done\", response_text)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 923, in verify_expected_in_response\n    assert all_found, f\"响应未包含期望内容: {missing_items}\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "cd2d9faaed90009a", "name": "stdout", "source": "cd2d9faaed90009a.txt", "type": "text/plain", "size": 18666}], "parameters": [], "attachmentStep": false, "stepsCount": 4, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399665339, "stop": 1754399665618, "duration": 279}, "status": "passed", "steps": [], "attachments": [{"uid": "b4df2b90c8327592", "name": "失败截图-TestEllaClearJunkFiles", "source": "b4df2b90c8327592.png", "type": "image/png", "size": 508977}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754399665620, "stop": 1754399666926, "duration": 1306}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_clear_junk_files"}, {"name": "subSuite", "value": "TestEllaClearJunkFiles"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_clear_junk_files"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "39fc1b9c2207ff3c.json", "parameterValues": []}