{"uid": "3b9a61ca339a789e", "name": "测试open notification ringtone settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_open_notification_ringtone_settings.TestEllaOpenSettings#test_open_notification_ringtone_settings", "historyId": "ff8706df57207971727cf6e1326d4a26", "time": {"start": 1754453570061, "stop": 1754453586358, "duration": 16297}, "description": "验证open notification ringtone settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证open notification ringtone settings指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453557973, "stop": 1754453570060, "duration": 12087}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453570061, "stop": 1754453570061, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证open notification ringtone settings指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: open notification ringtone settings", "time": {"start": 1754453570062, "stop": 1754453586163, "duration": 16101}, "status": "passed", "steps": [{"name": "执行命令: open notification ringtone settings", "time": {"start": 1754453570062, "stop": 1754453585966, "duration": 15904}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453585966, "stop": 1754453586162, "duration": 196}, "status": "passed", "steps": [], "attachments": [{"uid": "764f9c71b94fc604", "name": "测试总结", "source": "764f9c71b94fc604.txt", "type": "text/plain", "size": 265}, {"uid": "bec09340d0854a42", "name": "test_completed", "source": "bec09340d0854a42.png", "type": "image/png", "size": 504113}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453586163, "stop": 1754453586164, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453586164, "stop": 1754453586358, "duration": 194}, "status": "passed", "steps": [], "attachments": [{"uid": "100c020c7f6b0e3", "name": "测试总结", "source": "100c020c7f6b0e3.txt", "type": "text/plain", "size": 265}, {"uid": "2cc09b4d48748130", "name": "test_completed", "source": "2cc09b4d48748130.png", "type": "image/png", "size": 504064}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3c660b2587a1c88d", "name": "stdout", "source": "3c660b2587a1c88d.txt", "type": "text/plain", "size": 11958}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453586358, "stop": 1754453586358, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453586359, "stop": 1754453587651, "duration": 1292}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_notification_ringtone_settings"}, {"name": "subSuite", "value": "TestEllaOpenSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_notification_ringtone_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "2f514b5b655f5882", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403593327, "stop": 1754403606500, "duration": 13173}}], "categories": [], "tags": ["smoke"]}, "source": "3b9a61ca339a789e.json", "parameterValues": []}