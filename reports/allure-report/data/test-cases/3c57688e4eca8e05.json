{"uid": "3c57688e4eca8e05", "name": "测试hi能正常执行", "fullName": "testcases.test_ella.dialogue.test_hi.TestEllaHi#test_hi", "historyId": "18415b75388fbfdac9a7e4232373c000", "time": {"start": 1754398228333, "stop": 1754398242164, "duration": 13831}, "description": "hi", "descriptionHtml": "<p>hi</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398215683, "stop": 1754398228332, "duration": 12649}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398228333, "stop": 1754398228333, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "hi", "status": "passed", "steps": [{"name": "执行命令: hi", "time": {"start": 1754398228333, "stop": 1754398241887, "duration": 13554}, "status": "passed", "steps": [{"name": "执行命令: hi", "time": {"start": 1754398228333, "stop": 1754398241617, "duration": 13284}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398241617, "stop": 1754398241885, "duration": 268}, "status": "passed", "steps": [], "attachments": [{"uid": "7b5525a0b5191b52", "name": "测试总结", "source": "7b5525a0b5191b52.txt", "type": "text/plain", "size": 518}, {"uid": "853834ed9b2ce903", "name": "test_completed", "source": "853834ed9b2ce903.png", "type": "image/png", "size": 582811}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398241887, "stop": 1754398241893, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398241893, "stop": 1754398242163, "duration": 270}, "status": "passed", "steps": [], "attachments": [{"uid": "21871e76a71664ed", "name": "测试总结", "source": "21871e76a71664ed.txt", "type": "text/plain", "size": 518}, {"uid": "614c7d1f828f36a7", "name": "test_completed", "source": "614c7d1f828f36a7.png", "type": "image/png", "size": 582213}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d441f5dd2130b37c", "name": "stdout", "source": "d441f5dd2130b37c.txt", "type": "text/plain", "size": 13289}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398242167, "stop": 1754398242167, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398242168, "stop": 1754398243428, "duration": 1260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_hi"}, {"name": "subSuite", "value": "TestEllaHi"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_hi"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "3c57688e4eca8e05.json", "parameterValues": []}