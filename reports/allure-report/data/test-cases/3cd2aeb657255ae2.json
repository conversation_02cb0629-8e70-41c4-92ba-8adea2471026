{"uid": "3cd2aeb657255ae2", "name": "测试set phantom v pen返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_phantom_v_pen.TestEllaSetPhantomVPen#test_set_phantom_v_pen", "historyId": "ca9dd7f70b2888aafceb94247d7986f0", "time": {"start": 1754454802520, "stop": 1754454816577, "duration": 14057}, "description": "验证set phantom v pen指令返回预期的不支持响应", "descriptionHtml": "<p>验证set phantom v pen指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454790450, "stop": 1754454802518, "duration": 12068}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454802518, "stop": 1754454802518, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set phantom v pen指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set phantom v pen", "time": {"start": 1754454802520, "stop": 1754454816403, "duration": 13883}, "status": "passed", "steps": [{"name": "执行命令: set phantom v pen", "time": {"start": 1754454802520, "stop": 1754454816197, "duration": 13677}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454816197, "stop": 1754454816403, "duration": 206}, "status": "passed", "steps": [], "attachments": [{"uid": "22844d47d69aedff", "name": "测试总结", "source": "22844d47d69aedff.txt", "type": "text/plain", "size": 221}, {"uid": "f3d9f64a968357f8", "name": "test_completed", "source": "f3d9f64a968357f8.png", "type": "image/png", "size": 484889}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454816403, "stop": 1754454816404, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454816404, "stop": 1754454816576, "duration": 172}, "status": "passed", "steps": [], "attachments": [{"uid": "8b647f00278955c2", "name": "测试总结", "source": "8b647f00278955c2.txt", "type": "text/plain", "size": 221}, {"uid": "bbf677c06be3146a", "name": "test_completed", "source": "bbf677c06be3146a.png", "type": "image/png", "size": 484906}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "af1723b8189ccf4f", "name": "stdout", "source": "af1723b8189ccf4f.txt", "type": "text/plain", "size": 11268}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754454816577, "stop": 1754454817926, "duration": 1349}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754454816577, "stop": 1754454816577, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_phantom_v_pen"}, {"name": "subSuite", "value": "TestEllaSetPhantomVPen"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_phantom_v_pen"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "fc8ccc288e2ceac3", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404808870, "stop": 1754404819945, "duration": 11075}}], "categories": [], "tags": ["smoke"]}, "source": "3cd2aeb657255ae2.json", "parameterValues": []}