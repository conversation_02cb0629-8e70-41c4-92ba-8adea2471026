{"uid": "3cf415527b3ee855", "name": "测试play sun be song of jide chord", "fullName": "testcases.test_ella.component_coupling.test_play_sun_be_song_of_jide_chord.TestEllaOpenPlaySunBeSongOfJideChord#test_play_sun_be_song_of_jide_chord", "historyId": "d41a9c89a400d807309a9cecf36c0728", "time": {"start": 1754397600817, "stop": 1754397622053, "duration": 21236}, "description": "测试play sun be song of jide chord指令", "descriptionHtml": "<p>测试play sun be song of jide chord指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397587804, "stop": 1754397600817, "duration": 13013}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397600817, "stop": 1754397600817, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play sun be song of jide chord指令", "status": "passed", "steps": [{"name": "执行命令: play sun be song of jide chord", "time": {"start": 1754397600817, "stop": 1754397621766, "duration": 20949}, "status": "passed", "steps": [{"name": "执行命令: play sun be song of jide chord", "time": {"start": 1754397600817, "stop": 1754397621461, "duration": 20644}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397621461, "stop": 1754397621765, "duration": 304}, "status": "passed", "steps": [], "attachments": [{"uid": "37e7459bbbb202f", "name": "测试总结", "source": "37e7459bbbb202f.txt", "type": "text/plain", "size": 524}, {"uid": "4c041f221fac3d1e", "name": "test_completed", "source": "4c041f221fac3d1e.png", "type": "image/png", "size": 534405}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754397621766, "stop": 1754397621770, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754397621770, "stop": 1754397621770, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397621770, "stop": 1754397622052, "duration": 282}, "status": "passed", "steps": [], "attachments": [{"uid": "8ea8c8c94b451d25", "name": "测试总结", "source": "8ea8c8c94b451d25.txt", "type": "text/plain", "size": 524}, {"uid": "8b5c736fc9657db4", "name": "test_completed", "source": "8b5c736fc9657db4.png", "type": "image/png", "size": 534115}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "4e88f72161acc1d2", "name": "stdout", "source": "4e88f72161acc1d2.txt", "type": "text/plain", "size": 15270}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397622054, "stop": 1754397622055, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397622057, "stop": 1754397623319, "duration": 1262}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_sun_be_song_of_jide_chord"}, {"name": "subSuite", "value": "TestEllaOpenPlaySunBeSongOfJideChord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_sun_be_song_of_jide_chord"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "3cf415527b3ee855.json", "parameterValues": []}