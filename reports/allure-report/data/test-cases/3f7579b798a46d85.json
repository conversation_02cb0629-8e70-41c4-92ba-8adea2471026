{"uid": "3f7579b798a46d85", "name": "测试turn off flashlight能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_off_flashlight.TestEllaTurnOffFlashlight#test_turn_off_flashlight", "historyId": "c190fe929896ea57ed1e33f8bc5bf113", "time": {"start": 1754450919306, "stop": 1754450935420, "duration": 16114}, "description": "turn off flashlight", "descriptionHtml": "<p>turn off flashlight</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450906621, "stop": 1754450919305, "duration": 12684}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450919306, "stop": 1754450919306, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "turn off flashlight", "status": "passed", "steps": [{"name": "执行命令: turn off flashlight", "time": {"start": 1754450919307, "stop": 1754450935228, "duration": 15921}, "status": "passed", "steps": [{"name": "执行命令: turn off flashlight", "time": {"start": 1754450919307, "stop": 1754450935018, "duration": 15711}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450935018, "stop": 1754450935228, "duration": 210}, "status": "passed", "steps": [], "attachments": [{"uid": "f50fbade85dd1b22", "name": "测试总结", "source": "f50fbade85dd1b22.txt", "type": "text/plain", "size": 201}, {"uid": "a90512951e94cd94", "name": "test_completed", "source": "a90512951e94cd94.png", "type": "image/png", "size": 513982}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450935228, "stop": 1754450935229, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754450935229, "stop": 1754450935229, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450935229, "stop": 1754450935420, "duration": 191}, "status": "passed", "steps": [], "attachments": [{"uid": "8d8fb798754684ce", "name": "测试总结", "source": "8d8fb798754684ce.txt", "type": "text/plain", "size": 201}, {"uid": "b1092b6d93ce8cb9", "name": "test_completed", "source": "b1092b6d93ce8cb9.png", "type": "image/png", "size": 513812}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "e6b8ee5caf56b11", "name": "stdout", "source": "e6b8ee5caf56b11.txt", "type": "text/plain", "size": 11906}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450935420, "stop": 1754450935420, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450935423, "stop": 1754450936726, "duration": 1303}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_flashlight"}, {"name": "subSuite", "value": "TestEllaTurnOffFlashlight"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_flashlight"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "b4b8e7b6dab5f8f3", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Flashlight is turned off now']\nassert False", "time": {"start": 1754401078086, "stop": 1754401091361, "duration": 13275}}], "categories": [], "tags": ["smoke"]}, "source": "3f7579b798a46d85.json", "parameterValues": []}