{"uid": "3fc3f1d2266214de", "name": "测试jump to high brightness mode settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_high_brightness_mode_settings.TestEllaJumpHighBrightnessModeSettings#test_jump_to_high_brightness_mode_settings", "historyId": "48a2a80bfed06f0c82b99a0aaa26e252", "time": {"start": 1754453297139, "stop": 1754453311552, "duration": 14413}, "description": "验证jump to high brightness mode settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to high brightness mode settings指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453284903, "stop": 1754453297137, "duration": 12234}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453297138, "stop": 1754453297138, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证jump to high brightness mode settings指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: jump to high brightness mode settings", "time": {"start": 1754453297139, "stop": 1754453311336, "duration": 14197}, "status": "passed", "steps": [{"name": "执行命令: jump to high brightness mode settings", "time": {"start": 1754453297139, "stop": 1754453311164, "duration": 14025}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453311164, "stop": 1754453311336, "duration": 172}, "status": "passed", "steps": [], "attachments": [{"uid": "89dc0a69daa93034", "name": "测试总结", "source": "89dc0a69daa93034.txt", "type": "text/plain", "size": 264}, {"uid": "f9da598b92e3a58e", "name": "test_completed", "source": "f9da598b92e3a58e.png", "type": "image/png", "size": 511992}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453311336, "stop": 1754453311338, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453311338, "stop": 1754453311551, "duration": 213}, "status": "passed", "steps": [], "attachments": [{"uid": "e03da1adaf15f1f2", "name": "测试总结", "source": "e03da1adaf15f1f2.txt", "type": "text/plain", "size": 264}, {"uid": "6bb0830d222296bb", "name": "test_completed", "source": "6bb0830d222296bb.png", "type": "image/png", "size": 513181}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "444230cd47245837", "name": "stdout", "source": "444230cd47245837.txt", "type": "text/plain", "size": 12205}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453311552, "stop": 1754453311552, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453311554, "stop": 1754453312890, "duration": 1336}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_high_brightness_mode_settings"}, {"name": "subSuite", "value": "TestEllaJumpHighBrightnessModeSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_high_brightness_mode_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "225ed715c239151a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403313855, "stop": 1754403330303, "duration": 16448}}], "categories": [], "tags": ["smoke"]}, "source": "3fc3f1d2266214de.json", "parameterValues": []}