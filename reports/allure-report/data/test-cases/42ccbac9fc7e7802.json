{"uid": "42ccbac9fc7e7802", "name": "测试switch to equilibrium mode能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switch_to_equilibrium_mode.TestEllaSwitchToEquilibriumMode#test_switch_to_equilibrium_mode", "historyId": "45b57073b776ed5666f2f20a47a4638f", "time": {"start": 1754450565314, "stop": 1754450579258, "duration": 13944}, "description": "switch to equilibrium mode", "descriptionHtml": "<p>switch to equilibrium mode</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450552545, "stop": 1754450565314, "duration": 12769}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450565314, "stop": 1754450565314, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "switch to equilibrium mode", "status": "passed", "steps": [{"name": "执行命令: switch to equilibrium mode", "time": {"start": 1754450565314, "stop": 1754450579024, "duration": 13710}, "status": "passed", "steps": [{"name": "执行命令: switch to equilibrium mode", "time": {"start": 1754450565314, "stop": 1754450578823, "duration": 13509}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450578823, "stop": 1754450579024, "duration": 201}, "status": "passed", "steps": [], "attachments": [{"uid": "9f4b6e4e91bd976c", "name": "测试总结", "source": "9f4b6e4e91bd976c.txt", "type": "text/plain", "size": 245}, {"uid": "7433b560fd929fde", "name": "test_completed", "source": "7433b560fd929fde.png", "type": "image/png", "size": 645807}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1754450579024, "stop": 1754450579026, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450579026, "stop": 1754450579257, "duration": 231}, "status": "passed", "steps": [], "attachments": [{"uid": "2c7736cd2d721799", "name": "测试总结", "source": "2c7736cd2d721799.txt", "type": "text/plain", "size": 245}, {"uid": "be015fe06b0c7268", "name": "test_completed", "source": "be015fe06b0c7268.png", "type": "image/png", "size": 645981}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "a19cc1fc4286843f", "name": "stdout", "source": "a19cc1fc4286843f.txt", "type": "text/plain", "size": 11514}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450579258, "stop": 1754450579258, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450579259, "stop": 1754450580623, "duration": 1364}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_equilibrium_mode"}, {"name": "subSuite", "value": "TestEllaSwitchToEquilibriumMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_equilibrium_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "b58d08985d1ac10b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754400727493, "stop": 1754400743612, "duration": 16119}}], "categories": [], "tags": ["smoke"]}, "source": "42ccbac9fc7e7802.json", "parameterValues": []}