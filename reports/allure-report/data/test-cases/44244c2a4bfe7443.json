{"uid": "44244c2a4bfe7443", "name": "测试what·s the weather today？能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_s_the_weather_today.TestEllaWhatSWeatherToday#test_what_s_the_weather_today", "historyId": "8d12bedb52d3f000f4269afc25f3fe30", "time": {"start": 1754449009816, "stop": 1754449030221, "duration": 20405}, "description": "what·s the weather today？", "descriptionHtml": "<p>what·s the weather today？</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448997082, "stop": 1754449009815, "duration": 12733}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449009815, "stop": 1754449009816, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "what·s the weather today？", "status": "passed", "steps": [{"name": "执行命令: what·s the weather today？", "time": {"start": 1754449009816, "stop": 1754449030019, "duration": 20203}, "status": "passed", "steps": [{"name": "执行命令: what·s the weather today？", "time": {"start": 1754449009816, "stop": 1754449029825, "duration": 20009}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449029825, "stop": 1754449030019, "duration": 194}, "status": "passed", "steps": [], "attachments": [{"uid": "37139160bcdbc4ac", "name": "测试总结", "source": "37139160bcdbc4ac.txt", "type": "text/plain", "size": 272}, {"uid": "9a318c5d0dc45208", "name": "test_completed", "source": "9a318c5d0dc45208.png", "type": "image/png", "size": 544487}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754449030019, "stop": 1754449030021, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449030021, "stop": 1754449030220, "duration": 199}, "status": "passed", "steps": [], "attachments": [{"uid": "8204d4fb776f66cf", "name": "测试总结", "source": "8204d4fb776f66cf.txt", "type": "text/plain", "size": 272}, {"uid": "43f38f58b6d17924", "name": "test_completed", "source": "43f38f58b6d17924.png", "type": "image/png", "size": 544904}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "fdefdaee632e8ba4", "name": "stdout", "source": "fdefdaee632e8ba4.txt", "type": "text/plain", "size": 12429}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449030221, "stop": 1754449030221, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449030222, "stop": 1754449031673, "duration": 1451}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_s_the_weather_today"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_s_the_weather_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "5c0db2c76d746ab7", "status": "passed", "time": {"start": 1754399203309, "stop": 1754399223188, "duration": 19879}}], "categories": [], "tags": ["smoke"]}, "source": "44244c2a4bfe7443.json", "parameterValues": []}