{"uid": "45a77846489fea0", "name": "测试power saving能正常执行", "fullName": "testcases.test_ella.system_coupling.test_power_saving.TestEllaPowerSaving#test_power_saving", "historyId": "8acd3c85f9c9d0b7f252da4466c049e6", "time": {"start": 1754450007334, "stop": 1754450022955, "duration": 15621}, "description": "power saving", "descriptionHtml": "<p>power saving</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449994752, "stop": 1754450007333, "duration": 12581}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450007333, "stop": 1754450007333, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "power saving", "status": "passed", "steps": [{"name": "执行命令: power saving", "time": {"start": 1754450007334, "stop": 1754450022741, "duration": 15407}, "status": "passed", "steps": [{"name": "执行命令: power saving", "time": {"start": 1754450007334, "stop": 1754450022526, "duration": 15192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450022526, "stop": 1754450022741, "duration": 215}, "status": "passed", "steps": [], "attachments": [{"uid": "b58ed40911a3d6bf", "name": "测试总结", "source": "b58ed40911a3d6bf.txt", "type": "text/plain", "size": 273}, {"uid": "4182ae38c0d17df1", "name": "test_completed", "source": "4182ae38c0d17df1.png", "type": "image/png", "size": 563337}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450022741, "stop": 1754450022742, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证PhoneMaster应用已打开", "time": {"start": 1754450022742, "stop": 1754450022742, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450022742, "stop": 1754450022954, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "b05faa01c682e126", "name": "测试总结", "source": "b05faa01c682e126.txt", "type": "text/plain", "size": 273}, {"uid": "886c9e1f6a715dba", "name": "test_completed", "source": "886c9e1f6a715dba.png", "type": "image/png", "size": 563337}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "eb4702b38baba97a", "name": "stdout", "source": "eb4702b38baba97a.txt", "type": "text/plain", "size": 14556}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450022956, "stop": 1754450022956, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450022957, "stop": 1754450024365, "duration": 1408}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_power_saving"}, {"name": "subSuite", "value": "TestEllaPowerSaving"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_power_saving"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "d4ab31adbeaef605", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754400180565, "stop": 1754400205336, "duration": 24771}}], "categories": [], "tags": ["smoke"]}, "source": "45a77846489fea0.json", "parameterValues": []}