{"uid": "465733cfe6bd0a7f", "name": "测试close aivana能正常执行", "fullName": "testcases.test_ella.component_coupling.test_close_aivana.TestEllaCloseAivana#test_close_aivana", "historyId": "d9f01ef1af79559082ce9e9b2e40295f", "time": {"start": 1754446474549, "stop": 1754446508948, "duration": 34399}, "description": "close aivana", "descriptionHtml": "<p>close aivana</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754446462171, "stop": 1754446474548, "duration": 12377}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754446474548, "stop": 1754446474548, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "close aivana", "status": "passed", "steps": [{"name": "执行命令: close aivana", "time": {"start": 1754446474549, "stop": 1754446508765, "duration": 34216}, "status": "passed", "steps": [{"name": "执行命令: close aivana", "time": {"start": 1754446474549, "stop": 1754446508045, "duration": 33496}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446508045, "stop": 1754446508765, "duration": 720}, "status": "passed", "steps": [], "attachments": [{"uid": "91b60c4af90d1cb7", "name": "测试总结", "source": "91b60c4af90d1cb7.txt", "type": "text/plain", "size": 662}, {"uid": "a47e3620a3248861", "name": "test_completed", "source": "a47e3620a3248861.png", "type": "image/png", "size": 496715}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证已打开", "time": {"start": 1754446508765, "stop": 1754446508765, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446508765, "stop": 1754446508948, "duration": 183}, "status": "passed", "steps": [], "attachments": [{"uid": "8c3f49b7d480de28", "name": "测试总结", "source": "8c3f49b7d480de28.txt", "type": "text/plain", "size": 662}, {"uid": "e2468367e98280e9", "name": "test_completed", "source": "e2468367e98280e9.png", "type": "image/png", "size": 496461}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "a7c344e87a90a758", "name": "stdout", "source": "a7c344e87a90a758.txt", "type": "text/plain", "size": 19508}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754446508949, "stop": 1754446508949, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754446508950, "stop": 1754446510293, "duration": 1343}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "设备信息"}, {"name": "story", "value": "设备型号: TECNO CM8"}, {"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_aivana"}, {"name": "subSuite", "value": "TestEllaCloseAivana"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_aivana"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "6eb5fa83c7d1d80c", "status": "passed", "time": {"start": 1754396789473, "stop": 1754396821362, "duration": 31889}}], "categories": [], "tags": ["smoke"]}, "source": "465733cfe6bd0a7f.json", "parameterValues": []}