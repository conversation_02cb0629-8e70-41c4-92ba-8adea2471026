{"uid": "46957ed2e17cc77b", "name": "测试long screenshot能正常执行", "fullName": "testcases.test_ella.system_coupling.test_long_screenshot.TestEllaLongScreenshot#test_long_screenshot", "historyId": "6f7052acfdd45e34e5dded44ad87416e", "time": {"start": 1754449763285, "stop": 1754449779835, "duration": 16550}, "description": "long screenshot", "descriptionHtml": "<p>long screenshot</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449750380, "stop": 1754449763284, "duration": 12904}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449763284, "stop": 1754449763284, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "long screenshot", "status": "passed", "steps": [{"name": "执行命令: long screenshot", "time": {"start": 1754449763285, "stop": 1754449779629, "duration": 16344}, "status": "passed", "steps": [{"name": "执行命令: long screenshot", "time": {"start": 1754449763285, "stop": 1754449779382, "duration": 16097}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449779382, "stop": 1754449779628, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "7b21aa956ab6f6f", "name": "测试总结", "source": "7b21aa956ab6f6f.txt", "type": "text/plain", "size": 454}, {"uid": "ab80dc5abfb0b1f", "name": "test_completed", "source": "ab80dc5abfb0b1f.png", "type": "image/png", "size": 538476}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证文件存在", "time": {"start": 1754449779629, "stop": 1754449779629, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449779629, "stop": 1754449779834, "duration": 205}, "status": "passed", "steps": [], "attachments": [{"uid": "966eae19752a0914", "name": "测试总结", "source": "966eae19752a0914.txt", "type": "text/plain", "size": 454}, {"uid": "9c2de03fb6a0ef5", "name": "test_completed", "source": "9c2de03fb6a0ef5.png", "type": "image/png", "size": 538787}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3cda51d1c59ee42d", "name": "stdout", "source": "3cda51d1c59ee42d.txt", "type": "text/plain", "size": 13308}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449779835, "stop": 1754449779835, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449779836, "stop": 1754449781237, "duration": 1401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_long_screenshot"}, {"name": "subSuite", "value": "TestEllaLongScreenshot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_long_screenshot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "e2f40efc3b65c3eb", "status": "passed", "time": {"start": 1754399945669, "stop": 1754399960605, "duration": 14936}}], "categories": [], "tags": ["smoke"]}, "source": "46957ed2e17cc77b.json", "parameterValues": []}