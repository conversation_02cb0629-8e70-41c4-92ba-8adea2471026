{"uid": "46a3ccf5a87f0c13", "name": "测试play jay chou's music", "fullName": "testcases.test_ella.component_coupling.test_play_jay_chou_s_music.TestEllaOpenMusic#test_play_jay_chou_s_music", "historyId": "b4e75f584d82368436f820de28f92cfd", "time": {"start": 1754447167871, "stop": 1754447191593, "duration": 23722}, "description": "测试play jay chou's music指令", "descriptionHtml": "<p>测试play jay chou's music指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447155568, "stop": 1754447167870, "duration": 12302}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447167870, "stop": 1754447167870, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play jay chou's music指令", "status": "passed", "steps": [{"name": "执行命令: play jay chou's music", "time": {"start": 1754447167871, "stop": 1754447191403, "duration": 23532}, "status": "passed", "steps": [{"name": "执行命令: play jay chou's music", "time": {"start": 1754447167871, "stop": 1754447191178, "duration": 23307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447191178, "stop": 1754447191403, "duration": 225}, "status": "passed", "steps": [], "attachments": [{"uid": "1715bda180b8f469", "name": "测试总结", "source": "1715bda180b8f469.txt", "type": "text/plain", "size": 507}, {"uid": "4367bbd25bb7501a", "name": "test_completed", "source": "4367bbd25bb7501a.png", "type": "image/png", "size": 556358}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447191403, "stop": 1754447191405, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证music已打开", "time": {"start": 1754447191405, "stop": 1754447191405, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447191405, "stop": 1754447191593, "duration": 188}, "status": "passed", "steps": [], "attachments": [{"uid": "a045769bff78f2c0", "name": "测试总结", "source": "a045769bff78f2c0.txt", "type": "text/plain", "size": 507}, {"uid": "5d75ed7894be67e9", "name": "test_completed", "source": "5d75ed7894be67e9.png", "type": "image/png", "size": 556525}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "31969e11a960b74b", "name": "stdout", "source": "31969e11a960b74b.txt", "type": "text/plain", "size": 15153}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447191594, "stop": 1754447191595, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447191596, "stop": 1754447192969, "duration": 1373}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_jay_chou_s_music"}, {"name": "subSuite", "value": "TestEllaOpenMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_jay_chou_s_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "ce6590d0c6c6f198", "status": "passed", "time": {"start": 1754397470669, "stop": 1754397492320, "duration": 21651}}], "categories": [], "tags": ["smoke"]}, "source": "46a3ccf5a87f0c13.json", "parameterValues": []}