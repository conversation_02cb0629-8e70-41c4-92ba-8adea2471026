{"uid": "46a76feb8efb0c84", "name": "测试download basketball能正常执行", "fullName": "testcases.test_ella.third_coupling.test_download_basketball.TestEllaDownloadBasketball#test_download_basketball", "historyId": "6f2c4144233271771cdd01a5c48ea3ca", "time": {"start": 1754451326748, "stop": 1754451341823, "duration": 15075}, "description": "download basketball", "descriptionHtml": "<p>download basketball</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451314271, "stop": 1754451326747, "duration": 12476}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451326747, "stop": 1754451326747, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "download basketball", "status": "passed", "steps": [{"name": "执行命令: download basketball", "time": {"start": 1754451326748, "stop": 1754451341638, "duration": 14890}, "status": "passed", "steps": [{"name": "执行命令: download basketball", "time": {"start": 1754451326748, "stop": 1754451341458, "duration": 14710}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451341458, "stop": 1754451341638, "duration": 180}, "status": "passed", "steps": [], "attachments": [{"uid": "1e90ea92ca9a8b66", "name": "测试总结", "source": "1e90ea92ca9a8b66.txt", "type": "text/plain", "size": 231}, {"uid": "d0110bcf64a26ed7", "name": "test_completed", "source": "d0110bcf64a26ed7.png", "type": "image/png", "size": 432483}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1754451341638, "stop": 1754451341639, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451341639, "stop": 1754451341822, "duration": 183}, "status": "passed", "steps": [], "attachments": [{"uid": "383b7f80a6633ae1", "name": "测试总结", "source": "383b7f80a6633ae1.txt", "type": "text/plain", "size": 231}, {"uid": "c3be0974813e5306", "name": "test_completed", "source": "c3be0974813e5306.png", "type": "image/png", "size": 432424}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "89e48dbf2841eb0a", "name": "stdout", "source": "89e48dbf2841eb0a.txt", "type": "text/plain", "size": 11854}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451341823, "stop": 1754451341823, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451341824, "stop": 1754451343224, "duration": 1400}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_download_basketball"}, {"name": "subSuite", "value": "TestEllaDownloadBasketball"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_download_basketball"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "4ab6aef2b0a9a905", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754401480341, "stop": 1754401493094, "duration": 12753}}], "categories": [], "tags": ["smoke"]}, "source": "46a76feb8efb0c84.json", "parameterValues": []}