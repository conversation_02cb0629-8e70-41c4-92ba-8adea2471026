{"uid": "46ba013c32f0a867", "name": "测试global gdp trends能正常执行", "fullName": "testcases.test_ella.dialogue.test_global_gdp_trends.TestEllaGlobalGdpTrends#test_global_gdp_trends", "historyId": "dee08db8cb0f1293bf864f56326992d5", "time": {"start": 1754447867234, "stop": 1754447884006, "duration": 16772}, "description": "global gdp trends", "descriptionHtml": "<p>global gdp trends</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447854711, "stop": 1754447867233, "duration": 12522}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447867233, "stop": 1754447867233, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "global gdp trends", "status": "passed", "steps": [{"name": "执行命令: global gdp trends", "time": {"start": 1754447867234, "stop": 1754447883808, "duration": 16574}, "status": "passed", "steps": [{"name": "执行命令: global gdp trends", "time": {"start": 1754447867234, "stop": 1754447883604, "duration": 16370}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447883604, "stop": 1754447883807, "duration": 203}, "status": "passed", "steps": [], "attachments": [{"uid": "3bc5c31fb48f35a1", "name": "测试总结", "source": "3bc5c31fb48f35a1.txt", "type": "text/plain", "size": 813}, {"uid": "9efd59bfdbfc3bae", "name": "test_completed", "source": "9efd59bfdbfc3bae.png", "type": "image/png", "size": 714649}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447883808, "stop": 1754447883809, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447883809, "stop": 1754447884006, "duration": 197}, "status": "passed", "steps": [], "attachments": [{"uid": "2b5b772658b8e079", "name": "测试总结", "source": "2b5b772658b8e079.txt", "type": "text/plain", "size": 813}, {"uid": "133e4a89d420d01a", "name": "test_completed", "source": "133e4a89d420d01a.png", "type": "image/png", "size": 722348}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "62c19fbdd9664e1e", "name": "stdout", "source": "62c19fbdd9664e1e.txt", "type": "text/plain", "size": 14485}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754447884007, "stop": 1754447885406, "duration": 1399}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754447884007, "stop": 1754447884007, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_global_gdp_trends"}, {"name": "subSuite", "value": "TestEllaGlobalGdpTrends"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_global_gdp_trends"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "f60123fd513b8334", "status": "passed", "time": {"start": 1754398131113, "stop": 1754398158728, "duration": 27615}}], "categories": [], "tags": ["smoke"]}, "source": "46ba013c32f0a867.json", "parameterValues": []}