{"uid": "4722e4f5730d45cb", "name": "测试play sun be song of jide chord", "fullName": "testcases.test_ella.component_coupling.test_play_sun_be_song_of_jide_chord.TestEllaOpenPlaySunBeSongOfJideChord#test_play_sun_be_song_of_jide_chord", "historyId": "d41a9c89a400d807309a9cecf36c0728", "time": {"start": 1754447308540, "stop": 1754447331031, "duration": 22491}, "description": "测试play sun be song of jide chord指令", "descriptionHtml": "<p>测试play sun be song of jide chord指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447296129, "stop": 1754447308539, "duration": 12410}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447308539, "stop": 1754447308539, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play sun be song of jide chord指令", "status": "passed", "steps": [{"name": "执行命令: play sun be song of jide chord", "time": {"start": 1754447308540, "stop": 1754447330839, "duration": 22299}, "status": "passed", "steps": [{"name": "执行命令: play sun be song of jide chord", "time": {"start": 1754447308540, "stop": 1754447330645, "duration": 22105}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447330645, "stop": 1754447330838, "duration": 193}, "status": "passed", "steps": [], "attachments": [{"uid": "720a52e571966b90", "name": "测试总结", "source": "720a52e571966b90.txt", "type": "text/plain", "size": 525}, {"uid": "b5a96c8bd8a5465a", "name": "test_completed", "source": "b5a96c8bd8a5465a.png", "type": "image/png", "size": 564793}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447330839, "stop": 1754447330840, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754447330840, "stop": 1754447330840, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447330840, "stop": 1754447331031, "duration": 191}, "status": "passed", "steps": [], "attachments": [{"uid": "6c1ef077151624a8", "name": "测试总结", "source": "6c1ef077151624a8.txt", "type": "text/plain", "size": 525}, {"uid": "7829ad0f84aaf6f6", "name": "test_completed", "source": "7829ad0f84aaf6f6.png", "type": "image/png", "size": 564072}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3ac3c8c4bd55155e", "name": "stdout", "source": "3ac3c8c4bd55155e.txt", "type": "text/plain", "size": 15272}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754447331032, "stop": 1754447332429, "duration": 1397}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754447331032, "stop": 1754447331032, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_sun_be_song_of_jide_chord"}, {"name": "subSuite", "value": "TestEllaOpenPlaySunBeSongOfJideChord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_sun_be_song_of_jide_chord"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "3cf415527b3ee855", "status": "passed", "time": {"start": 1754397600817, "stop": 1754397622053, "duration": 21236}}], "categories": [], "tags": ["smoke"]}, "source": "4722e4f5730d45cb.json", "parameterValues": []}