{"uid": "47576954d15fc148", "name": "测试set cover screen apps返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_cover_screen_apps.TestEllaSetCoverScreenApps#test_set_cover_screen_apps", "historyId": "154a720f41d8f5a908552e8c7cf8e781", "time": {"start": 1754454357679, "stop": 1754454371310, "duration": 13631}, "description": "验证set cover screen apps指令返回预期的不支持响应", "descriptionHtml": "<p>验证set cover screen apps指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454345058, "stop": 1754454357679, "duration": 12621}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454357679, "stop": 1754454357679, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set cover screen apps指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set cover screen apps", "time": {"start": 1754454357679, "stop": 1754454371124, "duration": 13445}, "status": "passed", "steps": [{"name": "执行命令: set cover screen apps", "time": {"start": 1754454357679, "stop": 1754454370941, "duration": 13262}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454370941, "stop": 1754454371123, "duration": 182}, "status": "passed", "steps": [], "attachments": [{"uid": "e6ef0c918a48dcca", "name": "测试总结", "source": "e6ef0c918a48dcca.txt", "type": "text/plain", "size": 233}, {"uid": "a6906eb5524c308d", "name": "test_completed", "source": "a6906eb5524c308d.png", "type": "image/png", "size": 494635}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454371124, "stop": 1754454371125, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454371125, "stop": 1754454371310, "duration": 185}, "status": "passed", "steps": [], "attachments": [{"uid": "366a0310f7f6354f", "name": "测试总结", "source": "366a0310f7f6354f.txt", "type": "text/plain", "size": 233}, {"uid": "e0ff7cd1c827487e", "name": "test_completed", "source": "e0ff7cd1c827487e.png", "type": "image/png", "size": 494816}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "c43b5e8e567138e", "name": "stdout", "source": "c43b5e8e567138e.txt", "type": "text/plain", "size": 11324}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454371310, "stop": 1754454371310, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454371312, "stop": 1754454372736, "duration": 1424}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_cover_screen_apps"}, {"name": "subSuite", "value": "TestEllaSetCoverScreenApps"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_cover_screen_apps"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "1fadd1f8d5aebf9b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404366958, "stop": 1754404378077, "duration": 11119}}], "categories": [], "tags": ["smoke"]}, "source": "47576954d15fc148.json", "parameterValues": []}