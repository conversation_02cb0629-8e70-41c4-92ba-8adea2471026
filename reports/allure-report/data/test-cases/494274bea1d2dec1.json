{"uid": "494274bea1d2dec1", "name": "测试set compatibility mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_compatibility_mode.TestEllaSetCompatibilityMode#test_set_compatibility_mode", "historyId": "aecf9a6f67cd29766190cbcc133448d2", "time": {"start": 1754454330251, "stop": 1754454343621, "duration": 13370}, "description": "验证set compatibility mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证set compatibility mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454317752, "stop": 1754454330250, "duration": 12498}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454330250, "stop": 1754454330250, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set compatibility mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set compatibility mode", "time": {"start": 1754454330251, "stop": 1754454343443, "duration": 13192}, "status": "passed", "steps": [{"name": "执行命令: set compatibility mode", "time": {"start": 1754454330251, "stop": 1754454343214, "duration": 12963}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454343214, "stop": 1754454343443, "duration": 229}, "status": "passed", "steps": [], "attachments": [{"uid": "941b8246db713436", "name": "测试总结", "source": "941b8246db713436.txt", "type": "text/plain", "size": 236}, {"uid": "572455cc5133261c", "name": "test_completed", "source": "572455cc5133261c.png", "type": "image/png", "size": 497963}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454343443, "stop": 1754454343444, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454343444, "stop": 1754454343621, "duration": 177}, "status": "passed", "steps": [], "attachments": [{"uid": "cc2e1b6c6c9892ce", "name": "测试总结", "source": "cc2e1b6c6c9892ce.txt", "type": "text/plain", "size": 236}, {"uid": "fbc7ed3743c3b3c9", "name": "test_completed", "source": "fbc7ed3743c3b3c9.png", "type": "image/png", "size": 498171}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "4eb6562c6381e3b7", "name": "stdout", "source": "4eb6562c6381e3b7.txt", "type": "text/plain", "size": 11340}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754454343622, "stop": 1754454345052, "duration": 1430}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754454343622, "stop": 1754454343622, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_compatibility_mode"}, {"name": "subSuite", "value": "TestEllaSetCompatibilityMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_compatibility_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "e293087c57db4456", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404341419, "stop": 1754404352417, "duration": 10998}}], "categories": [], "tags": ["smoke"]}, "source": "494274bea1d2dec1.json", "parameterValues": []}