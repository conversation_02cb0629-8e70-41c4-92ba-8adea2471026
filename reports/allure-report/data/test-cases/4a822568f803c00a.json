{"uid": "4a822568f803c00a", "name": "测试more settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_more_settings.TestEllaMoreSettings#test_more_settings", "historyId": "6d49aaf4a5e11b32b961aa0aa3dbbf6e", "time": {"start": 1754453452902, "stop": 1754453469776, "duration": 16874}, "description": "验证more settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证more settings指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453440727, "stop": 1754453452902, "duration": 12175}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453452902, "stop": 1754453452902, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证more settings指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: more settings", "time": {"start": 1754453452902, "stop": 1754453469613, "duration": 16711}, "status": "passed", "steps": [{"name": "执行命令: more settings", "time": {"start": 1754453452902, "stop": 1754453469425, "duration": 16523}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453469425, "stop": 1754453469613, "duration": 188}, "status": "passed", "steps": [], "attachments": [{"uid": "397ce4e0c7538314", "name": "测试总结", "source": "397ce4e0c7538314.txt", "type": "text/plain", "size": 497}, {"uid": "d3385d2a77967e25", "name": "test_completed", "source": "d3385d2a77967e25.png", "type": "image/png", "size": 455502}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453469613, "stop": 1754453469614, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453469614, "stop": 1754453469775, "duration": 161}, "status": "passed", "steps": [], "attachments": [{"uid": "5f5df3405f9d63d", "name": "测试总结", "source": "5f5df3405f9d63d.txt", "type": "text/plain", "size": 497}, {"uid": "850bf890cfd26e4", "name": "test_completed", "source": "850bf890cfd26e4.png", "type": "image/png", "size": 455502}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "510bde793194b33f", "name": "stdout", "source": "510bde793194b33f.txt", "type": "text/plain", "size": 13714}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453469776, "stop": 1754453469776, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453469777, "stop": 1754453471141, "duration": 1364}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_more_settings"}, {"name": "subSuite", "value": "TestEllaMoreSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_more_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "9dccbf5fb1fc170c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403476099, "stop": 1754403491474, "duration": 15375}}], "categories": [], "tags": ["smoke"]}, "source": "4a822568f803c00a.json", "parameterValues": []}