{"uid": "4aabdb885aa35939", "name": "测试close folax能正常执行", "fullName": "testcases.test_ella.component_coupling.test_close_folax.TestEllaCloseFolax#test_close_folax", "historyId": "8b2d3084bb429ea5def5db416bbf10a7", "time": {"start": 1754396879640, "stop": 1754396910986, "duration": 31346}, "description": "close folax", "descriptionHtml": "<p>close folax</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754396866780, "stop": 1754396879639, "duration": 12859}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754396879639, "stop": 1754396879639, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "close folax", "status": "passed", "steps": [{"name": "执行命令: close folax", "time": {"start": 1754396879640, "stop": 1754396910673, "duration": 31033}, "status": "passed", "steps": [{"name": "执行命令: close folax", "time": {"start": 1754396879640, "stop": 1754396910374, "duration": 30734}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754396910374, "stop": 1754396910672, "duration": 298}, "status": "passed", "steps": [], "attachments": [{"uid": "758a5351a40b19c5", "name": "测试总结", "source": "758a5351a40b19c5.txt", "type": "text/plain", "size": 677}, {"uid": "869f33b218e8b68a", "name": "test_completed", "source": "869f33b218e8b68a.png", "type": "image/png", "size": 520782}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证已打开", "time": {"start": 1754396910673, "stop": 1754396910673, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754396910673, "stop": 1754396910985, "duration": 312}, "status": "passed", "steps": [], "attachments": [{"uid": "b4b7c22355c051de", "name": "测试总结", "source": "b4b7c22355c051de.txt", "type": "text/plain", "size": 677}, {"uid": "9838c7566fa14db9", "name": "test_completed", "source": "9838c7566fa14db9.png", "type": "image/png", "size": 520421}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "5dcfcbe36fc014c6", "name": "stdout", "source": "5dcfcbe36fc014c6.txt", "type": "text/plain", "size": 18985}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754396910989, "stop": 1754396910989, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754396910991, "stop": 1754396912264, "duration": 1273}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_folax"}, {"name": "subSuite", "value": "TestEllaCloseFolax"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_folax"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "4aabdb885aa35939.json", "parameterValues": []}