{"uid": "4b3374b2c47a62f0", "name": "测试play news", "fullName": "testcases.test_ella.dialogue.test_play_news.TestEllaOpenPlayNews#test_play_news", "historyId": "fee3033814a8b17ff8c8abe6bbcdc839", "time": {"start": 1754398624023, "stop": 1754398639789, "duration": 15766}, "description": "测试play news指令", "descriptionHtml": "<p>测试play news指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398611071, "stop": 1754398624021, "duration": 12950}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398624021, "stop": 1754398624021, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play news指令", "status": "passed", "steps": [{"name": "执行命令: play news", "time": {"start": 1754398624023, "stop": 1754398639479, "duration": 15456}, "status": "passed", "steps": [{"name": "执行命令: play news", "time": {"start": 1754398624023, "stop": 1754398639229, "duration": 15206}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398639229, "stop": 1754398639478, "duration": 249}, "status": "passed", "steps": [], "attachments": [{"uid": "524117ac97bbfd5c", "name": "测试总结", "source": "524117ac97bbfd5c.txt", "type": "text/plain", "size": 575}, {"uid": "7b07cb60e427f837", "name": "test_completed", "source": "7b07cb60e427f837.png", "type": "image/png", "size": 514014}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398639479, "stop": 1754398639486, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398639486, "stop": 1754398639788, "duration": 302}, "status": "passed", "steps": [], "attachments": [{"uid": "b3039e8ce38a8e2a", "name": "测试总结", "source": "b3039e8ce38a8e2a.txt", "type": "text/plain", "size": 575}, {"uid": "a8979901223650d6", "name": "test_completed", "source": "a8979901223650d6.png", "type": "image/png", "size": 514026}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "2c6ef2626f61e9f3", "name": "stdout", "source": "2c6ef2626f61e9f3.txt", "type": "text/plain", "size": 14046}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398639790, "stop": 1754398639790, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398639793, "stop": 1754398641080, "duration": 1287}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_news"}, {"name": "subSuite", "value": "TestEllaOpenPlayNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_news"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "4b3374b2c47a62f0.json", "parameterValues": []}