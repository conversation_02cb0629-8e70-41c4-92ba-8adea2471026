{"uid": "4bf08d2af72213b8", "name": "测试who is harry potter能正常执行", "fullName": "testcases.test_ella.dialogue.test_who_is_harry_potter.TestEllaWhoIsHarryPotter#test_who_is_harry_potter", "historyId": "56a09613cdb882018377e1c2c4e78472", "time": {"start": 1754399353992, "stop": 1754399369403, "duration": 15411}, "description": "who is harry potter", "descriptionHtml": "<p>who is harry potter</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399340724, "stop": 1754399353990, "duration": 13266}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399353990, "stop": 1754399353990, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "who is harry potter", "status": "passed", "steps": [{"name": "执行命令: who is harry potter", "time": {"start": 1754399353992, "stop": 1754399369092, "duration": 15100}, "status": "passed", "steps": [{"name": "执行命令: who is harry potter", "time": {"start": 1754399353992, "stop": 1754399368741, "duration": 14749}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399368741, "stop": 1754399369091, "duration": 350}, "status": "passed", "steps": [], "attachments": [{"uid": "62898e328bbc311a", "name": "测试总结", "source": "62898e328bbc311a.txt", "type": "text/plain", "size": 865}, {"uid": "1f59f4945aacb7cb", "name": "test_completed", "source": "1f59f4945aacb7cb.png", "type": "image/png", "size": 676764}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399369092, "stop": 1754399369097, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399369097, "stop": 1754399369401, "duration": 304}, "status": "passed", "steps": [], "attachments": [{"uid": "26664e25182ca479", "name": "测试总结", "source": "26664e25182ca479.txt", "type": "text/plain", "size": 865}, {"uid": "ec75e37c8bb1aab0", "name": "test_completed", "source": "ec75e37c8bb1aab0.png", "type": "image/png", "size": 675667}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "a56f5a115a628518", "name": "stdout", "source": "a56f5a115a628518.txt", "type": "text/plain", "size": 14700}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399369405, "stop": 1754399369405, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399369409, "stop": 1754399370684, "duration": 1275}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_who_is_harry_potter"}, {"name": "subSuite", "value": "TestEllaWhoIsHarry<PERSON>otter"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_who_is_harry_potter"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "4bf08d2af72213b8.json", "parameterValues": []}