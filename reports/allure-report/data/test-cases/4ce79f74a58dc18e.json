{"uid": "4ce79f74a58dc18e", "name": "测试switched to data mode能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switched_to_data_mode.TestEllaSwitchedDataMode#test_switched_to_data_mode", "historyId": "063471c2e7f2d00ecd08e780860e0cf2", "time": {"start": 1754450743050, "stop": 1754450758408, "duration": 15358}, "description": "switched to data mode", "descriptionHtml": "<p>switched to data mode</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450730340, "stop": 1754450743050, "duration": 12710}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450743050, "stop": 1754450743050, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "switched to data mode", "status": "passed", "steps": [{"name": "执行命令: switched to data mode", "time": {"start": 1754450743050, "stop": 1754450758224, "duration": 15174}, "status": "passed", "steps": [{"name": "执行命令: switched to data mode", "time": {"start": 1754450743050, "stop": 1754450758011, "duration": 14961}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450758011, "stop": 1754450758223, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "d020b077b4368014", "name": "测试总结", "source": "d020b077b4368014.txt", "type": "text/plain", "size": 204}, {"uid": "a720e8c286c8e897", "name": "test_completed", "source": "a720e8c286c8e897.png", "type": "image/png", "size": 539454}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450758224, "stop": 1754450758225, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754450758225, "stop": 1754450758225, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450758225, "stop": 1754450758408, "duration": 183}, "status": "passed", "steps": [], "attachments": [{"uid": "6793f79656bac35c", "name": "测试总结", "source": "6793f79656bac35c.txt", "type": "text/plain", "size": 204}, {"uid": "fda2b2cf0e6d213e", "name": "test_completed", "source": "fda2b2cf0e6d213e.png", "type": "image/png", "size": 539385}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "f2099e1d47e8e5b9", "name": "stdout", "source": "f2099e1d47e8e5b9.txt", "type": "text/plain", "size": 13438}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450758409, "stop": 1754450758409, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450758410, "stop": 1754450759772, "duration": 1362}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switched_to_data_mode"}, {"name": "subSuite", "value": "TestEllaSwitchedDataMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switched_to_data_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "4105704213c94fc", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Mobile data is turned on now']\nassert False", "time": {"start": 1754400899575, "stop": 1754400911739, "duration": 12164}}], "categories": [], "tags": ["smoke"]}, "source": "4ce79f74a58dc18e.json", "parameterValues": []}