{"uid": "4cf52d0e6ea4d2ce", "name": "测试who is j k rowling能正常执行", "fullName": "testcases.test_ella.dialogue.test_who_is_j_k_rowling.TestEllaWhoIsJKRowling#test_who_is_j_k_rowling", "historyId": "1a5cbbb97cbe59e003ae71750a8d910f", "time": {"start": 1754399383324, "stop": 1754399409890, "duration": 26566}, "description": "who is j k rowling", "descriptionHtml": "<p>who is j k rowling</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399370688, "stop": 1754399383323, "duration": 12635}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399383324, "stop": 1754399383324, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "who is j k rowling", "status": "passed", "steps": [{"name": "执行命令: who is j k rowling", "time": {"start": 1754399383324, "stop": 1754399409590, "duration": 26266}, "status": "passed", "steps": [{"name": "执行命令: who is j k rowling", "time": {"start": 1754399383324, "stop": 1754399409307, "duration": 25983}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399409307, "stop": 1754399409588, "duration": 281}, "status": "passed", "steps": [], "attachments": [{"uid": "e65bb5ae3e438894", "name": "测试总结", "source": "e65bb5ae3e438894.txt", "type": "text/plain", "size": 809}, {"uid": "d0242f6c29bc90c", "name": "test_completed", "source": "d0242f6c29bc90c.png", "type": "image/png", "size": 673576}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399409590, "stop": 1754399409594, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399409594, "stop": 1754399409889, "duration": 295}, "status": "passed", "steps": [], "attachments": [{"uid": "d0f5e6a46f2284fa", "name": "测试总结", "source": "d0f5e6a46f2284fa.txt", "type": "text/plain", "size": 809}, {"uid": "88d34248fba10c1d", "name": "test_completed", "source": "88d34248fba10c1d.png", "type": "image/png", "size": 674272}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "35e82c189c249bb0", "name": "stdout", "source": "35e82c189c249bb0.txt", "type": "text/plain", "size": 14470}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399409892, "stop": 1754399409892, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399409895, "stop": 1754399411181, "duration": 1286}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_who_is_j_k_rowling"}, {"name": "subSuite", "value": "TestEllaWhoIsJKRowling"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_who_is_j_k_rowling"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "4cf52d0e6ea4d2ce.json", "parameterValues": []}