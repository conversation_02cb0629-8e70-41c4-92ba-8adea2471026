{"uid": "4d48f2b9ddae972d", "name": "测试Enable Call on Hold返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_call_on_hold.TestEllaEnableCallHold#test_enable_call_on_hold", "historyId": "2bf170e8c0013ab361afb23f8f059db8", "time": {"start": 1754452667250, "stop": 1754452690029, "duration": 22779}, "description": "验证Enable Call on Hold指令返回预期的不支持响应", "descriptionHtml": "<p>验证Enable Call on Hold指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452655190, "stop": 1754452667249, "duration": 12059}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452667249, "stop": 1754452667249, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证Enable Call on Hold指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: Enable Call on Hold", "time": {"start": 1754452667250, "stop": 1754452689843, "duration": 22593}, "status": "passed", "steps": [{"name": "执行命令: Enable Call on Hold", "time": {"start": 1754452667250, "stop": 1754452689656, "duration": 22406}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452689656, "stop": 1754452689842, "duration": 186}, "status": "passed", "steps": [], "attachments": [{"uid": "e8a6bd950c23190a", "name": "测试总结", "source": "e8a6bd950c23190a.txt", "type": "text/plain", "size": 226}, {"uid": "8b6d82552c7298dc", "name": "test_completed", "source": "8b6d82552c7298dc.png", "type": "image/png", "size": 480750}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452689843, "stop": 1754452689844, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452689844, "stop": 1754452690028, "duration": 184}, "status": "passed", "steps": [], "attachments": [{"uid": "8efe7e6d445c7656", "name": "测试总结", "source": "8efe7e6d445c7656.txt", "type": "text/plain", "size": 226}, {"uid": "40b47dbd5b286f5a", "name": "test_completed", "source": "40b47dbd5b286f5a.png", "type": "image/png", "size": 479665}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "7d240607b441162b", "name": "stdout", "source": "7d240607b441162b.txt", "type": "text/plain", "size": 11688}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452690029, "stop": 1754452690029, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452690030, "stop": 1754452691410, "duration": 1380}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_call_on_hold"}, {"name": "subSuite", "value": "TestEllaEnableCallHold"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_call_on_hold"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "61d37c7565c0e541", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402695765, "stop": 1754402717667, "duration": 21902}}], "categories": [], "tags": ["smoke"]}, "source": "4d48f2b9ddae972d.json", "parameterValues": []}