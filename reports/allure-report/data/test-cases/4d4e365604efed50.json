{"uid": "4d4e365604efed50", "name": "测试set screen to minimum brightness返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_screen_to_minimum_brightness.TestEllaSetScreenMinimumBrightness#test_set_screen_to_minimum_brightness", "historyId": "544fc8b021d2dbcaf295cd05b798f816", "time": {"start": 1754454966147, "stop": 1754454980412, "duration": 14265}, "description": "验证set screen to minimum brightness指令返回预期的不支持响应", "descriptionHtml": "<p>验证set screen to minimum brightness指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454954303, "stop": 1754454966146, "duration": 11843}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454966146, "stop": 1754454966146, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set screen to minimum brightness指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set screen to minimum brightness", "time": {"start": 1754454966147, "stop": 1754454980240, "duration": 14093}, "status": "passed", "steps": [{"name": "执行命令: set screen to minimum brightness", "time": {"start": 1754454966147, "stop": 1754454980035, "duration": 13888}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454980035, "stop": 1754454980240, "duration": 205}, "status": "passed", "steps": [], "attachments": [{"uid": "1d4a29b1018c3858", "name": "测试总结", "source": "1d4a29b1018c3858.txt", "type": "text/plain", "size": 243}, {"uid": "8803b8af7c17095a", "name": "test_completed", "source": "8803b8af7c17095a.png", "type": "image/png", "size": 482549}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454980240, "stop": 1754454980242, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454980242, "stop": 1754454980411, "duration": 169}, "status": "passed", "steps": [], "attachments": [{"uid": "2db3534022c48f40", "name": "测试总结", "source": "2db3534022c48f40.txt", "type": "text/plain", "size": 243}, {"uid": "a096e9939d49e90e", "name": "test_completed", "source": "a096e9939d49e90e.png", "type": "image/png", "size": 481369}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "5a73b4a7948b9317", "name": "stdout", "source": "5a73b4a7948b9317.txt", "type": "text/plain", "size": 12119}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454980412, "stop": 1754454980412, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454980413, "stop": 1754454981759, "duration": 1346}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_screen_to_minimum_brightness"}, {"name": "subSuite", "value": "TestEllaSetScreenMinimumBrightness"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_screen_to_minimum_brightness"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "eb491126c83dca0a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404979662, "stop": 1754404997002, "duration": 17340}}], "categories": [], "tags": ["smoke"]}, "source": "4d4e365604efed50.json", "parameterValues": []}