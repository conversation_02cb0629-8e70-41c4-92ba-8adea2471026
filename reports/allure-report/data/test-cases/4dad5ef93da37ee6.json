{"uid": "4dad5ef93da37ee6", "name": "测试Help me write an email to make an appointment for a visit能正常执行", "fullName": "testcases.test_ella.dialogue.test_help_me_write_an_email_to_make_an_appointment_for_a_visit.TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit#test_help_me_write_an_email_to_make_an_appointment_for_a_visit", "historyId": "12eb3852c333145c5906579f2346c37a", "time": {"start": 1754398200943, "stop": 1754398214377, "duration": 13434}, "description": "Help me write an email to make an appointment for a visit", "descriptionHtml": "<p>Help me write an email to make an appointment for a visit</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398187772, "stop": 1754398200942, "duration": 13170}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398200942, "stop": 1754398200942, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "Help me write an email to make an appointment for a visit", "status": "passed", "steps": [{"name": "执行命令: Help me write an email to make an appointment for a visit", "time": {"start": 1754398200944, "stop": 1754398214055, "duration": 13111}, "status": "passed", "steps": [{"name": "执行命令: Help me write an email to make an appointment for a visit", "time": {"start": 1754398200944, "stop": 1754398213803, "duration": 12859}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398213803, "stop": 1754398214054, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "d6a10647a0771aee", "name": "测试总结", "source": "d6a10647a0771aee.txt", "type": "text/plain", "size": 1157}, {"uid": "aac8d50141873b6", "name": "test_completed", "source": "aac8d50141873b6.png", "type": "image/png", "size": 722213}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398214055, "stop": 1754398214061, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398214061, "stop": 1754398214376, "duration": 315}, "status": "passed", "steps": [], "attachments": [{"uid": "d88d7357e9be20a1", "name": "测试总结", "source": "d88d7357e9be20a1.txt", "type": "text/plain", "size": 1157}, {"uid": "3f98b130df7c8233", "name": "test_completed", "source": "3f98b130df7c8233.png", "type": "image/png", "size": 722370}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "5533934243a78847", "name": "stdout", "source": "5533934243a78847.txt", "type": "text/plain", "size": 16248}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398214379, "stop": 1754398214379, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398214381, "stop": 1754398215671, "duration": 1290}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_help_me_write_an_email_to_make_an_appointment_for_a_visit"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_help_me_write_an_email_to_make_an_appointment_for_a_visit"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "4dad5ef93da37ee6.json", "parameterValues": []}