{"uid": "4f13cb22f11a7ed0", "name": "测试stop music能正常执行", "fullName": "testcases.test_ella.dialogue.test_stop_music.TestEllaStopMusic#test_stop_music", "historyId": "794f685415bbcd702feae0b55a4dd537", "time": {"start": 1754448597918, "stop": 1754448611893, "duration": 13975}, "description": "stop music", "descriptionHtml": "<p>stop music</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448585301, "stop": 1754448597916, "duration": 12615}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448597917, "stop": 1754448597917, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "stop music", "status": "passed", "steps": [{"name": "执行命令: stop music", "time": {"start": 1754448597919, "stop": 1754448611689, "duration": 13770}, "status": "passed", "steps": [{"name": "执行命令: stop music", "time": {"start": 1754448597919, "stop": 1754448611487, "duration": 13568}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448611487, "stop": 1754448611689, "duration": 202}, "status": "passed", "steps": [], "attachments": [{"uid": "3789aac2aa5272fc", "name": "测试总结", "source": "3789aac2aa5272fc.txt", "type": "text/plain", "size": 177}, {"uid": "ee385c69ce1f1b58", "name": "test_completed", "source": "ee385c69ce1f1b58.png", "type": "image/png", "size": 568731}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448611689, "stop": 1754448611690, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448611690, "stop": 1754448611893, "duration": 203}, "status": "passed", "steps": [], "attachments": [{"uid": "f201f182d3d3410e", "name": "测试总结", "source": "f201f182d3d3410e.txt", "type": "text/plain", "size": 177}, {"uid": "837b9500782cae9e", "name": "test_completed", "source": "837b9500782cae9e.png", "type": "image/png", "size": 568485}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "744b6832c2a54e94", "name": "stdout", "source": "744b6832c2a54e94.txt", "type": "text/plain", "size": 11401}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448611893, "stop": 1754448611893, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448611895, "stop": 1754448613304, "duration": 1409}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_stop_music"}, {"name": "subSuite", "value": "TestEllaStopMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_stop_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "51941e8f0d4c7860", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['No music playing in the background.']\nassert False", "time": {"start": 1754398828682, "stop": 1754398841380, "duration": 12698}}], "categories": [], "tags": ["smoke"]}, "source": "4f13cb22f11a7ed0.json", "parameterValues": []}