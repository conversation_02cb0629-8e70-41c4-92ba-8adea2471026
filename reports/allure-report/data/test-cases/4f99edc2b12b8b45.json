{"uid": "4f99edc2b12b8b45", "name": "测试set battery saver settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_battery_saver_settings.TestEllaSetBatterySaverSettings#test_set_battery_saver_settings", "historyId": "e60dab4e55edacbecf632d4d22f368e6", "time": {"start": 1754454234998, "stop": 1754454251005, "duration": 16007}, "description": "验证set battery saver settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证set battery saver settings指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454222553, "stop": 1754454234998, "duration": 12445}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454234998, "stop": 1754454234998, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set battery saver settings指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set battery saver settings", "time": {"start": 1754454234999, "stop": 1754454250834, "duration": 15835}, "status": "passed", "steps": [{"name": "执行命令: set battery saver settings", "time": {"start": 1754454234999, "stop": 1754454250591, "duration": 15592}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454250591, "stop": 1754454250834, "duration": 243}, "status": "passed", "steps": [], "attachments": [{"uid": "edaab437bc1a1649", "name": "测试总结", "source": "edaab437bc1a1649.txt", "type": "text/plain", "size": 239}, {"uid": "3ccb967ee1aba715", "name": "test_completed", "source": "3ccb967ee1aba715.png", "type": "image/png", "size": 498227}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454250834, "stop": 1754454250835, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454250835, "stop": 1754454251004, "duration": 169}, "status": "passed", "steps": [], "attachments": [{"uid": "610bfe0eba787651", "name": "测试总结", "source": "610bfe0eba787651.txt", "type": "text/plain", "size": 239}, {"uid": "b2aa4005a03a6a80", "name": "test_completed", "source": "b2aa4005a03a6a80.png", "type": "image/png", "size": 497881}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "df9c326ca74933fe", "name": "stdout", "source": "df9c326ca74933fe.txt", "type": "text/plain", "size": 11875}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454251005, "stop": 1754454251005, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454251006, "stop": 1754454252395, "duration": 1389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_battery_saver_settings"}, {"name": "subSuite", "value": "TestEllaSetBatterySaverSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_battery_saver_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "40c9df1680c5103b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404243130, "stop": 1754404260113, "duration": 16983}}], "categories": [], "tags": ["smoke"]}, "source": "4f99edc2b12b8b45.json", "parameterValues": []}