{"uid": "4fa73543ebc54fd5", "name": "测试jump to auto rotate screen settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_auto_rotate_screen_settings.TestEllaJumpAutoRotateScreenSettings#test_jump_to_auto_rotate_screen_settings", "historyId": "ab2195315637668cad08b0606ef7ff17", "time": {"start": 1754453165713, "stop": 1754453188918, "duration": 23205}, "description": "验证jump to auto rotate screen settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to auto rotate screen settings指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453153679, "stop": 1754453165712, "duration": 12033}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453165712, "stop": 1754453165712, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证jump to auto rotate screen settings指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: jump to auto rotate screen settings", "time": {"start": 1754453165713, "stop": 1754453188737, "duration": 23024}, "status": "passed", "steps": [{"name": "执行命令: jump to auto rotate screen settings", "time": {"start": 1754453165713, "stop": 1754453188519, "duration": 22806}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453188519, "stop": 1754453188737, "duration": 218}, "status": "passed", "steps": [], "attachments": [{"uid": "397657d98edbeae5", "name": "测试总结", "source": "397657d98edbeae5.txt", "type": "text/plain", "size": 259}, {"uid": "509479079af1e7b7", "name": "test_completed", "source": "509479079af1e7b7.png", "type": "image/png", "size": 506589}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453188737, "stop": 1754453188738, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453188739, "stop": 1754453188917, "duration": 178}, "status": "passed", "steps": [], "attachments": [{"uid": "1406dd55f33d55ba", "name": "测试总结", "source": "1406dd55f33d55ba.txt", "type": "text/plain", "size": 259}, {"uid": "3823a496fb759a50", "name": "test_completed", "source": "3823a496fb759a50.png", "type": "image/png", "size": 506726}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "49dc661cd80ed206", "name": "stdout", "source": "49dc661cd80ed206.txt", "type": "text/plain", "size": 11871}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453188918, "stop": 1754453188918, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453188919, "stop": 1754453190258, "duration": 1339}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_auto_rotate_screen_settings"}, {"name": "subSuite", "value": "TestEllaJumpAutoRotateScreenSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_auto_rotate_screen_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "92f44c195b9b178d", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403183004, "stop": 1754403204054, "duration": 21050}}], "categories": [], "tags": ["smoke"]}, "source": "4fa73543ebc54fd5.json", "parameterValues": []}