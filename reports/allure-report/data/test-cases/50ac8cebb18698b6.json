{"uid": "50ac8cebb18698b6", "name": "测试how to say i love you in french能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_to_say_i_love_you_in_french.TestEllaHowSayILoveYouFrench#test_how_to_say_i_love_you_in_french", "historyId": "78de5607a6208f59723ba6cf4fcf09c4", "time": {"start": 1754448143739, "stop": 1754448155894, "duration": 12155}, "description": "how to say i love you in french", "descriptionHtml": "<p>how to say i love you in french</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448131062, "stop": 1754448143738, "duration": 12676}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448143738, "stop": 1754448143738, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "how to say i love you in french", "status": "passed", "steps": [{"name": "执行命令: how to say i love you in french", "time": {"start": 1754448143739, "stop": 1754448155712, "duration": 11973}, "status": "passed", "steps": [{"name": "执行命令: how to say i love you in french", "time": {"start": 1754448143739, "stop": 1754448155521, "duration": 11782}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448155521, "stop": 1754448155712, "duration": 191}, "status": "passed", "steps": [], "attachments": [{"uid": "32f114e4e29b037c", "name": "测试总结", "source": "32f114e4e29b037c.txt", "type": "text/plain", "size": 188}, {"uid": "b80fb3ac4100491", "name": "test_completed", "source": "b80fb3ac4100491.png", "type": "image/png", "size": 486488}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448155712, "stop": 1754448155713, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448155713, "stop": 1754448155892, "duration": 179}, "status": "passed", "steps": [], "attachments": [{"uid": "86264e7dd9570cf3", "name": "测试总结", "source": "86264e7dd9570cf3.txt", "type": "text/plain", "size": 188}, {"uid": "354b19c95fc8453b", "name": "test_completed", "source": "354b19c95fc8453b.png", "type": "image/png", "size": 486488}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "e7d56dc8c68b6066", "name": "stdout", "source": "e7d56dc8c68b6066.txt", "type": "text/plain", "size": 11316}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448155894, "stop": 1754448155894, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448155898, "stop": 1754448157350, "duration": 1452}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_to_say_i_love_you_in_french"}, {"name": "subSuite", "value": "TestEllaHowSayILoveYouFrench"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_to_say_i_love_you_in_french"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "62acaa3041ed52b0", "status": "passed", "time": {"start": 1754398412864, "stop": 1754398425777, "duration": 12913}}], "categories": [], "tags": ["smoke"]}, "source": "50ac8cebb18698b6.json", "parameterValues": []}