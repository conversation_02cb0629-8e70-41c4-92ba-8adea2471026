{"uid": "50ef96927d3bd86f", "name": "测试how is the wheather today能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_is_the_wheather_today.TestEllaHowIsWheatherToday#test_how_is_the_wheather_today", "historyId": "f7282303534c1c8599c3343608e6f453", "time": {"start": 1754398290921, "stop": 1754398303892, "duration": 12971}, "description": "how is the wheather today", "descriptionHtml": "<p>how is the wheather today</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398277961, "stop": 1754398290920, "duration": 12959}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398290920, "stop": 1754398290920, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "how is the wheather today", "status": "passed", "steps": [{"name": "执行命令: how is the wheather today", "time": {"start": 1754398290921, "stop": 1754398303568, "duration": 12647}, "status": "passed", "steps": [{"name": "执行命令: how is the wheather today", "time": {"start": 1754398290921, "stop": 1754398303299, "duration": 12378}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398303299, "stop": 1754398303567, "duration": 268}, "status": "passed", "steps": [], "attachments": [{"uid": "6fa0c8995734123", "name": "测试总结", "source": "6fa0c8995734123.txt", "type": "text/plain", "size": 255}, {"uid": "a9c2dc20b09cf7db", "name": "test_completed", "source": "a9c2dc20b09cf7db.png", "type": "image/png", "size": 506487}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398303568, "stop": 1754398303578, "duration": 10}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398303578, "stop": 1754398303890, "duration": 312}, "status": "passed", "steps": [], "attachments": [{"uid": "fadd288478a975b7", "name": "测试总结", "source": "fadd288478a975b7.txt", "type": "text/plain", "size": 255}, {"uid": "3f0fdebe973e3fe5", "name": "test_completed", "source": "3f0fdebe973e3fe5.png", "type": "image/png", "size": 507042}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "e0d9090110994a24", "name": "stdout", "source": "e0d9090110994a24.txt", "type": "text/plain", "size": 11968}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398303894, "stop": 1754398303894, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398303897, "stop": 1754398305155, "duration": 1258}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_is_the_wheather_today"}, {"name": "subSuite", "value": "TestEllaHowIsWheatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_is_the_wheather_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "50ef96927d3bd86f.json", "parameterValues": []}