{"uid": "51910e205a82d03e", "name": "测试start screen recording能正常执行", "fullName": "testcases.test_ella.system_coupling.test_start_screen_recording.TestEllaStartScreenRecording#test_start_screen_recording", "historyId": "5fe7611f5b7d3ef438ce938b66e0b99f", "time": {"start": 1754450232215, "stop": 1754450250336, "duration": 18121}, "description": "start screen recording", "descriptionHtml": "<p>start screen recording</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450219646, "stop": 1754450232214, "duration": 12568}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450232214, "stop": 1754450232214, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "start screen recording", "status": "passed", "steps": [{"name": "执行命令: start screen recording", "time": {"start": 1754450232215, "stop": 1754450250152, "duration": 17937}, "status": "passed", "steps": [{"name": "执行命令: start screen recording", "time": {"start": 1754450232215, "stop": 1754450249959, "duration": 17744}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450249959, "stop": 1754450250152, "duration": 193}, "status": "passed", "steps": [], "attachments": [{"uid": "d3ef11b424b6b0e1", "name": "测试总结", "source": "d3ef11b424b6b0e1.txt", "type": "text/plain", "size": 192}, {"uid": "d077411525ec5c79", "name": "test_completed", "source": "d077411525ec5c79.png", "type": "image/png", "size": 616258}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450250152, "stop": 1754450250154, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754450250154, "stop": 1754450250154, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450250154, "stop": 1754450250336, "duration": 182}, "status": "passed", "steps": [], "attachments": [{"uid": "f2b2a4c379d03fbe", "name": "测试总结", "source": "f2b2a4c379d03fbe.txt", "type": "text/plain", "size": 192}, {"uid": "e3c081ea9445f5e2", "name": "test_completed", "source": "e3c081ea9445f5e2.png", "type": "image/png", "size": 616445}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "71757da93e27e49e", "name": "stdout", "source": "71757da93e27e49e.txt", "type": "text/plain", "size": 11841}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450250337, "stop": 1754450250337, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450250338, "stop": 1754450251732, "duration": 1394}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_start_screen_recording"}, {"name": "subSuite", "value": "TestEllaStartScreenRecording"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_start_screen_recording"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "c29e74db44408169", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording started']\nassert False", "time": {"start": 1754400405853, "stop": 1754400422784, "duration": 16931}}], "categories": [], "tags": ["smoke"]}, "source": "51910e205a82d03e.json", "parameterValues": []}