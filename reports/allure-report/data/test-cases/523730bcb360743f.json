{"uid": "523730bcb360743f", "name": "测试whats the weather today能正常执行", "fullName": "testcases.test_ella.dialogue.test_whats_the_weather_today.TestEllaWhatsWeatherToday#test_whats_the_weather_today", "historyId": "acca7d0b06a28ad8e6ccfe3c35828ce1", "time": {"start": 1754399319034, "stop": 1754399339460, "duration": 20426}, "description": "whats the weather today", "descriptionHtml": "<p>whats the weather today</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399306066, "stop": 1754399319033, "duration": 12967}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399319033, "stop": 1754399319033, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "whats the weather today", "status": "passed", "steps": [{"name": "执行命令: whats the weather today", "time": {"start": 1754399319034, "stop": 1754399339185, "duration": 20151}, "status": "passed", "steps": [{"name": "执行命令: whats the weather today", "time": {"start": 1754399319034, "stop": 1754399338885, "duration": 19851}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399338885, "stop": 1754399339184, "duration": 299}, "status": "passed", "steps": [], "attachments": [{"uid": "7d80491ae3d495b4", "name": "测试总结", "source": "7d80491ae3d495b4.txt", "type": "text/plain", "size": 253}, {"uid": "93cd28211df0218e", "name": "test_completed", "source": "93cd28211df0218e.png", "type": "image/png", "size": 513758}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399339185, "stop": 1754399339195, "duration": 10}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399339195, "stop": 1754399339458, "duration": 263}, "status": "passed", "steps": [], "attachments": [{"uid": "2e0e3a2daeaba3a2", "name": "测试总结", "source": "2e0e3a2daeaba3a2.txt", "type": "text/plain", "size": 253}, {"uid": "20d6fcc91a07eb4f", "name": "test_completed", "source": "20d6fcc91a07eb4f.png", "type": "image/png", "size": 514615}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "264bef902ca8f61", "name": "stdout", "source": "264bef902ca8f61.txt", "type": "text/plain", "size": 11806}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399339461, "stop": 1754399339461, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399339463, "stop": 1754399340719, "duration": 1256}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_whats_the_weather_today"}, {"name": "subSuite", "value": "TestEllaWhatsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_whats_the_weather_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "523730bcb360743f.json", "parameterValues": []}