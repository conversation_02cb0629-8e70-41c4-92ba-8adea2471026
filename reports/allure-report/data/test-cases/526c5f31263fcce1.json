{"uid": "526c5f31263fcce1", "name": "测试i want to watch fireworks能正常执行", "fullName": "testcases.test_ella.dialogue.test_i_want_to_watch_fireworks.TestEllaIWantWatchFireworks#test_i_want_to_watch_fireworks", "historyId": "4ae696581fe41611547bc10ddba4f526", "time": {"start": 1754398504513, "stop": 1754398519081, "duration": 14568}, "description": "i want to watch fireworks", "descriptionHtml": "<p>i want to watch fireworks</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398491394, "stop": 1754398504512, "duration": 13118}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398504513, "stop": 1754398504513, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "i want to watch fireworks", "status": "passed", "steps": [{"name": "执行命令: i want to watch fireworks", "time": {"start": 1754398504513, "stop": 1754398518766, "duration": 14253}, "status": "passed", "steps": [{"name": "执行命令: i want to watch fireworks", "time": {"start": 1754398504513, "stop": 1754398518461, "duration": 13948}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398518461, "stop": 1754398518765, "duration": 304}, "status": "passed", "steps": [], "attachments": [{"uid": "4637ae4a7df63068", "name": "测试总结", "source": "4637ae4a7df63068.txt", "type": "text/plain", "size": 202}, {"uid": "26732da9cfa057fd", "name": "test_completed", "source": "26732da9cfa057fd.png", "type": "image/png", "size": 585238}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398518767, "stop": 1754398518773, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398518773, "stop": 1754398519081, "duration": 308}, "status": "passed", "steps": [], "attachments": [{"uid": "f408c5f34554d9ec", "name": "测试总结", "source": "f408c5f34554d9ec.txt", "type": "text/plain", "size": 202}, {"uid": "64a0c3bfedd6ea5f", "name": "test_completed", "source": "64a0c3bfedd6ea5f.png", "type": "image/png", "size": 583633}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "a581c2863729fb60", "name": "stdout", "source": "a581c2863729fb60.txt", "type": "text/plain", "size": 11530}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398519083, "stop": 1754398519083, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398519086, "stop": 1754398520339, "duration": 1253}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_i_want_to_watch_fireworks"}, {"name": "subSuite", "value": "TestEllaIWantWatchFireworks"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_i_want_to_watch_fireworks"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "526c5f31263fcce1.json", "parameterValues": []}