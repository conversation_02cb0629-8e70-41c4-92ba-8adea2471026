{"uid": "53b8bc8221a23244", "name": "测试what time is it now能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_time_is_it_now.TestEllaWhatTimeIsItNow#test_what_time_is_it_now", "historyId": "519d11d818a361bc75d5af94c6a68b28", "time": {"start": 1754449100344, "stop": 1754449114223, "duration": 13879}, "description": "what time is it now", "descriptionHtml": "<p>what time is it now</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449087901, "stop": 1754449100343, "duration": 12442}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449100343, "stop": 1754449100343, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "what time is it now", "status": "passed", "steps": [{"name": "执行命令: what time is it now", "time": {"start": 1754449100344, "stop": 1754449114050, "duration": 13706}, "status": "passed", "steps": [{"name": "执行命令: what time is it now", "time": {"start": 1754449100344, "stop": 1754449113853, "duration": 13509}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449113853, "stop": 1754449114050, "duration": 197}, "status": "passed", "steps": [], "attachments": [{"uid": "e66f614ab76e06eb", "name": "测试总结", "source": "e66f614ab76e06eb.txt", "type": "text/plain", "size": 175}, {"uid": "64cbca07909146ac", "name": "test_completed", "source": "64cbca07909146ac.png", "type": "image/png", "size": 607733}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754449114050, "stop": 1754449114051, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449114051, "stop": 1754449114223, "duration": 172}, "status": "passed", "steps": [], "attachments": [{"uid": "5729fb38c5276769", "name": "测试总结", "source": "5729fb38c5276769.txt", "type": "text/plain", "size": 175}, {"uid": "38d21cc9f897411", "name": "test_completed", "source": "38d21cc9f897411.png", "type": "image/png", "size": 607990}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "f16fe07db5fc9ccb", "name": "stdout", "source": "f16fe07db5fc9ccb.txt", "type": "text/plain", "size": 11139}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449114224, "stop": 1754449114224, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449114225, "stop": 1754449115654, "duration": 1429}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_time_is_it_now"}, {"name": "subSuite", "value": "TestEllaWhatTimeIsItNow"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_time_is_it_now"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "ef22d8f0a498be92", "status": "passed", "time": {"start": 1754399291821, "stop": 1754399304794, "duration": 12973}}], "categories": [], "tags": ["smoke"]}, "source": "53b8bc8221a23244.json", "parameterValues": []}