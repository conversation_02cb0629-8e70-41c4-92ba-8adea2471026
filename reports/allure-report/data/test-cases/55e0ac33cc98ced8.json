{"uid": "55e0ac33cc98ced8", "name": "测试check my balance of sim1返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_check_my_balance_of_sim.TestEllaCheckMyBalanceSim#test_check_my_balance_of_sim", "historyId": "8bcc4c0c2b314e79a7177168f7d787b8", "time": {"start": 1754402009861, "stop": 1754402022784, "duration": 12923}, "description": "验证check my balance of sim1指令返回预期的不支持响应", "descriptionHtml": "<p>验证check my balance of sim1指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754401996892, "stop": 1754402009861, "duration": 12969}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754402009861, "stop": 1754402009861, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证check my balance of sim1指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: check my balance of sim1", "time": {"start": 1754402009861, "stop": 1754402022477, "duration": 12616}, "status": "passed", "steps": [{"name": "执行命令: check my balance of sim1", "time": {"start": 1754402009861, "stop": 1754402022195, "duration": 12334}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754402022195, "stop": 1754402022475, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "44cc01b5c5a956e9", "name": "测试总结", "source": "44cc01b5c5a956e9.txt", "type": "text/plain", "size": 209}, {"uid": "9265b8bde2cdbbfa", "name": "test_completed", "source": "9265b8bde2cdbbfa.png", "type": "image/png", "size": 574114}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754402022477, "stop": 1754402022485, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754402022485, "stop": 1754402022783, "duration": 298}, "status": "passed", "steps": [], "attachments": [{"uid": "1523c266db33f6c1", "name": "测试总结", "source": "1523c266db33f6c1.txt", "type": "text/plain", "size": 209}, {"uid": "4d3aedfb386dd9c5", "name": "test_completed", "source": "4d3aedfb386dd9c5.png", "type": "image/png", "size": 574390}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "bd8e0a50aac566d", "name": "stdout", "source": "bd8e0a50aac566d.txt", "type": "text/plain", "size": 11292}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754402022786, "stop": 1754402022787, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754402022788, "stop": 1754402024107, "duration": 1319}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_my_balance_of_sim"}, {"name": "subSuite", "value": "TestEllaCheckMyBalanceSim"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_my_balance_of_sim"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "55e0ac33cc98ced8.json", "parameterValues": []}