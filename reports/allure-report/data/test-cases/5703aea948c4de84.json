{"uid": "5703aea948c4de84", "name": "测试search the address in the image能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_search_the_address_in_the_image.TestEllaSearchAddressImage#test_search_the_address_in_the_image", "historyId": "c50847e2010bac3c5a9bb7ff0b690fb6", "time": {"start": 1754404070890, "stop": 1754404086053, "duration": 15163}, "description": "search the address in the image", "descriptionHtml": "<p>search the address in the image</p>\n", "status": "failed", "statusMessage": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['search the address in the image', '', '', '', '对话 发现 8 篇参考资料 以下是一些可以搜索图片中地址的方法：&#10;&#10;利用手机自带功能 ：在部分手机中，如果收到的图片是原图，将其打开后往上推，下方可能会出现拍摄地址等信息 。比如在微信中收到好友发送的原图照片，可先点击“查看原图”再下载，然后在图库中打开该照片查看拍摄地址3。&#10;使用特定软件 ：&#10;百度识图 ：可通过上传图片，利用其图像识别技术，根据图中的地标、建筑物等特征来尝试确定位置4。&#10;每日水印相机 ：能自动添加包括地点水印等各种水印，若图片中有相关水印可直接获取地址信息；若没有，也可通过其定位功能获取拍摄时的地址6 。&#10;打卡定位水印相机 ：主打定位拍照，会自动添加当前所处位置的水印，有助于搜索图片拍摄时的地址6。&#10;借助在线工具 ：&#10;http://metapicz.com ：将照片添加到该网站，可自动分析出很多内容，可能包括 DeepSeek-R1 有问题尽管问我… 10:28']'\nassert None", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_search_the_address_in_the_image.TestEllaSearchAddressImage object at 0x00000240FF31AD50>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000024082F7B250>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_search_the_address_in_the_image(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     expected_text = self.expected_text\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证GoogleMap应用已打开\"):\n>           assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['search the address in the image', '', '', '', '对话 发现 8 篇参考资料 以下是一些可以搜索图片中地址的方法：&#10;&#10;利用手机自带功能 ：在部分手机中，如果收到的图片是原图，将其打开后往上推，下方可能会出现拍摄地址等信息 。比如在微信中收到好友发送的原图照片，可先点击“查看原图”再下载，然后在图库中打开该照片查看拍摄地址3。&#10;使用特定软件 ：&#10;百度识图 ：可通过上传图片，利用其图像识别技术，根据图中的地标、建筑物等特征来尝试确定位置4。&#10;每日水印相机 ：能自动添加包括地点水印等各种水印，若图片中有相关水印可直接获取地址信息；若没有，也可通过其定位功能获取拍摄时的地址6 。&#10;打卡定位水印相机 ：主打定位拍照，会自动添加当前所处位置的水印，有助于搜索图片拍摄时的地址6。&#10;借助在线工具 ：&#10;http://metapicz.com ：将照片添加到该网站，可自动分析出很多内容，可能包括 DeepSeek-R1 有问题尽管问我… 10:28']'\nE           assert None\n\ntestcases\\test_ella\\unsupported_commands\\test_search_the_address_in_the_image.py:36: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754404057903, "stop": 1754404070888, "duration": 12985}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754404070888, "stop": 1754404070888, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "search the address in the image", "status": "failed", "statusMessage": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['search the address in the image', '', '', '', '对话 发现 8 篇参考资料 以下是一些可以搜索图片中地址的方法：&#10;&#10;利用手机自带功能 ：在部分手机中，如果收到的图片是原图，将其打开后往上推，下方可能会出现拍摄地址等信息 。比如在微信中收到好友发送的原图照片，可先点击“查看原图”再下载，然后在图库中打开该照片查看拍摄地址3。&#10;使用特定软件 ：&#10;百度识图 ：可通过上传图片，利用其图像识别技术，根据图中的地标、建筑物等特征来尝试确定位置4。&#10;每日水印相机 ：能自动添加包括地点水印等各种水印，若图片中有相关水印可直接获取地址信息；若没有，也可通过其定位功能获取拍摄时的地址6 。&#10;打卡定位水印相机 ：主打定位拍照，会自动添加当前所处位置的水印，有助于搜索图片拍摄时的地址6。&#10;借助在线工具 ：&#10;http://metapicz.com ：将照片添加到该网站，可自动分析出很多内容，可能包括 DeepSeek-R1 有问题尽管问我… 10:28']'\nassert None", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_search_the_address_in_the_image.TestEllaSearchAddressImage object at 0x00000240FF31AD50>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000024082F7B250>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_search_the_address_in_the_image(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     expected_text = self.expected_text\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证GoogleMap应用已打开\"):\n>           assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['search the address in the image', '', '', '', '对话 发现 8 篇参考资料 以下是一些可以搜索图片中地址的方法：&#10;&#10;利用手机自带功能 ：在部分手机中，如果收到的图片是原图，将其打开后往上推，下方可能会出现拍摄地址等信息 。比如在微信中收到好友发送的原图照片，可先点击“查看原图”再下载，然后在图库中打开该照片查看拍摄地址3。&#10;使用特定软件 ：&#10;百度识图 ：可通过上传图片，利用其图像识别技术，根据图中的地标、建筑物等特征来尝试确定位置4。&#10;每日水印相机 ：能自动添加包括地点水印等各种水印，若图片中有相关水印可直接获取地址信息；若没有，也可通过其定位功能获取拍摄时的地址6 。&#10;打卡定位水印相机 ：主打定位拍照，会自动添加当前所处位置的水印，有助于搜索图片拍摄时的地址6。&#10;借助在线工具 ：&#10;http://metapicz.com ：将照片添加到该网站，可自动分析出很多内容，可能包括 DeepSeek-R1 有问题尽管问我… 10:28']'\nE           assert None\n\ntestcases\\test_ella\\unsupported_commands\\test_search_the_address_in_the_image.py:36: AssertionError", "steps": [{"name": "执行命令: search the address in the image", "time": {"start": 1754404070890, "stop": 1754404086051, "duration": 15161}, "status": "passed", "steps": [{"name": "执行命令: search the address in the image", "time": {"start": 1754404070890, "stop": 1754404085771, "duration": 14881}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754404085771, "stop": 1754404086049, "duration": 278}, "status": "passed", "steps": [], "attachments": [{"uid": "c62730dd0a1ed883", "name": "测试总结", "source": "c62730dd0a1ed883.txt", "type": "text/plain", "size": 1277}, {"uid": "d60d8e0f2de5f32a", "name": "test_completed", "source": "d60d8e0f2de5f32a.png", "type": "image/png", "size": 874965}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证GoogleMap应用已打开", "time": {"start": 1754404086051, "stop": 1754404086051, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['search the address in the image', '', '', '', '对话 发现 8 篇参考资料 以下是一些可以搜索图片中地址的方法：&#10;&#10;利用手机自带功能 ：在部分手机中，如果收到的图片是原图，将其打开后往上推，下方可能会出现拍摄地址等信息 。比如在微信中收到好友发送的原图照片，可先点击“查看原图”再下载，然后在图库中打开该照片查看拍摄地址3。&#10;使用特定软件 ：&#10;百度识图 ：可通过上传图片，利用其图像识别技术，根据图中的地标、建筑物等特征来尝试确定位置4。&#10;每日水印相机 ：能自动添加包括地点水印等各种水印，若图片中有相关水印可直接获取地址信息；若没有，也可通过其定位功能获取拍摄时的地址6 。&#10;打卡定位水印相机 ：主打定位拍照，会自动添加当前所处位置的水印，有助于搜索图片拍摄时的地址6。&#10;借助在线工具 ：&#10;http://metapicz.com ：将照片添加到该网站，可自动分析出很多内容，可能包括 DeepSeek-R1 有问题尽管问我… 10:28']'\nassert None\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_search_the_address_in_the_image.py\", line 36, in test_search_the_address_in_the_image\n    assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "524266ab6f67c85d", "name": "stdout", "source": "524266ab6f67c85d.txt", "type": "text/plain", "size": 15063}], "parameters": [], "attachmentStep": false, "stepsCount": 4, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754404086066, "stop": 1754404086377, "duration": 311}, "status": "passed", "steps": [], "attachments": [{"uid": "8d6d607cda2b36cc", "name": "失败截图-TestEllaSearchAddressImage", "source": "8d6d607cda2b36cc.png", "type": "image/png", "size": 874050}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754404086379, "stop": 1754404087628, "duration": 1249}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_search_the_address_in_the_image"}, {"name": "subSuite", "value": "TestEllaSearchAddressImage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_search_the_address_in_the_image"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "5703aea948c4de84.json", "parameterValues": []}