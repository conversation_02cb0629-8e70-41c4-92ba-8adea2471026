{"uid": "575d4153a349b439", "name": "测试set font size返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_font_size.TestEllaSetFontSize#test_set_font_size", "historyId": "6c315a350a546e1382e435255d28245b", "time": {"start": 1754454579451, "stop": 1754454593442, "duration": 13991}, "description": "验证set font size指令返回预期的不支持响应", "descriptionHtml": "<p>验证set font size指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454566960, "stop": 1754454579450, "duration": 12490}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454579450, "stop": 1754454579450, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set font size指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set font size", "time": {"start": 1754454579451, "stop": 1754454593267, "duration": 13816}, "status": "passed", "steps": [{"name": "执行命令: set font size", "time": {"start": 1754454579451, "stop": 1754454593093, "duration": 13642}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454593093, "stop": 1754454593266, "duration": 173}, "status": "passed", "steps": [], "attachments": [{"uid": "7599ae365ca1c2b0", "name": "测试总结", "source": "7599ae365ca1c2b0.txt", "type": "text/plain", "size": 209}, {"uid": "ca6dac0b2bdb1a8d", "name": "test_completed", "source": "ca6dac0b2bdb1a8d.png", "type": "image/png", "size": 469165}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454593267, "stop": 1754454593267, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454593267, "stop": 1754454593442, "duration": 175}, "status": "passed", "steps": [], "attachments": [{"uid": "34ae522294d965ae", "name": "测试总结", "source": "34ae522294d965ae.txt", "type": "text/plain", "size": 209}, {"uid": "1e4f1c8d302c9b77", "name": "test_completed", "source": "1e4f1c8d302c9b77.png", "type": "image/png", "size": 469236}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "95c64f466b4e89d8", "name": "stdout", "source": "95c64f466b4e89d8.txt", "type": "text/plain", "size": 11214}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454593443, "stop": 1754454593443, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454593444, "stop": 1754454594802, "duration": 1358}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_font_size"}, {"name": "subSuite", "value": "TestEllaSetFontSize"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_font_size"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "c32be5965af03bcf", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404579366, "stop": 1754404594795, "duration": 15429}}], "categories": [], "tags": ["smoke"]}, "source": "575d4153a349b439.json", "parameterValues": []}