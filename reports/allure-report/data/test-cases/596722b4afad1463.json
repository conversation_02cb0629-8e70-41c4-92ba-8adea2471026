{"uid": "596722b4afad1463", "name": "测试hello hello能正常执行", "fullName": "testcases.test_ella.dialogue.test_hello_hello.TestEllaHelloHello#test_hello_hello", "historyId": "b3fa1d22b59def5f059cd9b0eefbe2b0", "time": {"start": 1754398173047, "stop": 1754398186506, "duration": 13459}, "description": "hello hello", "descriptionHtml": "<p>hello hello</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398160043, "stop": 1754398173046, "duration": 13003}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398173046, "stop": 1754398173046, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "hello hello", "status": "passed", "steps": [{"name": "执行命令: hello hello", "time": {"start": 1754398173047, "stop": 1754398186193, "duration": 13146}, "status": "passed", "steps": [{"name": "执行命令: hello hello", "time": {"start": 1754398173047, "stop": 1754398185947, "duration": 12900}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398185947, "stop": 1754398186192, "duration": 245}, "status": "passed", "steps": [], "attachments": [{"uid": "2a351d8b3eafae34", "name": "测试总结", "source": "2a351d8b3eafae34.txt", "type": "text/plain", "size": 586}, {"uid": "39097bc6a4ea4b87", "name": "test_completed", "source": "39097bc6a4ea4b87.png", "type": "image/png", "size": 539716}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398186193, "stop": 1754398186197, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398186197, "stop": 1754398186505, "duration": 308}, "status": "passed", "steps": [], "attachments": [{"uid": "2a46c90e1f68f314", "name": "测试总结", "source": "2a46c90e1f68f314.txt", "type": "text/plain", "size": 586}, {"uid": "5fb0b3c6050e2a72", "name": "test_completed", "source": "5fb0b3c6050e2a72.png", "type": "image/png", "size": 539363}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "c49c4816f8db0106", "name": "stdout", "source": "c49c4816f8db0106.txt", "type": "text/plain", "size": 13561}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398186508, "stop": 1754398186508, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398186510, "stop": 1754398187761, "duration": 1251}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_hello_hello"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_hello_hello"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "596722b4afad1463.json", "parameterValues": []}