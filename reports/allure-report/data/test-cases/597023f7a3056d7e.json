{"uid": "597023f7a3056d7e", "name": "测试introduce yourself能正常执行", "fullName": "testcases.test_ella.dialogue.test_introduce_yourself.TestEllaIntroduceYourself#test_introduce_yourself", "historyId": "a19924fb0a564cf26596907610c0f678", "time": {"start": 1754398533070, "stop": 1754398546898, "duration": 13828}, "description": "introduce yourself", "descriptionHtml": "<p>introduce yourself</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398520348, "stop": 1754398533068, "duration": 12720}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398533068, "stop": 1754398533068, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "introduce yourself", "status": "passed", "steps": [{"name": "执行命令: introduce yourself", "time": {"start": 1754398533070, "stop": 1754398546630, "duration": 13560}, "status": "passed", "steps": [{"name": "执行命令: introduce yourself", "time": {"start": 1754398533070, "stop": 1754398546338, "duration": 13268}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398546338, "stop": 1754398546629, "duration": 291}, "status": "passed", "steps": [], "attachments": [{"uid": "cac5a78b8affd5d4", "name": "测试总结", "source": "cac5a78b8affd5d4.txt", "type": "text/plain", "size": 538}, {"uid": "4b7abcac56367048", "name": "test_completed", "source": "4b7abcac56367048.png", "type": "image/png", "size": 557629}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398546630, "stop": 1754398546636, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398546636, "stop": 1754398546898, "duration": 262}, "status": "passed", "steps": [], "attachments": [{"uid": "7319c241a6bf578f", "name": "测试总结", "source": "7319c241a6bf578f.txt", "type": "text/plain", "size": 538}, {"uid": "2e4e9020d95a71ec", "name": "test_completed", "source": "2e4e9020d95a71ec.png", "type": "image/png", "size": 557051}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "9f6ab88a682d659e", "name": "stdout", "source": "9f6ab88a682d659e.txt", "type": "text/plain", "size": 13528}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398546901, "stop": 1754398546901, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398546902, "stop": 1754398548200, "duration": 1298}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_introduce_yourself"}, {"name": "subSuite", "value": "TestEllaIntroduceYourself"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_introduce_yourself"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "597023f7a3056d7e.json", "parameterValues": []}