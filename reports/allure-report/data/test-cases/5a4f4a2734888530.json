{"uid": "5a4f4a2734888530", "name": "测试navigation to the lucky能正常执行", "fullName": "testcases.test_ella.third_coupling.test_navigation_to_the_lucky.TestEllaNavigationToTheLucky#test_navigation_to_the_lucky", "historyId": "75c56947a7b1061f7ec858fb20919b50", "time": {"start": 1754401677753, "stop": 1754401691699, "duration": 13946}, "description": "navigation to the lucky", "descriptionHtml": "<p>navigation to the lucky</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754401664878, "stop": 1754401677752, "duration": 12874}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754401677753, "stop": 1754401677753, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "navigation to the lucky", "status": "passed", "steps": [{"name": "执行命令: navigation to the lucky", "time": {"start": 1754401677753, "stop": 1754401691369, "duration": 13616}, "status": "passed", "steps": [{"name": "执行命令: navigation to the lucky", "time": {"start": 1754401677753, "stop": 1754401691106, "duration": 13353}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754401691106, "stop": 1754401691368, "duration": 262}, "status": "passed", "steps": [], "attachments": [{"uid": "86c0b3633d414c1d", "name": "测试总结", "source": "86c0b3633d414c1d.txt", "type": "text/plain", "size": 929}, {"uid": "eddcfb5d0b2c7b28", "name": "test_completed", "source": "eddcfb5d0b2c7b28.png", "type": "image/png", "size": 627608}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证应用已打开", "time": {"start": 1754401691369, "stop": 1754401691369, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754401691369, "stop": 1754401691698, "duration": 329}, "status": "passed", "steps": [], "attachments": [{"uid": "8b47963a3366f343", "name": "测试总结", "source": "8b47963a3366f343.txt", "type": "text/plain", "size": 929}, {"uid": "9c53395b02a81234", "name": "test_completed", "source": "9c53395b02a81234.png", "type": "image/png", "size": 627263}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "f7a11d62f0d6a4fe", "name": "stdout", "source": "f7a11d62f0d6a4fe.txt", "type": "text/plain", "size": 14921}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754401691701, "stop": 1754401691701, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754401691702, "stop": 1754401693008, "duration": 1306}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigation_to_the_lucky"}, {"name": "subSuite", "value": "TestEllaNavigationToTheLucky"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigation_to_the_lucky"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "5a4f4a2734888530.json", "parameterValues": []}