{"uid": "5a857d261f4a2ea1", "name": "测试i want to make a call能正常执行", "fullName": "testcases.test_ella.dialogue.test_i_want_to_make_a_call.TestEllaIWantMakeCall#test_i_want_to_make_a_call", "historyId": "f4da532f5d62abff197a05947efc027a", "time": {"start": 1754398468360, "stop": 1754398490137, "duration": 21777}, "description": "i want to make a call", "descriptionHtml": "<p>i want to make a call</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398455555, "stop": 1754398468358, "duration": 12803}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398468358, "stop": 1754398468358, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "i want to make a call", "status": "passed", "steps": [{"name": "执行命令: i want to make a call", "time": {"start": 1754398468360, "stop": 1754398489857, "duration": 21497}, "status": "passed", "steps": [{"name": "执行命令: i want to make a call", "time": {"start": 1754398468360, "stop": 1754398489634, "duration": 21274}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398489634, "stop": 1754398489856, "duration": 222}, "status": "passed", "steps": [], "attachments": [{"uid": "46c5540c19e320c0", "name": "测试总结", "source": "46c5540c19e320c0.txt", "type": "text/plain", "size": 193}, {"uid": "1e84c9a1166ee359", "name": "test_completed", "source": "1e84c9a1166ee359.png", "type": "image/png", "size": 525937}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398489857, "stop": 1754398489861, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398489861, "stop": 1754398490137, "duration": 276}, "status": "passed", "steps": [], "attachments": [{"uid": "b4d6fac46e5872e2", "name": "测试总结", "source": "b4d6fac46e5872e2.txt", "type": "text/plain", "size": 193}, {"uid": "c74fa080d20ca2bc", "name": "test_completed", "source": "c74fa080d20ca2bc.png", "type": "image/png", "size": 526143}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "7e52e6f6e4679d84", "name": "stdout", "source": "7e52e6f6e4679d84.txt", "type": "text/plain", "size": 11745}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398490138, "stop": 1754398490138, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398490140, "stop": 1754398491385, "duration": 1245}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_i_want_to_make_a_call"}, {"name": "subSuite", "value": "TestEllaIWantMakeCall"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_i_want_to_make_a_call"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "5a857d261f4a2ea1.json", "parameterValues": []}