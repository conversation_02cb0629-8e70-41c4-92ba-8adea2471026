{"uid": "5b34488539e56b9b", "name": "测试stop run能正常执行", "fullName": "testcases.test_ella.dialogue.test_stop_run.TestEllaStopRun#test_stop_run", "historyId": "30903d6e764eebda77a45c5af4464d00", "time": {"start": 1754398855952, "stop": 1754398868928, "duration": 12976}, "description": "stop run", "descriptionHtml": "<p>stop run</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398842962, "stop": 1754398855951, "duration": 12989}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398855951, "stop": 1754398855951, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "stop run", "status": "passed", "steps": [{"name": "执行命令: stop run", "time": {"start": 1754398855952, "stop": 1754398868620, "duration": 12668}, "status": "passed", "steps": [{"name": "执行命令: stop run", "time": {"start": 1754398855952, "stop": 1754398868362, "duration": 12410}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398868362, "stop": 1754398868619, "duration": 257}, "status": "passed", "steps": [], "attachments": [{"uid": "5b0aea14a4bc56d9", "name": "测试总结", "source": "5b0aea14a4bc56d9.txt", "type": "text/plain", "size": 173}, {"uid": "d98a711d173acbd6", "name": "test_completed", "source": "d98a711d173acbd6.png", "type": "image/png", "size": 537510}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398868620, "stop": 1754398868624, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398868624, "stop": 1754398868926, "duration": 302}, "status": "passed", "steps": [], "attachments": [{"uid": "45d18fb56c58d75b", "name": "测试总结", "source": "45d18fb56c58d75b.txt", "type": "text/plain", "size": 173}, {"uid": "5748e199543c39be", "name": "test_completed", "source": "5748e199543c39be.png", "type": "image/png", "size": 537560}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "ca4e5df2df553817", "name": "stdout", "source": "ca4e5df2df553817.txt", "type": "text/plain", "size": 11113}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398868930, "stop": 1754398868930, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398868932, "stop": 1754398870223, "duration": 1291}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_stop_run"}, {"name": "subSuite", "value": "TestEllaStopRun"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_stop_run"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "5b34488539e56b9b.json", "parameterValues": []}