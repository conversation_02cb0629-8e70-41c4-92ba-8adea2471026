{"uid": "5c0db2c76d746ab7", "name": "测试what·s the weather today？能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_s_the_weather_today.TestEllaWhatSWeatherToday#test_what_s_the_weather_today", "historyId": "8d12bedb52d3f000f4269afc25f3fe30", "time": {"start": 1754399203309, "stop": 1754399223188, "duration": 19879}, "description": "what·s the weather today？", "descriptionHtml": "<p>what·s the weather today？</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399190630, "stop": 1754399203308, "duration": 12678}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399203308, "stop": 1754399203308, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "what·s the weather today？", "status": "passed", "steps": [{"name": "执行命令: what·s the weather today？", "time": {"start": 1754399203309, "stop": 1754399222920, "duration": 19611}, "status": "passed", "steps": [{"name": "执行命令: what·s the weather today？", "time": {"start": 1754399203309, "stop": 1754399222686, "duration": 19377}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399222686, "stop": 1754399222919, "duration": 233}, "status": "passed", "steps": [], "attachments": [{"uid": "ab92f414324bc547", "name": "测试总结", "source": "ab92f414324bc547.txt", "type": "text/plain", "size": 263}, {"uid": "55fe54f2319774", "name": "test_completed", "source": "55fe54f2319774.png", "type": "image/png", "size": 503029}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399222920, "stop": 1754399222928, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399222928, "stop": 1754399223187, "duration": 259}, "status": "passed", "steps": [], "attachments": [{"uid": "e995f7894b48481b", "name": "测试总结", "source": "e995f7894b48481b.txt", "type": "text/plain", "size": 263}, {"uid": "a61d3bb485d91c62", "name": "test_completed", "source": "a61d3bb485d91c62.png", "type": "image/png", "size": 503029}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "e5c5e3cf74876284", "name": "stdout", "source": "e5c5e3cf74876284.txt", "type": "text/plain", "size": 12402}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399223189, "stop": 1754399223189, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399223192, "stop": 1754399224448, "duration": 1256}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_s_the_weather_today"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_s_the_weather_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "5c0db2c76d746ab7.json", "parameterValues": []}