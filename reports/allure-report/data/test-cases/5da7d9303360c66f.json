{"uid": "5da7d9303360c66f", "name": "测试how to say hello in french能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_to_say_hello_in_french.TestEllaHowSayHelloFrench#test_how_to_say_hello_in_french", "historyId": "cc44ad4097a589726631a345e0cd01ad", "time": {"start": 1754398386919, "stop": 1754398398598, "duration": 11679}, "description": "how to say hello in french", "descriptionHtml": "<p>how to say hello in french</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398373787, "stop": 1754398386918, "duration": 13131}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398386918, "stop": 1754398386918, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "how to say hello in french", "status": "passed", "steps": [{"name": "执行命令: how to say hello in french", "time": {"start": 1754398386919, "stop": 1754398398325, "duration": 11406}, "status": "passed", "steps": [{"name": "执行命令: how to say hello in french", "time": {"start": 1754398386919, "stop": 1754398398087, "duration": 11168}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398398087, "stop": 1754398398323, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "df4d854468db6e2d", "name": "测试总结", "source": "df4d854468db6e2d.txt", "type": "text/plain", "size": 178}, {"uid": "fdffac64192a3b29", "name": "test_completed", "source": "fdffac64192a3b29.png", "type": "image/png", "size": 430051}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398398325, "stop": 1754398398332, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398398332, "stop": 1754398398597, "duration": 265}, "status": "passed", "steps": [], "attachments": [{"uid": "510818f076d1b446", "name": "测试总结", "source": "510818f076d1b446.txt", "type": "text/plain", "size": 178}, {"uid": "fafacbdffe0a138d", "name": "test_completed", "source": "fafacbdffe0a138d.png", "type": "image/png", "size": 430051}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "cd41d6b49fe33de4", "name": "stdout", "source": "cd41d6b49fe33de4.txt", "type": "text/plain", "size": 11265}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398398599, "stop": 1754398398599, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398398601, "stop": 1754398399871, "duration": 1270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_to_say_hello_in_french"}, {"name": "subSuite", "value": "TestEllaHowSayHelloFrench"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_to_say_hello_in_french"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "5da7d9303360c66f.json", "parameterValues": []}