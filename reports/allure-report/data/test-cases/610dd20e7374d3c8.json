{"uid": "610dd20e7374d3c8", "name": "测试why is my phone not ringing on incoming calls能正常执行", "fullName": "testcases.test_ella.dialogue.test_why_is_my_phone_not_ringing_on_incoming_calls.TestEllaWhyIsMyPhoneNotRingingIncomingCalls#test_why_is_my_phone_not_ringing_on_incoming_calls", "historyId": "cf0ebfd1b4e2ab43e2f516ad6a1a6917", "time": {"start": 1754399424224, "stop": 1754399446307, "duration": 22083}, "description": "why is my phone not ringing on incoming calls", "descriptionHtml": "<p>why is my phone not ringing on incoming calls</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399411188, "stop": 1754399424223, "duration": 13035}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399424223, "stop": 1754399424223, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "why is my phone not ringing on incoming calls", "status": "passed", "steps": [{"name": "执行命令: why is my phone not ringing on incoming calls", "time": {"start": 1754399424224, "stop": 1754399446058, "duration": 21834}, "status": "passed", "steps": [{"name": "执行命令: why is my phone not ringing on incoming calls", "time": {"start": 1754399424224, "stop": 1754399445822, "duration": 21598}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399445822, "stop": 1754399446056, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "ddbef81b6f9e1bc8", "name": "测试总结", "source": "ddbef81b6f9e1bc8.txt", "type": "text/plain", "size": 686}, {"uid": "f30884dd698c28c7", "name": "test_completed", "source": "f30884dd698c28c7.png", "type": "image/png", "size": 622349}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399446058, "stop": 1754399446060, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399446060, "stop": 1754399446307, "duration": 247}, "status": "passed", "steps": [], "attachments": [{"uid": "56290d0ce77a68eb", "name": "测试总结", "source": "56290d0ce77a68eb.txt", "type": "text/plain", "size": 686}, {"uid": "3471b112d0a87bd4", "name": "test_completed", "source": "3471b112d0a87bd4.png", "type": "image/png", "size": 621578}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "ea03f68dcaa9141c", "name": "stdout", "source": "ea03f68dcaa9141c.txt", "type": "text/plain", "size": 13246}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399446308, "stop": 1754399446309, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399446311, "stop": 1754399447593, "duration": 1282}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_why_is_my_phone_not_ringing_on_incoming_calls"}, {"name": "subSuite", "value": "TestEllaWhyIsMyPhoneNotRingingIncomingCalls"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_why_is_my_phone_not_ringing_on_incoming_calls"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "610dd20e7374d3c8.json", "parameterValues": []}