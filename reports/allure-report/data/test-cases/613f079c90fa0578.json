{"uid": "613f079c90fa0578", "name": "测试set timezone返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_timezone.TestEllaSetTimezone#test_set_timezone", "historyId": "dbd7a7f96e1740fa05f50ed6fa7becfb", "time": {"start": 1754455130094, "stop": 1754455144079, "duration": 13985}, "description": "验证set timezone指令返回预期的不支持响应", "descriptionHtml": "<p>验证set timezone指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455118140, "stop": 1754455130093, "duration": 11953}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455130093, "stop": 1754455130093, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set timezone指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set timezone", "time": {"start": 1754455130094, "stop": 1754455143906, "duration": 13812}, "status": "passed", "steps": [{"name": "执行命令: set timezone", "time": {"start": 1754455130094, "stop": 1754455143710, "duration": 13616}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455143710, "stop": 1754455143906, "duration": 196}, "status": "passed", "steps": [], "attachments": [{"uid": "1e81a90f20b2ce79", "name": "测试总结", "source": "1e81a90f20b2ce79.txt", "type": "text/plain", "size": 207}, {"uid": "242f086ec01b43f4", "name": "test_completed", "source": "242f086ec01b43f4.png", "type": "image/png", "size": 476685}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455143906, "stop": 1754455143907, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455143908, "stop": 1754455144079, "duration": 171}, "status": "passed", "steps": [], "attachments": [{"uid": "d9aa80161030598", "name": "测试总结", "source": "d9aa80161030598.txt", "type": "text/plain", "size": 207}, {"uid": "aefe6709ab7db713", "name": "test_completed", "source": "aefe6709ab7db713.png", "type": "image/png", "size": 477016}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "79f59c070ede7bab", "name": "stdout", "source": "79f59c070ede7bab.txt", "type": "text/plain", "size": 11205}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754455144080, "stop": 1754455145358, "duration": 1278}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754455144080, "stop": 1754455144080, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_timezone"}, {"name": "subSuite", "value": "TestEllaSetTimezone"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_timezone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "9b33d0eb300d4f90", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405158782, "stop": 1754405175744, "duration": 16962}}], "categories": [], "tags": ["smoke"]}, "source": "613f079c90fa0578.json", "parameterValues": []}