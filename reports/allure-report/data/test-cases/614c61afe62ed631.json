{"uid": "614c61afe62ed631", "name": "测试set my themes返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_my_themes.TestEllaSetMyThemes#test_set_my_themes", "historyId": "084048a337d3081654dc4414f67fce70", "time": {"start": 1754454718943, "stop": 1754454733021, "duration": 14078}, "description": "验证set my themes指令返回预期的不支持响应", "descriptionHtml": "<p>验证set my themes指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454707174, "stop": 1754454718942, "duration": 11768}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454718942, "stop": 1754454718942, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set my themes指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set my themes", "time": {"start": 1754454718943, "stop": 1754454732817, "duration": 13874}, "status": "passed", "steps": [{"name": "执行命令: set my themes", "time": {"start": 1754454718943, "stop": 1754454732617, "duration": 13674}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454732617, "stop": 1754454732817, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "a50a276e0355e75c", "name": "测试总结", "source": "a50a276e0355e75c.txt", "type": "text/plain", "size": 228}, {"uid": "8b96c462928510cb", "name": "test_completed", "source": "8b96c462928510cb.png", "type": "image/png", "size": 489002}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454732817, "stop": 1754454732818, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454732818, "stop": 1754454733020, "duration": 202}, "status": "passed", "steps": [], "attachments": [{"uid": "a4fad60778f14d37", "name": "测试总结", "source": "a4fad60778f14d37.txt", "type": "text/plain", "size": 228}, {"uid": "f0b116158203fca2", "name": "test_completed", "source": "f0b116158203fca2.png", "type": "image/png", "size": 488884}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "895d2c589ee3ab3a", "name": "stdout", "source": "895d2c589ee3ab3a.txt", "type": "text/plain", "size": 11271}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454733021, "stop": 1754454733021, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454733022, "stop": 1754454734430, "duration": 1408}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_my_themes"}, {"name": "subSuite", "value": "TestEllaSetMyThemes"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_my_themes"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "5cca1f6181dc473b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404729688, "stop": 1754404743445, "duration": 13757}}], "categories": [], "tags": ["smoke"]}, "source": "614c61afe62ed631.json", "parameterValues": []}