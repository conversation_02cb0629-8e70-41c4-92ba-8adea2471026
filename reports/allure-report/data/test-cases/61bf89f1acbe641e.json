{"uid": "61bf89f1acbe641e", "name": "测试set flex-still mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_flex_still_mode.TestEllaSetFlexStillMode#test_set_flex_still_mode", "historyId": "969451307307a13b4d89a24bb46ad0bb", "time": {"start": 1754454468583, "stop": 1754454482618, "duration": 14035}, "description": "验证set flex-still mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证set flex-still mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454456236, "stop": 1754454468582, "duration": 12346}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454468582, "stop": 1754454468582, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set flex-still mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set flex-still mode", "time": {"start": 1754454468583, "stop": 1754454482418, "duration": 13835}, "status": "passed", "steps": [{"name": "执行命令: set flex-still mode", "time": {"start": 1754454468583, "stop": 1754454482225, "duration": 13642}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454482225, "stop": 1754454482418, "duration": 193}, "status": "passed", "steps": [], "attachments": [{"uid": "63d4f7dee38a2416", "name": "测试总结", "source": "63d4f7dee38a2416.txt", "type": "text/plain", "size": 227}, {"uid": "598ff90c296f515b", "name": "test_completed", "source": "598ff90c296f515b.png", "type": "image/png", "size": 490505}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454482418, "stop": 1754454482420, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454482420, "stop": 1754454482618, "duration": 198}, "status": "passed", "steps": [], "attachments": [{"uid": "7cea3378798b10ed", "name": "测试总结", "source": "7cea3378798b10ed.txt", "type": "text/plain", "size": 227}, {"uid": "5373ab504e9ab005", "name": "test_completed", "source": "5373ab504e9ab005.png", "type": "image/png", "size": 490317}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "a500cad6011d19c9", "name": "stdout", "source": "a500cad6011d19c9.txt", "type": "text/plain", "size": 11296}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454482619, "stop": 1754454482619, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454482620, "stop": 1754454484011, "duration": 1391}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_flex_still_mode"}, {"name": "subSuite", "value": "TestEllaSetFlexStillMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_flex_still_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "ff51c37aaa3a0361", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404471464, "stop": 1754404482487, "duration": 11023}}], "categories": [], "tags": ["smoke"]}, "source": "61bf89f1acbe641e.json", "parameterValues": []}