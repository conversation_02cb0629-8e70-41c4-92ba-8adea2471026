{"uid": "62928b601638239b", "name": "测试set an alarm at 8 am", "fullName": "testcases.test_ella.component_coupling.test_set_an_alarm_at_8_am.TestEllaOpenClock#test_set_an_alarm_at_8_am", "historyId": "de0ed312f350c708e7a00bb74aeaac0f", "time": {"start": 1754447401605, "stop": 1754447416889, "duration": 15284}, "description": "测试set an alarm at 8 am指令", "descriptionHtml": "<p>测试set an alarm at 8 am指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447389099, "stop": 1754447401603, "duration": 12504}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447401603, "stop": 1754447401603, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试set an alarm at 8 am指令", "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "time": {"start": 1754447401605, "stop": 1754447416694, "duration": 15089}, "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "time": {"start": 1754447401605, "stop": 1754447416488, "duration": 14883}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447416488, "stop": 1754447416694, "duration": 206}, "status": "passed", "steps": [], "attachments": [{"uid": "dcb3b21d69e5fee1", "name": "测试总结", "source": "dcb3b21d69e5fee1.txt", "type": "text/plain", "size": 197}, {"uid": "18e8d877d9e52f7e", "name": "test_completed", "source": "18e8d877d9e52f7e.png", "type": "image/png", "size": 485950}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447416694, "stop": 1754447416695, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447416695, "stop": 1754447416889, "duration": 194}, "status": "passed", "steps": [], "attachments": [{"uid": "d2e8b7666423a78d", "name": "测试总结", "source": "d2e8b7666423a78d.txt", "type": "text/plain", "size": 197}, {"uid": "540ce0e7c72eb35b", "name": "test_completed", "source": "540ce0e7c72eb35b.png", "type": "image/png", "size": 485102}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "db8fe346a8f2b0f", "name": "stdout", "source": "db8fe346a8f2b0f.txt", "type": "text/plain", "size": 11960}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754447416890, "stop": 1754447418283, "duration": 1393}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754447416890, "stop": 1754447416890, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_set_an_alarm_at_8_am"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_set_an_alarm_at_8_am"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "ecd122ba3ea3ea0a", "status": "passed", "time": {"start": 1754397691760, "stop": 1754397706148, "duration": 14388}}], "categories": [], "tags": ["smoke"]}, "source": "62928b601638239b.json", "parameterValues": []}