{"uid": "62acaa3041ed52b0", "name": "测试how to say i love you in french能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_to_say_i_love_you_in_french.TestEllaHowSayILoveYouFrench#test_how_to_say_i_love_you_in_french", "historyId": "78de5607a6208f59723ba6cf4fcf09c4", "time": {"start": 1754398412864, "stop": 1754398425777, "duration": 12913}, "description": "how to say i love you in french", "descriptionHtml": "<p>how to say i love you in french</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398399878, "stop": 1754398412863, "duration": 12985}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398412863, "stop": 1754398412863, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "how to say i love you in french", "status": "passed", "steps": [{"name": "执行命令: how to say i love you in french", "time": {"start": 1754398412864, "stop": 1754398425496, "duration": 12632}, "status": "passed", "steps": [{"name": "执行命令: how to say i love you in french", "time": {"start": 1754398412864, "stop": 1754398425256, "duration": 12392}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398425256, "stop": 1754398425494, "duration": 238}, "status": "passed", "steps": [], "attachments": [{"uid": "c0a3e0c1df0c7f48", "name": "测试总结", "source": "c0a3e0c1df0c7f48.txt", "type": "text/plain", "size": 188}, {"uid": "3d1f280d1be6c5a0", "name": "test_completed", "source": "3d1f280d1be6c5a0.png", "type": "image/png", "size": 447898}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398425496, "stop": 1754398425499, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398425499, "stop": 1754398425776, "duration": 277}, "status": "passed", "steps": [], "attachments": [{"uid": "9e18c0c4c6493e89", "name": "测试总结", "source": "9e18c0c4c6493e89.txt", "type": "text/plain", "size": 188}, {"uid": "b4c3f22a89ecbc73", "name": "test_completed", "source": "b4c3f22a89ecbc73.png", "type": "image/png", "size": 448281}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "ba97410ff36c5f37", "name": "stdout", "source": "ba97410ff36c5f37.txt", "type": "text/plain", "size": 11316}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398425778, "stop": 1754398425778, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398425780, "stop": 1754398427055, "duration": 1275}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_to_say_i_love_you_in_french"}, {"name": "subSuite", "value": "TestEllaHowSayILoveYouFrench"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_to_say_i_love_you_in_french"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "62acaa3041ed52b0.json", "parameterValues": []}