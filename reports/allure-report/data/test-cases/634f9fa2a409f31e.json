{"uid": "634f9fa2a409f31e", "name": "测试set split-screen apps返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_split_screen_apps.TestEllaSetSplitScreenApps#test_set_split_screen_apps", "historyId": "b5e1711cce3102fc710ff74e18bf9129", "time": {"start": 1754455103017, "stop": 1754455116832, "duration": 13815}, "description": "验证set split-screen apps指令返回预期的不支持响应", "descriptionHtml": "<p>验证set split-screen apps指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455090998, "stop": 1754455103016, "duration": 12018}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455103016, "stop": 1754455103017, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set split-screen apps指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set split-screen apps", "time": {"start": 1754455103017, "stop": 1754455116650, "duration": 13633}, "status": "passed", "steps": [{"name": "执行命令: set split-screen apps", "time": {"start": 1754455103018, "stop": 1754455116464, "duration": 13446}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455116464, "stop": 1754455116650, "duration": 186}, "status": "passed", "steps": [], "attachments": [{"uid": "853e2c11a440ba81", "name": "测试总结", "source": "853e2c11a440ba81.txt", "type": "text/plain", "size": 233}, {"uid": "ea6e0613ef9e29cf", "name": "test_completed", "source": "ea6e0613ef9e29cf.png", "type": "image/png", "size": 492903}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455116650, "stop": 1754455116651, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455116651, "stop": 1754455116831, "duration": 180}, "status": "passed", "steps": [], "attachments": [{"uid": "3572d137f515c867", "name": "测试总结", "source": "3572d137f515c867.txt", "type": "text/plain", "size": 233}, {"uid": "6721622e58d1f8a9", "name": "test_completed", "source": "6721622e58d1f8a9.png", "type": "image/png", "size": 493163}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "e56061c6a30c9ffc", "name": "stdout", "source": "e56061c6a30c9ffc.txt", "type": "text/plain", "size": 11324}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455116832, "stop": 1754455116832, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455116833, "stop": 1754455118130, "duration": 1297}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_split_screen_apps"}, {"name": "subSuite", "value": "TestEllaSetSplitScreenApps"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_split_screen_apps"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "6a78bcdeb911f1dd", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405130127, "stop": 1754405143973, "duration": 13846}}], "categories": [], "tags": ["smoke"]}, "source": "634f9fa2a409f31e.json", "parameterValues": []}