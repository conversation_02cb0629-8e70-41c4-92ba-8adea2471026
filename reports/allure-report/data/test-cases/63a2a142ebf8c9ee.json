{"uid": "63a2a142ebf8c9ee", "name": "测试i wanna be rich能正常执行", "fullName": "testcases.test_ella.dialogue.test_i_wanna_be_rich.TestEllaIWannaBeRich#test_i_wanna_be_rich", "historyId": "6ecc7e0fc961d0d4e7e46672c033625a", "time": {"start": 1754448170003, "stop": 1754448186489, "duration": 16486}, "description": "i wanna be rich", "descriptionHtml": "<p>i wanna be rich</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448157361, "stop": 1754448170001, "duration": 12640}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448170002, "stop": 1754448170002, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "i wanna be rich", "status": "passed", "steps": [{"name": "执行命令: i wanna be rich", "time": {"start": 1754448170003, "stop": 1754448186284, "duration": 16281}, "status": "passed", "steps": [{"name": "执行命令: i wanna be rich", "time": {"start": 1754448170003, "stop": 1754448186066, "duration": 16063}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448186066, "stop": 1754448186283, "duration": 217}, "status": "passed", "steps": [], "attachments": [{"uid": "7ac79a9522f90221", "name": "测试总结", "source": "7ac79a9522f90221.txt", "type": "text/plain", "size": 179}, {"uid": "fef8c546bceb220b", "name": "test_completed", "source": "fef8c546bceb220b.png", "type": "image/png", "size": 587272}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448186284, "stop": 1754448186285, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448186285, "stop": 1754448186489, "duration": 204}, "status": "passed", "steps": [], "attachments": [{"uid": "76ecb8a193cbab81", "name": "测试总结", "source": "76ecb8a193cbab81.txt", "type": "text/plain", "size": 179}, {"uid": "fc81a2213e1d3359", "name": "test_completed", "source": "fc81a2213e1d3359.png", "type": "image/png", "size": 587153}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "46627699b3126827", "name": "stdout", "source": "46627699b3126827.txt", "type": "text/plain", "size": 11418}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448186489, "stop": 1754448186489, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448186490, "stop": 1754448187860, "duration": 1370}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_i_wanna_be_rich"}, {"name": "subSuite", "value": "TestEllaIWannaBeRich"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_i_wanna_be_rich"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "a497a4f10cf03219", "status": "passed", "time": {"start": 1754398439998, "stop": 1754398454283, "duration": 14285}}], "categories": [], "tags": ["smoke"]}, "source": "63a2a142ebf8c9ee.json", "parameterValues": []}