{"uid": "6443b855f11fc58", "name": "测试take a screenshot能正常执行", "fullName": "testcases.test_ella.component_coupling.test_take_a_screenshot.TestEllaTakeScreenshot#test_take_a_screenshot", "historyId": "6bdbaaabff6497c5d3be4727f1a7cd8d", "time": {"start": 1754447499429, "stop": 1754447514675, "duration": 15246}, "description": "take a screenshot", "descriptionHtml": "<p>take a screenshot</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447486769, "stop": 1754447499427, "duration": 12658}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447499427, "stop": 1754447499427, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "take a screenshot", "status": "passed", "steps": [{"name": "执行命令: take a screenshot", "time": {"start": 1754447499429, "stop": 1754447514497, "duration": 15068}, "status": "passed", "steps": [{"name": "执行命令: take a screenshot", "time": {"start": 1754447499429, "stop": 1754447514276, "duration": 14847}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447514276, "stop": 1754447514497, "duration": 221}, "status": "passed", "steps": [], "attachments": [{"uid": "aa0a78ab45c54966", "name": "测试总结", "source": "aa0a78ab45c54966.txt", "type": "text/plain", "size": 451}, {"uid": "c742130fb8095547", "name": "test_completed", "source": "c742130fb8095547.png", "type": "image/png", "size": 543937}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证文件存在", "time": {"start": 1754447514497, "stop": 1754447514497, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447514497, "stop": 1754447514675, "duration": 178}, "status": "passed", "steps": [], "attachments": [{"uid": "b949c659442f1cf4", "name": "测试总结", "source": "b949c659442f1cf4.txt", "type": "text/plain", "size": 451}, {"uid": "2d0b439653fdf38", "name": "test_completed", "source": "2d0b439653fdf38.png", "type": "image/png", "size": 542985}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "34f6c4b24c92aca5", "name": "stdout", "source": "34f6c4b24c92aca5.txt", "type": "text/plain", "size": 13305}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447514675, "stop": 1754447514675, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447514677, "stop": 1754447516082, "duration": 1405}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_take_a_screenshot"}, {"name": "subSuite", "value": "TestEllaTakeScreenshot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_take_a_screenshot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "b5b647eaf24aceba", "status": "failed", "statusDetails": "AssertionError: 文件不存在！\nassert False", "time": {"start": 1754397779894, "stop": 1754397792684, "duration": 12790}}], "categories": [], "tags": ["smoke"]}, "source": "6443b855f11fc58.json", "parameterValues": []}