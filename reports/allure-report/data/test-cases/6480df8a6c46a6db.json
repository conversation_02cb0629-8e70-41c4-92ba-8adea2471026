{"uid": "6480df8a6c46a6db", "name": "测试navigate from to red square能正常执行", "fullName": "testcases.test_ella.third_coupling.test_navigate_from_to_red_square.TestEllaNavigateFromRedSquare#test_navigate_from_to_red_square", "historyId": "e8f03971277a71512b5ebaad612bc964", "time": {"start": 1754451457990, "stop": 1754451478299, "duration": 20309}, "description": "navigate from to red square", "descriptionHtml": "<p>navigate from to red square</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451445530, "stop": 1754451457989, "duration": 12459}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451457990, "stop": 1754451457990, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "navigate from to red square", "status": "passed", "steps": [{"name": "执行命令: navigate from to red square", "time": {"start": 1754451457990, "stop": 1754451478110, "duration": 20120}, "status": "passed", "steps": [{"name": "执行命令: navigate from to red square", "time": {"start": 1754451457990, "stop": 1754451477906, "duration": 19916}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451477906, "stop": 1754451478110, "duration": 204}, "status": "passed", "steps": [], "attachments": [{"uid": "12241ea9e5fbf2f4", "name": "测试总结", "source": "12241ea9e5fbf2f4.txt", "type": "text/plain", "size": 244}, {"uid": "3c601e0175eef277", "name": "test_completed", "source": "3c601e0175eef277.png", "type": "image/png", "size": 436474}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451478110, "stop": 1754451478112, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451478112, "stop": 1754451478299, "duration": 187}, "status": "passed", "steps": [], "attachments": [{"uid": "bae43173481bb8df", "name": "测试总结", "source": "bae43173481bb8df.txt", "type": "text/plain", "size": 244}, {"uid": "9d47b247e3ef5453", "name": "test_completed", "source": "9d47b247e3ef5453.png", "type": "image/png", "size": 436474}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d42ce4d123b18093", "name": "stdout", "source": "d42ce4d123b18093.txt", "type": "text/plain", "size": 16580}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451478299, "stop": 1754451478301, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451478301, "stop": 1754451479684, "duration": 1383}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigate_from_to_red_square"}, {"name": "subSuite", "value": "TestEllaNavigateFromRedSquare"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigate_from_to_red_square"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "30df45280102ccec", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754401608878, "stop": 1754401629332, "duration": 20454}}], "categories": [], "tags": ["smoke"]}, "source": "6480df8a6c46a6db.json", "parameterValues": []}