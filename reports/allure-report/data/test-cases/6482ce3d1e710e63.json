{"uid": "6482ce3d1e710e63", "name": "测试what's the wheather today?能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_what_s_the_wheather_today.TestEllaWhatSWheatherToday#test_what_s_the_wheather_today", "historyId": "7c862e3b7376f0fe8afd8d3c5d41a1ab", "time": {"start": 1754455583977, "stop": 1754455598021, "duration": 14044}, "description": "what's the wheather today?", "descriptionHtml": "<p>what's the wheather today?</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455571429, "stop": 1754455583976, "duration": 12547}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455583976, "stop": 1754455583976, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "what's the wheather today?", "status": "passed", "steps": [{"name": "执行命令: what's the wheather today?", "time": {"start": 1754455583977, "stop": 1754455597805, "duration": 13828}, "status": "passed", "steps": [{"name": "执行命令: what's the wheather today?", "time": {"start": 1754455583977, "stop": 1754455597606, "duration": 13629}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455597606, "stop": 1754455597805, "duration": 199}, "status": "passed", "steps": [], "attachments": [{"uid": "f84783f94b035f53", "name": "测试总结", "source": "f84783f94b035f53.txt", "type": "text/plain", "size": 266}, {"uid": "559ac50d6d817bb6", "name": "test_completed", "source": "559ac50d6d817bb6.png", "type": "image/png", "size": 499568}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754455597806, "stop": 1754455597808, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455597808, "stop": 1754455598021, "duration": 213}, "status": "passed", "steps": [], "attachments": [{"uid": "b116a843f5340ec0", "name": "测试总结", "source": "b116a843f5340ec0.txt", "type": "text/plain", "size": 266}, {"uid": "ee18bfceef0fa8a4", "name": "test_completed", "source": "ee18bfceef0fa8a4.png", "type": "image/png", "size": 499485}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "e06fc7d7889f5406", "name": "stdout", "source": "e06fc7d7889f5406.txt", "type": "text/plain", "size": 12004}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455598022, "stop": 1754455598022, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455598023, "stop": 1754455599423, "duration": 1400}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_what_s_the_wheather_today"}, {"name": "subSuite", "value": "TestEllaWhatSWheatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_what_s_the_wheather_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "f026f66cbf7e42b7", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['is', 'The high is forecast as', 'and the low as']\nassert False", "time": {"start": 1754405625110, "stop": 1754405637918, "duration": 12808}}], "categories": [], "tags": ["smoke"]}, "source": "6482ce3d1e710e63.json", "parameterValues": []}