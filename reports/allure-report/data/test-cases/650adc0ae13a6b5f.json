{"uid": "650adc0ae13a6b5f", "name": "测试open contact命令", "fullName": "testcases.test_ella.component_coupling.test_open_contact.TestEllaContactCommandConcise#test_open_contact", "historyId": "7c32e753573a480d7d5c09abab43469e", "time": {"start": 1754446879576, "stop": 1754446902465, "duration": 22889}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754446867246, "stop": 1754446879575, "duration": 12329}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754446879575, "stop": 1754446879575, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "passed", "steps": [{"name": "执行命令: open contact", "time": {"start": 1754446879576, "stop": 1754446902250, "duration": 22674}, "status": "passed", "steps": [{"name": "执行命令: open contact", "time": {"start": 1754446879576, "stop": 1754446902028, "duration": 22452}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446902028, "stop": 1754446902250, "duration": 222}, "status": "passed", "steps": [], "attachments": [{"uid": "5a24cd1eb9431cee", "name": "测试总结", "source": "5a24cd1eb9431cee.txt", "type": "text/plain", "size": 208}, {"uid": "5748f7aff2d51477", "name": "test_completed", "source": "5748f7aff2d51477.png", "type": "image/png", "size": 555066}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含Done", "time": {"start": 1754446902250, "stop": 1754446902251, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证Dalier应用已打开", "time": {"start": 1754446902251, "stop": 1754446902251, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446902251, "stop": 1754446902464, "duration": 213}, "status": "passed", "steps": [], "attachments": [{"uid": "369d8bea462388f7", "name": "测试总结", "source": "369d8bea462388f7.txt", "type": "text/plain", "size": 208}, {"uid": "576b96da8e5ec4b5", "name": "test_completed", "source": "576b96da8e5ec4b5.png", "type": "image/png", "size": 554714}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "24ee1691a1519b8e", "name": "stdout", "source": "24ee1691a1519b8e.txt", "type": "text/plain", "size": 14386}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754446902465, "stop": 1754446902465, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754446902466, "stop": 1754446903805, "duration": 1339}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "联系人控制命令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_contact"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_contact"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "579535b7c694d7cc", "status": "passed", "time": {"start": 1754397185412, "stop": 1754397203564, "duration": 18152}}], "categories": [], "tags": ["smoke"]}, "source": "650adc0ae13a6b5f.json", "parameterValues": []}