{"uid": "656ca2d1faac87de", "name": "测试jump to battery and power saving返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_battery_and_power_saving.TestEllaJumpBatteryPowerSaving#test_jump_to_battery_and_power_saving", "historyId": "436193bc4e8c44d21b6520da0589f88d", "time": {"start": 1754453202437, "stop": 1754453217767, "duration": 15330}, "description": "验证jump to battery and power saving指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to battery and power saving指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453190262, "stop": 1754453202436, "duration": 12174}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453202436, "stop": 1754453202436, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证jump to battery and power saving指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: jump to battery and power saving", "time": {"start": 1754453202437, "stop": 1754453217562, "duration": 15125}, "status": "passed", "steps": [{"name": "执行命令: jump to battery and power saving", "time": {"start": 1754453202437, "stop": 1754453217362, "duration": 14925}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453217362, "stop": 1754453217561, "duration": 199}, "status": "passed", "steps": [], "attachments": [{"uid": "ee3b98e695da40ea", "name": "测试总结", "source": "ee3b98e695da40ea.txt", "type": "text/plain", "size": 260}, {"uid": "2215bcc9fb095b2b", "name": "test_completed", "source": "2215bcc9fb095b2b.png", "type": "image/png", "size": 506013}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453217562, "stop": 1754453217563, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453217563, "stop": 1754453217767, "duration": 204}, "status": "passed", "steps": [], "attachments": [{"uid": "c99ca02e4e0f62ff", "name": "测试总结", "source": "c99ca02e4e0f62ff.txt", "type": "text/plain", "size": 260}, {"uid": "2f290313b6c1d41c", "name": "test_completed", "source": "2f290313b6c1d41c.png", "type": "image/png", "size": 505895}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "e1732377c16f0132", "name": "stdout", "source": "e1732377c16f0132.txt", "type": "text/plain", "size": 11976}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453217768, "stop": 1754453217768, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453217769, "stop": 1754453219172, "duration": 1403}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_battery_and_power_saving"}, {"name": "subSuite", "value": "TestEllaJumpBatteryPowerSaving"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_battery_and_power_saving"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "b2b16d888f1ac4b0", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403218514, "stop": 1754403233951, "duration": 15437}}], "categories": [], "tags": ["smoke"]}, "source": "656ca2d1faac87de.json", "parameterValues": []}