{"uid": "6827be8ffca997a7", "name": "测试help me take a screenshot能正常执行", "fullName": "testcases.test_ella.system_coupling.test_help_me_take_a_screenshot.TestEllaHelpMeTakeScreenshot#test_help_me_take_a_screenshot", "historyId": "459c099a876d1129ddcb7cb28663b756", "time": {"start": 1754399916015, "stop": 1754399931170, "duration": 15155}, "description": "help me take a screenshot", "descriptionHtml": "<p>help me take a screenshot</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399902915, "stop": 1754399916014, "duration": 13099}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399916014, "stop": 1754399916014, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "help me take a screenshot", "status": "passed", "steps": [{"name": "执行命令: help me take a screenshot", "time": {"start": 1754399916015, "stop": 1754399930845, "duration": 14830}, "status": "passed", "steps": [{"name": "执行命令: help me take a screenshot", "time": {"start": 1754399916015, "stop": 1754399930579, "duration": 14564}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399930579, "stop": 1754399930843, "duration": 264}, "status": "passed", "steps": [], "attachments": [{"uid": "af3ba2f176d40c8d", "name": "测试总结", "source": "af3ba2f176d40c8d.txt", "type": "text/plain", "size": 490}, {"uid": "e66929723f2aacd0", "name": "test_completed", "source": "e66929723f2aacd0.png", "type": "image/png", "size": 559548}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证文件存在", "time": {"start": 1754399930846, "stop": 1754399930846, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399930846, "stop": 1754399931168, "duration": 322}, "status": "passed", "steps": [], "attachments": [{"uid": "d3c092ebe95f72b", "name": "测试总结", "source": "d3c092ebe95f72b.txt", "type": "text/plain", "size": 490}, {"uid": "535bacb05ee77fe4", "name": "test_completed", "source": "535bacb05ee77fe4.png", "type": "image/png", "size": 560806}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "c70a37bbab788b9e", "name": "stdout", "source": "c70a37bbab788b9e.txt", "type": "text/plain", "size": 13457}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399931171, "stop": 1754399931171, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399931172, "stop": 1754399932512, "duration": 1340}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_help_me_take_a_screenshot"}, {"name": "subSuite", "value": "TestEllaHelpMeTakeScreenshot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_help_me_take_a_screenshot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "6827be8ffca997a7.json", "parameterValues": []}