{"uid": "6a31892470e25970", "name": "测试Help me write an email to make an appointment for a visit能正常执行", "fullName": "testcases.test_ella.dialogue.test_help_me_write_an_email_to_make_an_appointment_for_a_visit.TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit#test_help_me_write_an_email_to_make_an_appointment_for_a_visit", "historyId": "12eb3852c333145c5906579f2346c37a", "time": {"start": 1754447927479, "stop": 1754447941296, "duration": 13817}, "description": "Help me write an email to make an appointment for a visit", "descriptionHtml": "<p>Help me write an email to make an appointment for a visit</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447914810, "stop": 1754447927478, "duration": 12668}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447927478, "stop": 1754447927478, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "Help me write an email to make an appointment for a visit", "status": "passed", "steps": [{"name": "执行命令: Help me write an email to make an appointment for a visit", "time": {"start": 1754447927479, "stop": 1754447941081, "duration": 13602}, "status": "passed", "steps": [{"name": "执行命令: Help me write an email to make an appointment for a visit", "time": {"start": 1754447927479, "stop": 1754447940870, "duration": 13391}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447940870, "stop": 1754447941081, "duration": 211}, "status": "passed", "steps": [], "attachments": [{"uid": "82c481fd5fb27398", "name": "测试总结", "source": "82c481fd5fb27398.txt", "type": "text/plain", "size": 1125}, {"uid": "78b3fabf65e9afc8", "name": "test_completed", "source": "78b3fabf65e9afc8.png", "type": "image/png", "size": 779575}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447941081, "stop": 1754447941083, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447941083, "stop": 1754447941295, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "a1063f2d072b784e", "name": "测试总结", "source": "a1063f2d072b784e.txt", "type": "text/plain", "size": 1125}, {"uid": "45820d51f5bb4de4", "name": "test_completed", "source": "45820d51f5bb4de4.png", "type": "image/png", "size": 779833}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "8c1461acc61f4221", "name": "stdout", "source": "8c1461acc61f4221.txt", "type": "text/plain", "size": 16161}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447941296, "stop": 1754447941296, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447941297, "stop": 1754447942684, "duration": 1387}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_help_me_write_an_email_to_make_an_appointment_for_a_visit"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_help_me_write_an_email_to_make_an_appointment_for_a_visit"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "4dad5ef93da37ee6", "status": "passed", "time": {"start": 1754398200943, "stop": 1754398214377, "duration": 13434}}], "categories": [], "tags": ["smoke"]}, "source": "6a31892470e25970.json", "parameterValues": []}