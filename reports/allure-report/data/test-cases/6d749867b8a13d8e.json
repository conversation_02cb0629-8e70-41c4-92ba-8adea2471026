{"uid": "6d749867b8a13d8e", "name": "测试take a joke能正常执行", "fullName": "testcases.test_ella.dialogue.test_take_a_joke.TestEllaTakeJoke#test_take_a_joke", "historyId": "543965b4120af95548616c95b1b70ef1", "time": {"start": 1754398965423, "stop": 1754398979207, "duration": 13784}, "description": "take a joke", "descriptionHtml": "<p>take a joke</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398952340, "stop": 1754398965422, "duration": 13082}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398965422, "stop": 1754398965422, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "take a joke", "status": "passed", "steps": [{"name": "执行命令: take a joke", "time": {"start": 1754398965423, "stop": 1754398978909, "duration": 13486}, "status": "passed", "steps": [{"name": "执行命令: take a joke", "time": {"start": 1754398965423, "stop": 1754398978618, "duration": 13195}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398978619, "stop": 1754398978907, "duration": 288}, "status": "passed", "steps": [], "attachments": [{"uid": "954e05ba50029079", "name": "测试总结", "source": "954e05ba50029079.txt", "type": "text/plain", "size": 439}, {"uid": "cded0fb23ff2d671", "name": "test_completed", "source": "cded0fb23ff2d671.png", "type": "image/png", "size": 568911}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398978909, "stop": 1754398978912, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398978912, "stop": 1754398979207, "duration": 295}, "status": "passed", "steps": [], "attachments": [{"uid": "25179241af5d3956", "name": "测试总结", "source": "25179241af5d3956.txt", "type": "text/plain", "size": 439}, {"uid": "c69b244a5a1ff6f", "name": "test_completed", "source": "c69b244a5a1ff6f.png", "type": "image/png", "size": 568458}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "c7c18e4ca3efb7b5", "name": "stdout", "source": "c7c18e4ca3efb7b5.txt", "type": "text/plain", "size": 12963}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398979209, "stop": 1754398979209, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398979210, "stop": 1754398980468, "duration": 1258}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_a_joke"}, {"name": "subSuite", "value": "TestEllaTakeJoke"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_a_joke"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "6d749867b8a13d8e.json", "parameterValues": []}