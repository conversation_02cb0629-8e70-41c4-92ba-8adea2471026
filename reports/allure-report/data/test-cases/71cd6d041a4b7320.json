{"uid": "71cd6d041a4b7320", "name": "测试set screen refresh rate返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_screen_refresh_rate.TestEllaSetScreenRefreshRate#test_set_screen_refresh_rate", "historyId": "1bc9389e45f0f75c30d3dfb39134948d", "time": {"start": 1754454885302, "stop": 1754454898804, "duration": 13502}, "description": "验证set screen refresh rate指令返回预期的不支持响应", "descriptionHtml": "<p>验证set screen refresh rate指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454873488, "stop": 1754454885301, "duration": 11813}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454885301, "stop": 1754454885302, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set screen refresh rate指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set screen refresh rate", "time": {"start": 1754454885302, "stop": 1754454898609, "duration": 13307}, "status": "passed", "steps": [{"name": "执行命令: set screen refresh rate", "time": {"start": 1754454885302, "stop": 1754454898406, "duration": 13104}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454898406, "stop": 1754454898609, "duration": 203}, "status": "passed", "steps": [], "attachments": [{"uid": "60410062e3f85afb", "name": "测试总结", "source": "60410062e3f85afb.txt", "type": "text/plain", "size": 239}, {"uid": "c65915bcbfcae15e", "name": "test_completed", "source": "c65915bcbfcae15e.png", "type": "image/png", "size": 501350}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454898609, "stop": 1754454898610, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454898610, "stop": 1754454898804, "duration": 194}, "status": "passed", "steps": [], "attachments": [{"uid": "7534b9a232c29790", "name": "测试总结", "source": "7534b9a232c29790.txt", "type": "text/plain", "size": 239}, {"uid": "ab0e3ce338bd30b2", "name": "test_completed", "source": "ab0e3ce338bd30b2.png", "type": "image/png", "size": 501579}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "4c2d849909fa4bf3", "name": "stdout", "source": "4c2d849909fa4bf3.txt", "type": "text/plain", "size": 11352}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454898804, "stop": 1754454898804, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454898805, "stop": 1754454900133, "duration": 1328}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_screen_refresh_rate"}, {"name": "subSuite", "value": "TestEllaSetScreenRefreshRate"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_screen_refresh_rate"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "979737cc692b98a9", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404891455, "stop": 1754404908244, "duration": 16789}}], "categories": [], "tags": ["smoke"]}, "source": "71cd6d041a4b7320.json", "parameterValues": []}