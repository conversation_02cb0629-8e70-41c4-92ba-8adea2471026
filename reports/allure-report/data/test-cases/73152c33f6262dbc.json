{"uid": "73152c33f6262dbc", "name": "测试summarize content on this page能正常执行", "fullName": "testcases.test_ella.dialogue.test_summarize_content_on_this_page.TestEllaSummarizeContentThisPage#test_summarize_content_on_this_page", "historyId": "3e1e4da6344de7cdf40fa1d59c43dcc3", "time": {"start": 1754448712091, "stop": 1754448726036, "duration": 13945}, "description": "summarize content on this page", "descriptionHtml": "<p>summarize content on this page</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448699375, "stop": 1754448712089, "duration": 12714}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448712089, "stop": 1754448712089, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "summarize content on this page", "status": "passed", "steps": [{"name": "执行命令: summarize content on this page", "time": {"start": 1754448712091, "stop": 1754448725829, "duration": 13738}, "status": "passed", "steps": [{"name": "执行命令: summarize content on this page", "time": {"start": 1754448712091, "stop": 1754448725567, "duration": 13476}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448725567, "stop": 1754448725829, "duration": 262}, "status": "passed", "steps": [], "attachments": [{"uid": "49ab1cbde31464c2", "name": "测试总结", "source": "49ab1cbde31464c2.txt", "type": "text/plain", "size": 277}, {"uid": "573517d871cace18", "name": "test_completed", "source": "573517d871cace18.png", "type": "image/png", "size": 618315}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448725829, "stop": 1754448725831, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448725831, "stop": 1754448726036, "duration": 205}, "status": "passed", "steps": [], "attachments": [{"uid": "1859e92b483a96a", "name": "测试总结", "source": "1859e92b483a96a.txt", "type": "text/plain", "size": 277}, {"uid": "11ec870e2b019d15", "name": "test_completed", "source": "11ec870e2b019d15.png", "type": "image/png", "size": 618677}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "c32dcc610bc5855f", "name": "stdout", "source": "c32dcc610bc5855f.txt", "type": "text/plain", "size": 11510}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754448726037, "stop": 1754448727469, "duration": 1432}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754448726037, "stop": 1754448726037, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_summarize_content_on_this_page"}, {"name": "subSuite", "value": "TestEllaSummarizeContentThisPage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_summarize_content_on_this_page"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "ea6730154255a6b7", "status": "passed", "time": {"start": 1754398910901, "stop": 1754398923728, "duration": 12827}}], "categories": [], "tags": ["smoke"]}, "source": "73152c33f6262dbc.json", "parameterValues": []}