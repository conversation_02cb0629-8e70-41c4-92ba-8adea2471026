{"uid": "73784dd66b275a1b", "name": "测试appeler maman能正常执行", "fullName": "testcases.test_ella.dialogue.test_appeler_maman.TestEllaAppelerMaman#test_appeler_maman", "historyId": "c5050ea089fe0f7a5b962119cd32b32e", "time": {"start": 1754397841751, "stop": 1754397854561, "duration": 12810}, "description": "appeler maman", "descriptionHtml": "<p>appeler maman</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397828915, "stop": 1754397841751, "duration": 12836}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397841751, "stop": 1754397841751, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "appeler maman", "status": "passed", "steps": [{"name": "执行命令: appeler maman", "time": {"start": 1754397841751, "stop": 1754397854298, "duration": 12547}, "status": "passed", "steps": [{"name": "执行命令: appeler maman", "time": {"start": 1754397841751, "stop": 1754397854005, "duration": 12254}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397854005, "stop": 1754397854297, "duration": 292}, "status": "passed", "steps": [], "attachments": [{"uid": "368c0962e398edd6", "name": "测试总结", "source": "368c0962e398edd6.txt", "type": "text/plain", "size": 192}, {"uid": "886fd0e8b2277ee3", "name": "test_completed", "source": "886fd0e8b2277ee3.png", "type": "image/png", "size": 541590}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754397854298, "stop": 1754397854302, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397854302, "stop": 1754397854560, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "688f49283f3f03e6", "name": "测试总结", "source": "688f49283f3f03e6.txt", "type": "text/plain", "size": 192}, {"uid": "360c6a8db88fe049", "name": "test_completed", "source": "360c6a8db88fe049.png", "type": "image/png", "size": 542100}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3ecfa2e8f21f3ed1", "name": "stdout", "source": "3ecfa2e8f21f3ed1.txt", "type": "text/plain", "size": 11182}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397854562, "stop": 1754397854562, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397854564, "stop": 1754397855867, "duration": 1303}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_appeler_maman"}, {"name": "subSuite", "value": "Test<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_appeler_maman"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "73784dd66b275a1b.json", "parameterValues": []}