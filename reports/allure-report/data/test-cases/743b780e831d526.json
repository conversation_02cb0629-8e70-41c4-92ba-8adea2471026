{"uid": "743b780e831d526", "name": "测试minimum volume能正常执行", "fullName": "testcases.test_ella.system_coupling.test_minimum_volume.TestEllaMinimumVolume#test_minimum_volume", "historyId": "7cd08c87d5de8ec73ac863e8a636c8aa", "time": {"start": 1754449863875, "stop": 1754449878772, "duration": 14897}, "description": "minimum volume", "descriptionHtml": "<p>minimum volume</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449851293, "stop": 1754449863874, "duration": 12581}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449863874, "stop": 1754449863874, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "minimum volume", "status": "passed", "steps": [{"name": "执行命令: minimum volume", "time": {"start": 1754449863875, "stop": 1754449878539, "duration": 14664}, "status": "passed", "steps": [{"name": "执行命令: minimum volume", "time": {"start": 1754449863875, "stop": 1754449878331, "duration": 14456}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449878331, "stop": 1754449878539, "duration": 208}, "status": "passed", "steps": [], "attachments": [{"uid": "a8cee3df13934bab", "name": "测试总结", "source": "a8cee3df13934bab.txt", "type": "text/plain", "size": 186}, {"uid": "388725dd7892545", "name": "test_completed", "source": "388725dd7892545.png", "type": "image/png", "size": 625408}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754449878539, "stop": 1754449878540, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754449878540, "stop": 1754449878540, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449878540, "stop": 1754449878770, "duration": 230}, "status": "passed", "steps": [], "attachments": [{"uid": "7b9be8b3c9d4fb51", "name": "测试总结", "source": "7b9be8b3c9d4fb51.txt", "type": "text/plain", "size": 186}, {"uid": "205f415eb8a96fcb", "name": "test_completed", "source": "205f415eb8a96fcb.png", "type": "image/png", "size": 625744}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "993a269fe32d78dd", "name": "stdout", "source": "993a269fe32d78dd.txt", "type": "text/plain", "size": 12102}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449878772, "stop": 1754449878772, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449878773, "stop": 1754449880176, "duration": 1403}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_minimum_volume"}, {"name": "subSuite", "value": "TestEllaMinimumVolume"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_minimum_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "c5f687104531f3a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Media volume has been set to the minimum']\nassert False", "time": {"start": 1754400043877, "stop": 1754400056644, "duration": 12767}}], "categories": [], "tags": ["smoke"]}, "source": "743b780e831d526.json", "parameterValues": []}