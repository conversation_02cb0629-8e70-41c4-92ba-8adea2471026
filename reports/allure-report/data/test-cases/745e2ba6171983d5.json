{"uid": "745e2ba6171983d5", "name": "测试who is j k rowling能正常执行", "fullName": "testcases.test_ella.dialogue.test_who_is_j_k_rowling.TestEllaWhoIsJKRowling#test_who_is_j_k_rowling", "historyId": "1a5cbbb97cbe59e003ae71750a8d910f", "time": {"start": 1754449193146, "stop": 1754449209426, "duration": 16280}, "description": "who is j k rowling", "descriptionHtml": "<p>who is j k rowling</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449180490, "stop": 1754449193143, "duration": 12653}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449193143, "stop": 1754449193144, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "who is j k rowling", "status": "passed", "steps": [{"name": "执行命令: who is j k rowling", "time": {"start": 1754449193146, "stop": 1754449209227, "duration": 16081}, "status": "passed", "steps": [{"name": "执行命令: who is j k rowling", "time": {"start": 1754449193146, "stop": 1754449209022, "duration": 15876}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449209022, "stop": 1754449209227, "duration": 205}, "status": "passed", "steps": [], "attachments": [{"uid": "1dc46e62386b7acf", "name": "测试总结", "source": "1dc46e62386b7acf.txt", "type": "text/plain", "size": 388}, {"uid": "4bab93a7722cbe15", "name": "test_completed", "source": "4bab93a7722cbe15.png", "type": "image/png", "size": 654286}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754449209227, "stop": 1754449209228, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449209228, "stop": 1754449209425, "duration": 197}, "status": "passed", "steps": [], "attachments": [{"uid": "9aa4ca35ac1654f6", "name": "测试总结", "source": "9aa4ca35ac1654f6.txt", "type": "text/plain", "size": 388}, {"uid": "2cd30e49a95f690b", "name": "test_completed", "source": "2cd30e49a95f690b.png", "type": "image/png", "size": 654283}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d1b643581d930794", "name": "stdout", "source": "d1b643581d930794.txt", "type": "text/plain", "size": 12786}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449209427, "stop": 1754449209427, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449209427, "stop": 1754449210836, "duration": 1409}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_who_is_j_k_rowling"}, {"name": "subSuite", "value": "TestEllaWhoIsJKRowling"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_who_is_j_k_rowling"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "4cf52d0e6ea4d2ce", "status": "passed", "time": {"start": 1754399383324, "stop": 1754399409890, "duration": 26566}}], "categories": [], "tags": ["smoke"]}, "source": "745e2ba6171983d5.json", "parameterValues": []}