{"uid": "74841191efeb3379", "name": "测试how's the weather today in shanghai能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_s_the_weather_today_in_shanghai.TestEllaWhatSWeatherLikeShanghaiToday#test_what_s_the_weather_like_in_shanghai_today", "historyId": "bd4d204a449f3a4013b03af9a9101446", "time": {"start": 1754448081733, "stop": 1754448102779, "duration": 21046}, "description": "how's the weather today in shanghai", "descriptionHtml": "<p>how's the weather today in shanghai</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448068990, "stop": 1754448081732, "duration": 12742}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448081732, "stop": 1754448081732, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "how's the weather today in shanghai", "status": "passed", "steps": [{"name": "执行命令: how's the weather today in shanghai", "time": {"start": 1754448081733, "stop": 1754448102556, "duration": 20823}, "status": "passed", "steps": [{"name": "执行命令: how's the weather today in shanghai", "time": {"start": 1754448081733, "stop": 1754448102345, "duration": 20612}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448102345, "stop": 1754448102556, "duration": 211}, "status": "passed", "steps": [], "attachments": [{"uid": "23c0291a52788b51", "name": "测试总结", "source": "23c0291a52788b51.txt", "type": "text/plain", "size": 278}, {"uid": "ec1115113657325d", "name": "test_completed", "source": "ec1115113657325d.png", "type": "image/png", "size": 552509}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448102556, "stop": 1754448102557, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448102557, "stop": 1754448102778, "duration": 221}, "status": "passed", "steps": [], "attachments": [{"uid": "5ef804444a8a7e59", "name": "测试总结", "source": "5ef804444a8a7e59.txt", "type": "text/plain", "size": 278}, {"uid": "774f9e7ba3b8e565", "name": "test_completed", "source": "774f9e7ba3b8e565.png", "type": "image/png", "size": 552043}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3c8f18fbaec94429", "name": "stdout", "source": "3c8f18fbaec94429.txt", "type": "text/plain", "size": 11941}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448102779, "stop": 1754448102779, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448102781, "stop": 1754448104161, "duration": 1380}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_s_the_weather_today_in_shanghai"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherLikeShanghaiToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_s_the_weather_today_in_shanghai"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "81941d3cfb50de0e", "status": "passed", "time": {"start": 1754398352397, "stop": 1754398372537, "duration": 20140}}], "categories": [], "tags": ["smoke"]}, "source": "74841191efeb3379.json", "parameterValues": []}