{"uid": "7595375f57ce60c8", "name": "测试help me take a long screenshot能正常执行", "fullName": "testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot.TestEllaHelpMeTakeLongScreenshot#test_help_me_take_a_long_screenshot", "historyId": "fe3d09fe0bad56e7804ef2f5ea49d283", "time": {"start": 1754449699015, "stop": 1754449717676, "duration": 18661}, "description": "help me take a long screenshot", "descriptionHtml": "<p>help me take a long screenshot</p>\n", "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False", "statusTrace": "self = <testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot.TestEllaHelpMeTakeLongScreenshot object at 0x000001E3893EEA90>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E389EA2910>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_help_me_take_a_long_screenshot(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=True\n            )\n    \n        with allure.step(f\"验证文件存在\"):\n>           assert files_status, f\"文件不存在！\"\nE           AssertionError: 文件不存在！\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_help_me_take_a_long_screenshot.py:32: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449686096, "stop": 1754449699014, "duration": 12918}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449699014, "stop": 1754449699014, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "help me take a long screenshot", "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False", "statusTrace": "self = <testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot.TestEllaHelpMeTakeLongScreenshot object at 0x000001E3893EEA90>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E389EA2910>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_help_me_take_a_long_screenshot(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=True\n            )\n    \n        with allure.step(f\"验证文件存在\"):\n>           assert files_status, f\"文件不存在！\"\nE           AssertionError: 文件不存在！\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_help_me_take_a_long_screenshot.py:32: AssertionError", "steps": [{"name": "执行命令: help me take a long screenshot", "time": {"start": 1754449699015, "stop": 1754449717675, "duration": 18660}, "status": "passed", "steps": [{"name": "执行命令: help me take a long screenshot", "time": {"start": 1754449699015, "stop": 1754449717438, "duration": 18423}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449717438, "stop": 1754449717675, "duration": 237}, "status": "passed", "steps": [], "attachments": [{"uid": "df755d8b50607656", "name": "测试总结", "source": "df755d8b50607656.txt", "type": "text/plain", "size": 555}, {"uid": "e98407fcacca0ae8", "name": "test_completed", "source": "e98407fcacca0ae8.png", "type": "image/png", "size": 564270}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证文件存在", "time": {"start": 1754449717675, "stop": 1754449717676, "duration": 1}, "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\system_coupling\\test_help_me_take_a_long_screenshot.py\", line 32, in test_help_me_take_a_long_screenshot\n    assert files_status, f\"文件不存在！\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "a87a93f2f79e0adc", "name": "stdout", "source": "a87a93f2f79e0adc.txt", "type": "text/plain", "size": 15090}], "parameters": [], "attachmentStep": false, "stepsCount": 4, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449717679, "stop": 1754449717881, "duration": 202}, "status": "passed", "steps": [], "attachments": [{"uid": "369dc7ff9641a281", "name": "失败截图-TestEllaHelpMeTakeLongScreenshot", "source": "369dc7ff9641a281.png", "type": "image/png", "size": 561122}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754449717882, "stop": 1754449719315, "duration": 1433}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_help_me_take_a_long_screenshot"}, {"name": "subSuite", "value": "TestEllaHelpMeTakeLongScreenshot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "33cfa9681d0b6933", "status": "failed", "statusDetails": "AssertionError: 文件不存在！\nassert False", "time": {"start": 1754399884489, "stop": 1754399901283, "duration": 16794}}], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "7595375f57ce60c8.json", "parameterValues": []}