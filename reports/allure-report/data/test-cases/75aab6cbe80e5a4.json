{"uid": "75aab6cbe80e5a4", "name": "测试yandex eats返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_yandex_eats.TestEllaYandexEats#test_yandex_eats", "historyId": "b911308f3c1fe764715d778a884946c2", "time": {"start": 1754455612062, "stop": 1754455626406, "duration": 14344}, "description": "验证yandex eats指令返回预期的不支持响应", "descriptionHtml": "<p>验证yandex eats指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455599434, "stop": 1754455612061, "duration": 12627}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455612061, "stop": 1754455612061, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证yandex eats指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: yandex eats", "time": {"start": 1754455612062, "stop": 1754455626175, "duration": 14113}, "status": "passed", "steps": [{"name": "执行命令: yandex eats", "time": {"start": 1754455612062, "stop": 1754455625949, "duration": 13887}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455625949, "stop": 1754455626175, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "8a75cb20bb0258de", "name": "测试总结", "source": "8a75cb20bb0258de.txt", "type": "text/plain", "size": 212}, {"uid": "5d8f48d255e0f175", "name": "test_completed", "source": "5d8f48d255e0f175.png", "type": "image/png", "size": 478692}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455626175, "stop": 1754455626176, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455626176, "stop": 1754455626405, "duration": 229}, "status": "passed", "steps": [], "attachments": [{"uid": "970cc7f0f47fbd5a", "name": "测试总结", "source": "970cc7f0f47fbd5a.txt", "type": "text/plain", "size": 212}, {"uid": "1e92ab02aa7d7af3", "name": "test_completed", "source": "1e92ab02aa7d7af3.png", "type": "image/png", "size": 478209}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "9ea3dba15cf69014", "name": "stdout", "source": "9ea3dba15cf69014.txt", "type": "text/plain", "size": 11564}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455626406, "stop": 1754455626406, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455626407, "stop": 1754455627765, "duration": 1358}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_yandex_eats"}, {"name": "subSuite", "value": "TestEllaYandexEats"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_yandex_eats"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "fb766181f848b01", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405652567, "stop": 1754405663727, "duration": 11160}}], "categories": [], "tags": ["smoke"]}, "source": "75aab6cbe80e5a4.json", "parameterValues": []}