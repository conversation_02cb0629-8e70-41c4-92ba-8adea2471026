{"uid": "75b158406577622d", "name": "测试set scheduled power on/off and restart返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_scheduled_power_on_off_and_restart.TestEllaSetScheduledPowerOffRestart#test_set_scheduled_power_on_off_and_restart", "historyId": "e21c5dda6a9f09862a68c3a0bcda554a", "time": {"start": 1754454856985, "stop": 1754454872199, "duration": 15214}, "description": "验证set scheduled power on/off and restart指令返回预期的不支持响应", "descriptionHtml": "<p>验证set scheduled power on/off and restart指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454845013, "stop": 1754454856983, "duration": 11970}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454856984, "stop": 1754454856984, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set scheduled power on/off and restart指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set scheduled power on/off and restart", "time": {"start": 1754454856985, "stop": 1754454872020, "duration": 15035}, "status": "passed", "steps": [{"name": "执行命令: set scheduled power on/off and restart", "time": {"start": 1754454856985, "stop": 1754454871831, "duration": 14846}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454871831, "stop": 1754454872020, "duration": 189}, "status": "passed", "steps": [], "attachments": [{"uid": "284e38dcaae7cfdd", "name": "测试总结", "source": "284e38dcaae7cfdd.txt", "type": "text/plain", "size": 284}, {"uid": "10241c4cd18318b7", "name": "test_completed", "source": "10241c4cd18318b7.png", "type": "image/png", "size": 536925}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454872020, "stop": 1754454872022, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454872022, "stop": 1754454872199, "duration": 177}, "status": "passed", "steps": [], "attachments": [{"uid": "84e7585cdb00e35d", "name": "测试总结", "source": "84e7585cdb00e35d.txt", "type": "text/plain", "size": 284}, {"uid": "a466a9b1c5ad6c47", "name": "test_completed", "source": "a466a9b1c5ad6c47.png", "type": "image/png", "size": 535863}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "48180e75f786c8cd", "name": "stdout", "source": "48180e75f786c8cd.txt", "type": "text/plain", "size": 12076}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454872200, "stop": 1754454872200, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454872201, "stop": 1754454873483, "duration": 1282}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_scheduled_power_on_off_and_restart"}, {"name": "subSuite", "value": "TestEllaSetScheduledPowerOffRestart"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_scheduled_power_on_off_and_restart"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "1af053dc3643bc05", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404859827, "stop": 1754404876609, "duration": 16782}}], "categories": [], "tags": ["smoke"]}, "source": "75b158406577622d.json", "parameterValues": []}