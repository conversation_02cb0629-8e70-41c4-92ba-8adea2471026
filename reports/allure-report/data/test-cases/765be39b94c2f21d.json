{"uid": "765be39b94c2f21d", "name": "测试take a note on how to build a treehouse能正常执行", "fullName": "testcases.test_ella.dialogue.test_take_a_note_on_how_to_build_a_treehouse.TestEllaTakeNoteHowBuildTreehouse#test_take_a_note_on_how_to_build_a_treehouse", "historyId": "c45aa63628fe12b375ba7e65c39d93b1", "time": {"start": 1754448797093, "stop": 1754448810624, "duration": 13531}, "description": "take a note on how to build a treehouse", "descriptionHtml": "<p>take a note on how to build a treehouse</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448784382, "stop": 1754448797092, "duration": 12710}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448797092, "stop": 1754448797092, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "take a note on how to build a treehouse", "status": "passed", "steps": [{"name": "执行命令: take a note on how to build a treehouse", "time": {"start": 1754448797093, "stop": 1754448810419, "duration": 13326}, "status": "passed", "steps": [{"name": "执行命令: take a note on how to build a treehouse", "time": {"start": 1754448797093, "stop": 1754448810223, "duration": 13130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448810223, "stop": 1754448810419, "duration": 196}, "status": "passed", "steps": [], "attachments": [{"uid": "d37a1aa9b53c4512", "name": "测试总结", "source": "d37a1aa9b53c4512.txt", "type": "text/plain", "size": 236}, {"uid": "120dee25796ac2ab", "name": "test_completed", "source": "120dee25796ac2ab.png", "type": "image/png", "size": 550460}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448810419, "stop": 1754448810422, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448810422, "stop": 1754448810624, "duration": 202}, "status": "passed", "steps": [], "attachments": [{"uid": "992c7833b8819d0", "name": "测试总结", "source": "992c7833b8819d0.txt", "type": "text/plain", "size": 236}, {"uid": "2d46b78e9da9f0c9", "name": "test_completed", "source": "2d46b78e9da9f0c9.png", "type": "image/png", "size": 550080}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "ca0b77e93e4f8354", "name": "stdout", "source": "ca0b77e93e4f8354.txt", "type": "text/plain", "size": 11432}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448810624, "stop": 1754448810624, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448810625, "stop": 1754448812035, "duration": 1410}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_a_note_on_how_to_build_a_treehouse"}, {"name": "subSuite", "value": "TestEllaTakeNoteHowBuildTreehouse"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_a_note_on_how_to_build_a_treehouse"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "79af219d3a0f25c5", "status": "passed", "time": {"start": 1754398993585, "stop": 1754399006413, "duration": 12828}}], "categories": [], "tags": ["smoke"]}, "source": "765be39b94c2f21d.json", "parameterValues": []}