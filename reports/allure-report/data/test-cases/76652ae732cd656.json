{"uid": "76652ae732cd656", "name": "测试set lockscreen passwords返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_lockscreen_passwords.TestEllaSetLockscreenPasswords#test_set_lockscreen_passwords", "historyId": "89b134ac1374e88187e793daf9f8fcab", "time": {"start": 1754454664856, "stop": 1754454678949, "duration": 14093}, "description": "验证set lockscreen passwords指令返回预期的不支持响应", "descriptionHtml": "<p>验证set lockscreen passwords指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454652440, "stop": 1754454664855, "duration": 12415}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454664856, "stop": 1754454664856, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set lockscreen passwords指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set lockscreen passwords", "time": {"start": 1754454664856, "stop": 1754454678745, "duration": 13889}, "status": "passed", "steps": [{"name": "执行命令: set lockscreen passwords", "time": {"start": 1754454664856, "stop": 1754454678571, "duration": 13715}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454678571, "stop": 1754454678745, "duration": 174}, "status": "passed", "steps": [], "attachments": [{"uid": "450e635eee11267f", "name": "测试总结", "source": "450e635eee11267f.txt", "type": "text/plain", "size": 243}, {"uid": "f37452b7beaed3b", "name": "test_completed", "source": "f37452b7beaed3b.png", "type": "image/png", "size": 514363}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454678745, "stop": 1754454678746, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454678746, "stop": 1754454678949, "duration": 203}, "status": "passed", "steps": [], "attachments": [{"uid": "331796b8c9c1c459", "name": "测试总结", "source": "331796b8c9c1c459.txt", "type": "text/plain", "size": 243}, {"uid": "c424243dcd3a290c", "name": "test_completed", "source": "c424243dcd3a290c.png", "type": "image/png", "size": 514313}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "a567bd7cf94d3083", "name": "stdout", "source": "a567bd7cf94d3083.txt", "type": "text/plain", "size": 11380}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454678950, "stop": 1754454678950, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454678951, "stop": 1754454680353, "duration": 1402}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_lockscreen_passwords"}, {"name": "subSuite", "value": "TestEllaSetLockscreenPasswords"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_lockscreen_passwords"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "21f4016d44e1d422", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404667548, "stop": 1754404684724, "duration": 17176}}], "categories": [], "tags": ["smoke"]}, "source": "76652ae732cd656.json", "parameterValues": []}