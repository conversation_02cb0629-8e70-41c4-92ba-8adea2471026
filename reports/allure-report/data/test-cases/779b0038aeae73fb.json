{"uid": "779b0038aeae73fb", "name": "测试take a selfie能正常执行", "fullName": "testcases.test_ella.system_coupling.test_take_a_selfie.TestEllaTakeSelfie#test_take_a_selfie", "historyId": "6c46a38570672e3c21f37ef82690d639", "time": {"start": 1754400970403, "stop": 1754400998323, "duration": 27920}, "description": "take a selfie", "descriptionHtml": "<p>take a selfie</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754400957200, "stop": 1754400970400, "duration": 13200}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754400970401, "stop": 1754400970401, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "take a selfie", "status": "passed", "steps": [{"name": "执行命令: take a selfie", "time": {"start": 1754400970403, "stop": 1754400998045, "duration": 27642}, "status": "passed", "steps": [{"name": "执行命令: take a selfie", "time": {"start": 1754400970403, "stop": 1754400997747, "duration": 27344}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754400997748, "stop": 1754400998044, "duration": 296}, "status": "passed", "steps": [], "attachments": [{"uid": "100a05f430f88524", "name": "测试总结", "source": "100a05f430f88524.txt", "type": "text/plain", "size": 273}, {"uid": "acc77e40585adc07", "name": "test_completed", "source": "acc77e40585adc07.png", "type": "image/png", "size": 551287}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证应用已打开", "time": {"start": 1754400998046, "stop": 1754400998046, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证文件存在", "time": {"start": 1754400998046, "stop": 1754400998046, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754400998046, "stop": 1754400998323, "duration": 277}, "status": "passed", "steps": [], "attachments": [{"uid": "8f74b4ddf5d1ede8", "name": "测试总结", "source": "8f74b4ddf5d1ede8.txt", "type": "text/plain", "size": 273}, {"uid": "a491e3870eec2b9e", "name": "test_completed", "source": "a491e3870eec2b9e.png", "type": "image/png", "size": 551391}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "ed7da4f51bbad1fa", "name": "stdout", "source": "ed7da4f51bbad1fa.txt", "type": "text/plain", "size": 15048}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754400998325, "stop": 1754400998325, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754400998326, "stop": 1754400999681, "duration": 1355}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_take_a_selfie"}, {"name": "subSuite", "value": "TestEllaTakeSelfie"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_take_a_selfie"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "779b0038aeae73fb.json", "parameterValues": []}