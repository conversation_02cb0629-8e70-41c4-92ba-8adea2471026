{"uid": "77a207bf58510ea3", "name": "测试whatsapp能正常执行", "fullName": "testcases.test_ella.third_coupling.test_whatsapp.TestEllaWhatsapp#test_whatsapp", "historyId": "fae56e9bcf9e0511ef4a7c93775731e3", "time": {"start": 1754401843149, "stop": 1754401855503, "duration": 12354}, "description": "whatsapp", "descriptionHtml": "<p>whatsapp</p>\n", "status": "broken", "statusMessage": "ValueError: too many values to unpack (expected 3)", "statusTrace": "self = <testcases.test_ella.third_coupling.test_whatsapp.TestEllaWhatsapp object at 0x00000240FF1D2890>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000024082AC4C50>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_whatsapp(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n>           initial_status, final_status, files_status = self.simple_command_test(\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\nE           ValueError: too many values to unpack (expected 3)\n\ntestcases\\test_ella\\third_coupling\\test_whatsapp.py:26: ValueError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754401829645, "stop": 1754401843148, "duration": 13503}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754401843148, "stop": 1754401843149, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "whatsapp", "status": "broken", "statusMessage": "ValueError: too many values to unpack (expected 3)", "statusTrace": "self = <testcases.test_ella.third_coupling.test_whatsapp.TestEllaWhatsapp object at 0x00000240FF1D2890>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000024082AC4C50>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_whatsapp(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n>           initial_status, final_status, files_status = self.simple_command_test(\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\nE           ValueError: too many values to unpack (expected 3)\n\ntestcases\\test_ella\\third_coupling\\test_whatsapp.py:26: ValueError", "steps": [{"name": "执行命令: whatsapp", "time": {"start": 1754401843149, "stop": 1754401855502, "duration": 12353}, "status": "broken", "statusMessage": "ValueError: too many values to unpack (expected 3)\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\third_coupling\\test_whatsapp.py\", line 26, in test_whatsapp\n    initial_status, final_status, files_status = self.simple_command_test(\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "steps": [{"name": "执行命令: whatsapp", "time": {"start": 1754401843149, "stop": 1754401855172, "duration": 12023}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754401855172, "stop": 1754401855500, "duration": 328}, "status": "passed", "steps": [], "attachments": [{"uid": "7f25642246124105", "name": "测试总结", "source": "7f25642246124105.txt", "type": "text/plain", "size": 221}, {"uid": "d6eaf1baf45a17", "name": "test_completed", "source": "d6eaf1baf45a17.png", "type": "image/png", "size": 601883}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 2}], "attachments": [{"uid": "64acda5eaa2a803", "name": "stdout", "source": "64acda5eaa2a803.txt", "type": "text/plain", "size": 11495}], "parameters": [], "attachmentStep": false, "stepsCount": 3, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754401855515, "stop": 1754401855809, "duration": 294}, "status": "passed", "steps": [], "attachments": [{"uid": "efc2ef166ade8319", "name": "失败截图-TestEllaWhatsapp", "source": "efc2ef166ade8319.png", "type": "image/png", "size": 601809}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754401855812, "stop": 1754401857082, "duration": 1270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_whatsapp"}, {"name": "subSuite", "value": "TestEllaW<PERSON>sapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "77a207bf58510ea3.json", "parameterValues": []}