{"uid": "780ea6e88536084c", "name": "测试turn on do not disturb mode能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_do_not_disturb_mode.TestEllaTurnDoNotDisturbMode#test_turn_on_do_not_disturb_mode", "historyId": "e32881dd9d54414fa74d523ef27b055c", "time": {"start": 1754451004913, "stop": 1754451019638, "duration": 14725}, "description": "turn on do not disturb mode", "descriptionHtml": "<p>turn on do not disturb mode</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450992354, "stop": 1754451004912, "duration": 12558}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451004912, "stop": 1754451004912, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "turn on do not disturb mode", "status": "passed", "steps": [{"name": "执行命令: turn on do not disturb mode", "time": {"start": 1754451004913, "stop": 1754451019444, "duration": 14531}, "status": "passed", "steps": [{"name": "执行命令: turn on do not disturb mode", "time": {"start": 1754451004913, "stop": 1754451019246, "duration": 14333}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451019246, "stop": 1754451019444, "duration": 198}, "status": "passed", "steps": [], "attachments": [{"uid": "db3deff2b479391c", "name": "测试总结", "source": "db3deff2b479391c.txt", "type": "text/plain", "size": 223}, {"uid": "c0814d435c7600f7", "name": "test_completed", "source": "c0814d435c7600f7.png", "type": "image/png", "size": 516173}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451019444, "stop": 1754451019446, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754451019446, "stop": 1754451019446, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451019446, "stop": 1754451019637, "duration": 191}, "status": "passed", "steps": [], "attachments": [{"uid": "276fc18c393c5a5b", "name": "测试总结", "source": "276fc18c393c5a5b.txt", "type": "text/plain", "size": 223}, {"uid": "f7c359fe491bd056", "name": "test_completed", "source": "f7c359fe491bd056.png", "type": "image/png", "size": 516031}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "fed88b07c385a115", "name": "stdout", "source": "fed88b07c385a115.txt", "type": "text/plain", "size": 12103}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451019638, "stop": 1754451019638, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451019639, "stop": 1754451021008, "duration": 1369}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_do_not_disturb_mode"}, {"name": "subSuite", "value": "TestEllaTurnDoNotDisturbMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_do_not_disturb_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "588cc2abfa696660", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Do Not Disturb is turned on now']\nassert False", "time": {"start": 1754401159628, "stop": 1754401171063, "duration": 11435}}], "categories": [], "tags": ["smoke"]}, "source": "780ea6e88536084c.json", "parameterValues": []}