{"uid": "784ae7c2d07c1340", "name": "测试take notes on how to build a treehouse能正常执行", "fullName": "testcases.test_ella.dialogue.test_take_notes_on_how_to_build_a_treehouse.TestEllaTakeNotesHowBuildTreehouse#test_take_notes_on_how_to_build_a_treehouse", "historyId": "772728b3468560788490a3673352724d", "time": {"start": 1754399020354, "stop": 1754399033277, "duration": 12923}, "description": "take notes on how to build a treehouse", "descriptionHtml": "<p>take notes on how to build a treehouse</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399007671, "stop": 1754399020352, "duration": 12681}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399020352, "stop": 1754399020353, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "take notes on how to build a treehouse", "status": "passed", "steps": [{"name": "执行命令: take notes on how to build a treehouse", "time": {"start": 1754399020354, "stop": 1754399032970, "duration": 12616}, "status": "passed", "steps": [{"name": "执行命令: take notes on how to build a treehouse", "time": {"start": 1754399020354, "stop": 1754399032687, "duration": 12333}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399032687, "stop": 1754399032967, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "f1fb5d72cf154765", "name": "测试总结", "source": "f1fb5d72cf154765.txt", "type": "text/plain", "size": 234}, {"uid": "1b8b6e7feb9eb253", "name": "test_completed", "source": "1b8b6e7feb9eb253.png", "type": "image/png", "size": 502262}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399032970, "stop": 1754399032978, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399032978, "stop": 1754399033276, "duration": 298}, "status": "passed", "steps": [], "attachments": [{"uid": "4efce7f9a0281ae1", "name": "测试总结", "source": "4efce7f9a0281ae1.txt", "type": "text/plain", "size": 234}, {"uid": "b92a6f9dba46d576", "name": "test_completed", "source": "b92a6f9dba46d576.png", "type": "image/png", "size": 502715}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "2e4ab2dfc8bacc13", "name": "stdout", "source": "2e4ab2dfc8bacc13.txt", "type": "text/plain", "size": 11425}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399033278, "stop": 1754399033278, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399033281, "stop": 1754399034565, "duration": 1284}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_notes_on_how_to_build_a_treehouse"}, {"name": "subSuite", "value": "TestEllaTakeNotesHowBuildTreehouse"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_notes_on_how_to_build_a_treehouse"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "784ae7c2d07c1340.json", "parameterValues": []}