{"uid": "78e2205d4e4f206c", "name": "stop  screen recording能正常执行", "fullName": "testcases.test_ella.system_coupling.test_start_record.TestEllaStartRecord#test_stop_screen_recording", "historyId": "f0ba0ad0a7e0160d42d963e5a0186d00", "time": {"start": 1754450199489, "stop": 1754450218292, "duration": 18803}, "description": "stop  screen recording", "descriptionHtml": "<p>stop  screen recording</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450186557, "stop": 1754450199489, "duration": 12932}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450199489, "stop": 1754450199489, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "stop  screen recording", "status": "passed", "steps": [{"name": "执行命令: stop screen recording", "time": {"start": 1754450199489, "stop": 1754450218065, "duration": 18576}, "status": "passed", "steps": [{"name": "执行命令: stop screen recording", "time": {"start": 1754450199489, "stop": 1754450217833, "duration": 18344}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450217833, "stop": 1754450218065, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "c59209ab9da8ab6a", "name": "测试总结", "source": "c59209ab9da8ab6a.txt", "type": "text/plain", "size": 191}, {"uid": "4aaadceeae1409b2", "name": "test_completed", "source": "4aaadceeae1409b2.png", "type": "image/png", "size": 604342}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450218065, "stop": 1754450218066, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754450218066, "stop": 1754450218066, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证文件存在", "time": {"start": 1754450218066, "stop": 1754450218066, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450218066, "stop": 1754450218292, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "460cd012cdf87a75", "name": "测试总结", "source": "460cd012cdf87a75.txt", "type": "text/plain", "size": 191}, {"uid": "6b2b8c025ab8cfd", "name": "test_completed", "source": "6b2b8c025ab8cfd.png", "type": "image/png", "size": 604342}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "7d2423cfded04bcf", "name": "stdout", "source": "7d2423cfded04bcf.txt", "type": "text/plain", "size": 12908}], "parameters": [], "attachmentStep": false, "stepsCount": 7, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450218293, "stop": 1754450218293, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450218294, "stop": 1754450219642, "duration": 1348}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_start_record"}, {"name": "subSuite", "value": "TestEllaStartRecord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_start_record"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "98eeabad24fd12f3", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording finished']\nassert False", "time": {"start": 1754400375681, "stop": 1754400391399, "duration": 15718}}], "categories": [], "tags": ["smoke"]}, "source": "78e2205d4e4f206c.json", "parameterValues": []}