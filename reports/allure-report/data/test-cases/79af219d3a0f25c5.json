{"uid": "79af219d3a0f25c5", "name": "测试take a note on how to build a treehouse能正常执行", "fullName": "testcases.test_ella.dialogue.test_take_a_note_on_how_to_build_a_treehouse.TestEllaTakeNoteHowBuildTreehouse#test_take_a_note_on_how_to_build_a_treehouse", "historyId": "c45aa63628fe12b375ba7e65c39d93b1", "time": {"start": 1754398993585, "stop": 1754399006413, "duration": 12828}, "description": "take a note on how to build a treehouse", "descriptionHtml": "<p>take a note on how to build a treehouse</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398980484, "stop": 1754398993584, "duration": 13100}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398993584, "stop": 1754398993584, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "take a note on how to build a treehouse", "status": "passed", "steps": [{"name": "执行命令: take a note on how to build a treehouse", "time": {"start": 1754398993586, "stop": 1754399006117, "duration": 12531}, "status": "passed", "steps": [{"name": "执行命令: take a note on how to build a treehouse", "time": {"start": 1754398993586, "stop": 1754399005860, "duration": 12274}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399005860, "stop": 1754399006117, "duration": 257}, "status": "passed", "steps": [], "attachments": [{"uid": "94c8855060f89f73", "name": "测试总结", "source": "94c8855060f89f73.txt", "type": "text/plain", "size": 236}, {"uid": "17f3aad1a20e2ab0", "name": "test_completed", "source": "17f3aad1a20e2ab0.png", "type": "image/png", "size": 506604}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399006117, "stop": 1754399006121, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399006121, "stop": 1754399006410, "duration": 289}, "status": "passed", "steps": [], "attachments": [{"uid": "b8c66e1abec601ae", "name": "测试总结", "source": "b8c66e1abec601ae.txt", "type": "text/plain", "size": 236}, {"uid": "43ff868082408519", "name": "test_completed", "source": "43ff868082408519.png", "type": "image/png", "size": 507049}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "55d0cb8af9d9cfa9", "name": "stdout", "source": "55d0cb8af9d9cfa9.txt", "type": "text/plain", "size": 11432}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399006415, "stop": 1754399006415, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399006417, "stop": 1754399007666, "duration": 1249}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_a_note_on_how_to_build_a_treehouse"}, {"name": "subSuite", "value": "TestEllaTakeNoteHowBuildTreehouse"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_a_note_on_how_to_build_a_treehouse"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "79af219d3a0f25c5.json", "parameterValues": []}