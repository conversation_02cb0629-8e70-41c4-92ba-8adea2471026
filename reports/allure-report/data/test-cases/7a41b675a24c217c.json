{"uid": "7a41b675a24c217c", "name": "测试disable running lock返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_running_lock.TestEllaDisableRunningLock#test_disable_running_lock", "historyId": "bb51fd67dac102de95e755be72996bd1", "time": {"start": 1754452385420, "stop": 1754452400847, "duration": 15427}, "description": "验证disable running lock指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable running lock指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452372786, "stop": 1754452385419, "duration": 12633}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452385419, "stop": 1754452385419, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证disable running lock指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable running lock", "time": {"start": 1754452385420, "stop": 1754452400624, "duration": 15204}, "status": "passed", "steps": [{"name": "执行命令: disable running lock", "time": {"start": 1754452385420, "stop": 1754452400426, "duration": 15006}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452400426, "stop": 1754452400624, "duration": 198}, "status": "passed", "steps": [], "attachments": [{"uid": "d112489f51941f3e", "name": "测试总结", "source": "d112489f51941f3e.txt", "type": "text/plain", "size": 227}, {"uid": "fe56f2662e3d9f11", "name": "test_completed", "source": "fe56f2662e3d9f11.png", "type": "image/png", "size": 484125}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452400624, "stop": 1754452400625, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452400625, "stop": 1754452400847, "duration": 222}, "status": "passed", "steps": [], "attachments": [{"uid": "a720abbced71d0e3", "name": "测试总结", "source": "a720abbced71d0e3.txt", "type": "text/plain", "size": 227}, {"uid": "e5eafcfecda235ed", "name": "test_completed", "source": "e5eafcfecda235ed.png", "type": "image/png", "size": 484714}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "b69df75db0ff0724", "name": "stdout", "source": "b69df75db0ff0724.txt", "type": "text/plain", "size": 11738}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452400848, "stop": 1754452400848, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452400849, "stop": 1754452402151, "duration": 1302}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_running_lock"}, {"name": "subSuite", "value": "TestEllaDisableRunningLock"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_running_lock"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "970dcb3f0e912a2", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402418873, "stop": 1754402436211, "duration": 17338}}], "categories": [], "tags": ["smoke"]}, "source": "7a41b675a24c217c.json", "parameterValues": []}