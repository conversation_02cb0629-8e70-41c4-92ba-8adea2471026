{"uid": "7a434d777ce8aa77", "name": "测试next channel能正常执行", "fullName": "testcases.test_ella.component_coupling.test_next_channel.TestEllaNextChannel#test_next_channel", "historyId": "1d15cba90ae0426fa12e3218f1c542a6", "time": {"start": 1754446786785, "stop": 1754446800286, "duration": 13501}, "description": "next channel", "descriptionHtml": "<p>next channel</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754446774273, "stop": 1754446786783, "duration": 12510}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754446786783, "stop": 1754446786783, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "next channel", "status": "passed", "steps": [{"name": "执行命令: next channel", "time": {"start": 1754446786785, "stop": 1754446800129, "duration": 13344}, "status": "passed", "steps": [{"name": "执行命令: next channel", "time": {"start": 1754446786785, "stop": 1754446799929, "duration": 13144}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446799929, "stop": 1754446800129, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "832c91fc03ae0ccc", "name": "测试总结", "source": "832c91fc03ae0ccc.txt", "type": "text/plain", "size": 204}, {"uid": "b4662e78e8c1319d", "name": "test_completed", "source": "b4662e78e8c1319d.png", "type": "image/png", "size": 627196}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754446800129, "stop": 1754446800131, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446800131, "stop": 1754446800286, "duration": 155}, "status": "passed", "steps": [], "attachments": [{"uid": "6839bdad4f125efc", "name": "测试总结", "source": "6839bdad4f125efc.txt", "type": "text/plain", "size": 204}, {"uid": "bce71dbd55759f93", "name": "test_completed", "source": "bce71dbd55759f93.png", "type": "image/png", "size": 626673}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "978526fe8eaeab02", "name": "stdout", "source": "978526fe8eaeab02.txt", "type": "text/plain", "size": 11249}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754446800287, "stop": 1754446801603, "duration": 1316}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754446800287, "stop": 1754446800287, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_next_channel"}, {"name": "subSuite", "value": "TestEllaNextChannel"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_next_channel"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "a34c1115fd12a8e9", "status": "passed", "time": {"start": 1754397094717, "stop": 1754397107416, "duration": 12699}}], "categories": [], "tags": ["smoke"]}, "source": "7a434d777ce8aa77.json", "parameterValues": []}