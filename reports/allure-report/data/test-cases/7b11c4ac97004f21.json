{"uid": "7b11c4ac97004f21", "name": "测试open flashlight", "fullName": "testcases.test_ella.system_coupling.test_open_flashlight.TestEllaCommandConcise#test_open_flashlight", "historyId": "66496441d7401e453a580b4a8c23d111", "time": {"start": 1754449948163, "stop": 1754449964590, "duration": 16427}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449935238, "stop": 1754449948162, "duration": 12924}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449948162, "stop": 1754449948162, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "passed", "steps": [{"name": "执行命令: open flashlight", "time": {"start": 1754449948163, "stop": 1754449964398, "duration": 16235}, "status": "passed", "steps": [{"name": "执行命令: open flashlight", "time": {"start": 1754449948163, "stop": 1754449964183, "duration": 16020}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449964183, "stop": 1754449964397, "duration": 214}, "status": "passed", "steps": [], "attachments": [{"uid": "7b552b74ba047486", "name": "测试总结", "source": "7b552b74ba047486.txt", "type": "text/plain", "size": 191}, {"uid": "6c17432fc097093a", "name": "test_completed", "source": "6c17432fc097093a.png", "type": "image/png", "size": 523728}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含Done", "time": {"start": 1754449964398, "stop": 1754449964399, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证flashlight已打开", "time": {"start": 1754449964399, "stop": 1754449964399, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449964399, "stop": 1754449964590, "duration": 191}, "status": "passed", "steps": [], "attachments": [{"uid": "b15063f4ca10ebc0", "name": "测试总结", "source": "b15063f4ca10ebc0.txt", "type": "text/plain", "size": 191}, {"uid": "37c9ee23093661d4", "name": "test_completed", "source": "37c9ee23093661d4.png", "type": "image/png", "size": 523897}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "cb81b79fd655eae0", "name": "stdout", "source": "cb81b79fd655eae0.txt", "type": "text/plain", "size": 11842}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449964590, "stop": 1754449964590, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449964592, "stop": 1754449966036, "duration": 1444}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_open_flashlight"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_open_flashlight"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "63080b8a9b4f0272", "status": "passed", "time": {"start": 1754400125863, "stop": 1754400139434, "duration": 13571}}], "categories": [], "tags": ["smoke"]}, "source": "7b11c4ac97004f21.json", "parameterValues": []}