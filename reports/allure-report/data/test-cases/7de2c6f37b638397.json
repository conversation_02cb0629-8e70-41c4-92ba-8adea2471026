{"uid": "7de2c6f37b638397", "name": "测试take a selfie能正常执行", "fullName": "testcases.test_ella.system_coupling.test_take_a_selfie.TestEllaTakeSelfie#test_take_a_selfie", "historyId": "6c46a38570672e3c21f37ef82690d639", "time": {"start": 1754450816757, "stop": 1754450847988, "duration": 31231}, "description": "take a selfie", "descriptionHtml": "<p>take a selfie</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450803890, "stop": 1754450816756, "duration": 12866}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450816756, "stop": 1754450816756, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "take a selfie", "status": "passed", "steps": [{"name": "执行命令: take a selfie", "time": {"start": 1754450816757, "stop": 1754450847783, "duration": 31026}, "status": "passed", "steps": [{"name": "执行命令: take a selfie", "time": {"start": 1754450816757, "stop": 1754450847545, "duration": 30788}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450847545, "stop": 1754450847783, "duration": 238}, "status": "passed", "steps": [], "attachments": [{"uid": "55ba7f3b8ce9e8db", "name": "测试总结", "source": "55ba7f3b8ce9e8db.txt", "type": "text/plain", "size": 340}, {"uid": "75df23b3eec004c4", "name": "test_completed", "source": "75df23b3eec004c4.png", "type": "image/png", "size": 584780}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证应用已打开", "time": {"start": 1754450847783, "stop": 1754450847783, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证文件存在", "time": {"start": 1754450847783, "stop": 1754450847783, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450847783, "stop": 1754450847987, "duration": 204}, "status": "passed", "steps": [], "attachments": [{"uid": "e5834a9107bbec4a", "name": "测试总结", "source": "e5834a9107bbec4a.txt", "type": "text/plain", "size": 340}, {"uid": "729a51876a89506d", "name": "test_completed", "source": "729a51876a89506d.png", "type": "image/png", "size": 585641}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3c6a81091253c67d", "name": "stdout", "source": "3c6a81091253c67d.txt", "type": "text/plain", "size": 15019}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450847988, "stop": 1754450847988, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450847990, "stop": 1754450849386, "duration": 1396}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_take_a_selfie"}, {"name": "subSuite", "value": "TestEllaTakeSelfie"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_take_a_selfie"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "779b0038aeae73fb", "status": "passed", "time": {"start": 1754400970403, "stop": 1754400998323, "duration": 27920}}], "categories": [], "tags": ["smoke"]}, "source": "7de2c6f37b638397.json", "parameterValues": []}