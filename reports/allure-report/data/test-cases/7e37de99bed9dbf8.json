{"uid": "7e37de99bed9dbf8", "name": "测试what's the weather like in shanghai today能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_s_the_weather_like_in_shanghai_today.TestEllaWhatSWeatherLikeShanghaiToday#test_what_s_the_weather_like_in_shanghai_today", "historyId": "b60d4c80cd1df80873da3fea78736e6a", "time": {"start": 1754448975280, "stop": 1754448995678, "duration": 20398}, "description": "what's the weather like in shanghai today", "descriptionHtml": "<p>what's the weather like in shanghai today</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448962734, "stop": 1754448975279, "duration": 12545}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448975279, "stop": 1754448975279, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "what's the weather like in shanghai today", "status": "passed", "steps": [{"name": "执行命令: what's the weather like in shanghai today", "time": {"start": 1754448975280, "stop": 1754448995463, "duration": 20183}, "status": "passed", "steps": [{"name": "执行命令: what's the weather like in shanghai today", "time": {"start": 1754448975280, "stop": 1754448995264, "duration": 19984}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448995264, "stop": 1754448995462, "duration": 198}, "status": "passed", "steps": [], "attachments": [{"uid": "d674a33d45cd718f", "name": "测试总结", "source": "d674a33d45cd718f.txt", "type": "text/plain", "size": 276}, {"uid": "860183669dd96df3", "name": "test_completed", "source": "860183669dd96df3.png", "type": "image/png", "size": 539774}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448995463, "stop": 1754448995464, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448995464, "stop": 1754448995678, "duration": 214}, "status": "passed", "steps": [], "attachments": [{"uid": "e54cfa791712aa3a", "name": "测试总结", "source": "e54cfa791712aa3a.txt", "type": "text/plain", "size": 276}, {"uid": "9f0522be83ad8994", "name": "test_completed", "source": "9f0522be83ad8994.png", "type": "image/png", "size": 540439}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "db8825e809834ddf", "name": "stdout", "source": "db8825e809834ddf.txt", "type": "text/plain", "size": 11960}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448995679, "stop": 1754448995679, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448995680, "stop": 1754448997077, "duration": 1397}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_s_the_weather_like_in_shanghai_today"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherLikeShanghaiToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_s_the_weather_like_in_shanghai_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "2cb8ed69ebde6522", "status": "passed", "time": {"start": 1754399169453, "stop": 1754399189388, "duration": 19935}}], "categories": [], "tags": ["smoke"]}, "source": "7e37de99bed9dbf8.json", "parameterValues": []}