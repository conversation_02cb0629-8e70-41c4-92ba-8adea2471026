{"uid": "7eb67937aa9b6276", "name": "测试make a call能正常执行", "fullName": "testcases.test_ella.dialogue.test_make_a_call.TestEllaMakeCall#test_make_a_call", "historyId": "2428ad915810150c12838b88ee13f49c", "time": {"start": 1754398561070, "stop": 1754398582929, "duration": 21859}, "description": "make a call", "descriptionHtml": "<p>make a call</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398548205, "stop": 1754398561069, "duration": 12864}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398561070, "stop": 1754398561070, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "make a call", "status": "passed", "steps": [{"name": "执行命令: make a call", "time": {"start": 1754398561070, "stop": 1754398582666, "duration": 21596}, "status": "passed", "steps": [{"name": "执行命令: make a call", "time": {"start": 1754398561070, "stop": 1754398582405, "duration": 21335}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398582405, "stop": 1754398582665, "duration": 260}, "status": "passed", "steps": [], "attachments": [{"uid": "2f865d80fb1a39d1", "name": "测试总结", "source": "2f865d80fb1a39d1.txt", "type": "text/plain", "size": 173}, {"uid": "7525bdac4bccf014", "name": "test_completed", "source": "7525bdac4bccf014.png", "type": "image/png", "size": 537628}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398582666, "stop": 1754398582671, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398582671, "stop": 1754398582928, "duration": 257}, "status": "passed", "steps": [], "attachments": [{"uid": "6fe987c122a78f7e", "name": "测试总结", "source": "6fe987c122a78f7e.txt", "type": "text/plain", "size": 173}, {"uid": "9584a93189ebfe93", "name": "test_completed", "source": "9584a93189ebfe93.png", "type": "image/png", "size": 537529}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "72f8022d6bd25f7a", "name": "stdout", "source": "72f8022d6bd25f7a.txt", "type": "text/plain", "size": 11515}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398582931, "stop": 1754398582931, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398582933, "stop": 1754398584212, "duration": 1279}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_make_a_call"}, {"name": "subSuite", "value": "TestEllaMakeCall"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_make_a_call"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "7eb67937aa9b6276.json", "parameterValues": []}