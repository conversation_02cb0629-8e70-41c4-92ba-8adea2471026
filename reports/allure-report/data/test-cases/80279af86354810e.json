{"uid": "80279af86354810e", "name": "测试disable magic voice changer能正常执行", "fullName": "testcases.test_ella.dialogue.test_disable_magic_voice_changer.TestEllaDisableMagicVoiceChanger#test_disable_magic_voice_changer", "historyId": "d6d97ebce763bf8ead601650bfb2383c", "time": {"start": 1754447808985, "stop": 1754447822724, "duration": 13739}, "description": "disable magic voice changer", "descriptionHtml": "<p>disable magic voice changer</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447796331, "stop": 1754447808983, "duration": 12652}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447808983, "stop": 1754447808984, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "disable magic voice changer", "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "time": {"start": 1754447808985, "stop": 1754447822510, "duration": 13525}, "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "time": {"start": 1754447808985, "stop": 1754447822315, "duration": 13330}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447822315, "stop": 1754447822510, "duration": 195}, "status": "passed", "steps": [], "attachments": [{"uid": "d32a492b6f15e03c", "name": "测试总结", "source": "d32a492b6f15e03c.txt", "type": "text/plain", "size": 239}, {"uid": "34a84e7f71f1e301", "name": "test_completed", "source": "34a84e7f71f1e301.png", "type": "image/png", "size": 634198}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1754447822510, "stop": 1754447822512, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447822512, "stop": 1754447822723, "duration": 211}, "status": "passed", "steps": [], "attachments": [{"uid": "cb850fb9fd0fd183", "name": "测试总结", "source": "cb850fb9fd0fd183.txt", "type": "text/plain", "size": 239}, {"uid": "67254aa63305796b", "name": "test_completed", "source": "67254aa63305796b.png", "type": "image/png", "size": 634245}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "6ea41082c32c7de7", "name": "stdout", "source": "6ea41082c32c7de7.txt", "type": "text/plain", "size": 11501}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447822724, "stop": 1754447822724, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447822724, "stop": 1754447824099, "duration": 1375}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_disable_magic_voice_changer"}, {"name": "subSuite", "value": "TestEllaDisableMagicVoiceChanger"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_disable_magic_voice_changer"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "12c8b31eae81770c", "status": "passed", "time": {"start": 1754398074697, "stop": 1754398087507, "duration": 12810}}], "categories": [], "tags": ["smoke"]}, "source": "80279af86354810e.json", "parameterValues": []}