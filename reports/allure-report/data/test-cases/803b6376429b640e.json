{"uid": "803b6376429b640e", "name": "测试start record能正常执行", "fullName": "testcases.test_ella.system_coupling.test_start_record.TestEllaStartRecord#test_start_record", "historyId": "7acb737855a3a3110ed556a3e5fe1256", "time": {"start": 1754450167254, "stop": 1754450185189, "duration": 17935}, "description": "start record", "descriptionHtml": "<p>start record</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450154577, "stop": 1754450167253, "duration": 12676}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450167253, "stop": 1754450167253, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "start record", "status": "passed", "steps": [{"name": "执行命令: start record", "time": {"start": 1754450167254, "stop": 1754450184977, "duration": 17723}, "status": "passed", "steps": [{"name": "执行命令: start record", "time": {"start": 1754450167254, "stop": 1754450184746, "duration": 17492}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450184747, "stop": 1754450184977, "duration": 230}, "status": "passed", "steps": [], "attachments": [{"uid": "df88a24456349ab7", "name": "测试总结", "source": "df88a24456349ab7.txt", "type": "text/plain", "size": 172}, {"uid": "4985bc3b01ebcbb8", "name": "test_completed", "source": "4985bc3b01ebcbb8.png", "type": "image/png", "size": 579156}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450184977, "stop": 1754450184978, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754450184978, "stop": 1754450184978, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450184978, "stop": 1754450185189, "duration": 211}, "status": "passed", "steps": [], "attachments": [{"uid": "943d09c8ef10ce76", "name": "测试总结", "source": "943d09c8ef10ce76.txt", "type": "text/plain", "size": 172}, {"uid": "f25c922f0037ec2c", "name": "test_completed", "source": "f25c922f0037ec2c.png", "type": "image/png", "size": 579649}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "985087aa70846f3f", "name": "stdout", "source": "985087aa70846f3f.txt", "type": "text/plain", "size": 11603}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450185190, "stop": 1754450185190, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450185191, "stop": 1754450186554, "duration": 1363}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_start_record"}, {"name": "subSuite", "value": "TestEllaStartRecord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_start_record"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "3903395be9c42062", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording started']\nassert False", "time": {"start": 1754400343590, "stop": 1754400360843, "duration": 17253}}], "categories": [], "tags": ["smoke"]}, "source": "803b6376429b640e.json", "parameterValues": []}