{"uid": "80bc5ab5763925a4", "name": "测试Enable Network Enhancement返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_network_enhancement.TestEllaEnableNetworkEnhancement#test_enable_network_enhancement", "historyId": "657acdf17dda1a11abf6946763f6ed52", "time": {"start": 1754452739108, "stop": 1754452753065, "duration": 13957}, "description": "验证Enable Network Enhancement指令返回预期的不支持响应", "descriptionHtml": "<p>验证Enable Network Enhancement指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452727236, "stop": 1754452739107, "duration": 11871}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452739108, "stop": 1754452739108, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证Enable Network Enhancement指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: Enable Network Enhancement", "time": {"start": 1754452739108, "stop": 1754452752892, "duration": 13784}, "status": "passed", "steps": [{"name": "执行命令: Enable Network Enhancement", "time": {"start": 1754452739108, "stop": 1754452752684, "duration": 13576}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452752684, "stop": 1754452752892, "duration": 208}, "status": "passed", "steps": [], "attachments": [{"uid": "2d2711f95ce1f615", "name": "测试总结", "source": "2d2711f95ce1f615.txt", "type": "text/plain", "size": 246}, {"uid": "54e496974bf8cf9", "name": "test_completed", "source": "54e496974bf8cf9.png", "type": "image/png", "size": 513841}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452752892, "stop": 1754452752893, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452752893, "stop": 1754452753065, "duration": 172}, "status": "passed", "steps": [], "attachments": [{"uid": "80000c66c7f78701", "name": "测试总结", "source": "80000c66c7f78701.txt", "type": "text/plain", "size": 246}, {"uid": "a1f514d543b51d4c", "name": "test_completed", "source": "a1f514d543b51d4c.png", "type": "image/png", "size": 513927}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "9c89a057536e1f6f", "name": "stdout", "source": "9c89a057536e1f6f.txt", "type": "text/plain", "size": 11390}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452753066, "stop": 1754452753066, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452753067, "stop": 1754452754395, "duration": 1328}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_network_enhancement"}, {"name": "subSuite", "value": "TestEllaEnableNetworkEnhancement"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_network_enhancement"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "e1c42c9fcac6120d", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402771478, "stop": 1754402782822, "duration": 11344}}], "categories": [], "tags": ["smoke"]}, "source": "80bc5ab5763925a4.json", "parameterValues": []}