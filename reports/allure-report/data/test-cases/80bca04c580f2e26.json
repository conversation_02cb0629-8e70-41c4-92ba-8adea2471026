{"uid": "80bca04c580f2e26", "name": "测试whats the weather today能正常执行", "fullName": "testcases.test_ella.dialogue.test_whats_the_weather_today.TestEllaWhatsWeatherToday#test_whats_the_weather_today", "historyId": "acca7d0b06a28ad8e6ccfe3c35828ce1", "time": {"start": 1754449128369, "stop": 1754449149343, "duration": 20974}, "description": "whats the weather today", "descriptionHtml": "<p>whats the weather today</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449115668, "stop": 1754449128368, "duration": 12700}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449128368, "stop": 1754449128368, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "whats the weather today", "status": "passed", "steps": [{"name": "执行命令: whats the weather today", "time": {"start": 1754449128369, "stop": 1754449149121, "duration": 20752}, "status": "passed", "steps": [{"name": "执行命令: whats the weather today", "time": {"start": 1754449128369, "stop": 1754449148915, "duration": 20546}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449148915, "stop": 1754449149121, "duration": 206}, "status": "passed", "steps": [], "attachments": [{"uid": "3ef8ab3fa314b013", "name": "测试总结", "source": "3ef8ab3fa314b013.txt", "type": "text/plain", "size": 262}, {"uid": "ddda332c6c3f2c0a", "name": "test_completed", "source": "ddda332c6c3f2c0a.png", "type": "image/png", "size": 534949}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754449149121, "stop": 1754449149121, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449149121, "stop": 1754449149343, "duration": 222}, "status": "passed", "steps": [], "attachments": [{"uid": "7b9e7bc0ab952192", "name": "测试总结", "source": "7b9e7bc0ab952192.txt", "type": "text/plain", "size": 262}, {"uid": "ce92635b2dfffa73", "name": "test_completed", "source": "ce92635b2dfffa73.png", "type": "image/png", "size": 535183}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d8e270075b866a75", "name": "stdout", "source": "d8e270075b866a75.txt", "type": "text/plain", "size": 11833}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449149343, "stop": 1754449149344, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449149344, "stop": 1754449150735, "duration": 1391}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_whats_the_weather_today"}, {"name": "subSuite", "value": "TestEllaWhatsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_whats_the_weather_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "523730bcb360743f", "status": "passed", "time": {"start": 1754399319034, "stop": 1754399339460, "duration": 20426}}], "categories": [], "tags": ["smoke"]}, "source": "80bca04c580f2e26.json", "parameterValues": []}