{"uid": "81616d50f4291bda", "name": "测试turn down ring volume能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_down_ring_volume.TestEllaTurnDownRingVolume#test_turn_down_ring_volume", "historyId": "bf94eb080274f830f3097dd5adde1ed1", "time": {"start": 1754450891563, "stop": 1754450905225, "duration": 13662}, "description": "turn down ring volume", "descriptionHtml": "<p>turn down ring volume</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450879007, "stop": 1754450891562, "duration": 12555}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450891562, "stop": 1754450891562, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "turn down ring volume", "status": "passed", "steps": [{"name": "执行命令: turn down ring volume", "time": {"start": 1754450891563, "stop": 1754450905011, "duration": 13448}, "status": "passed", "steps": [{"name": "执行命令: turn down ring volume", "time": {"start": 1754450891563, "stop": 1754450904824, "duration": 13261}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450904824, "stop": 1754450905011, "duration": 187}, "status": "passed", "steps": [], "attachments": [{"uid": "7cccc8e85bf4bfe", "name": "测试总结", "source": "7cccc8e85bf4bfe.txt", "type": "text/plain", "size": 201}, {"uid": "d13897426d318e13", "name": "test_completed", "source": "d13897426d318e13.png", "type": "image/png", "size": 581741}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450905011, "stop": 1754450905014, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450905014, "stop": 1754450905224, "duration": 210}, "status": "passed", "steps": [], "attachments": [{"uid": "e3abfe023b73ae0", "name": "测试总结", "source": "e3abfe023b73ae0.txt", "type": "text/plain", "size": 201}, {"uid": "4b3ad67149dea70b", "name": "test_completed", "source": "4b3ad67149dea70b.png", "type": "image/png", "size": 581457}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "b51c74ef2666383c", "name": "stdout", "source": "b51c74ef2666383c.txt", "type": "text/plain", "size": 11260}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450905225, "stop": 1754450905225, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450905227, "stop": 1754450906617, "duration": 1390}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_down_ring_volume"}, {"name": "subSuite", "value": "TestEllaTurnDownRingVolume"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_down_ring_volume"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "9afebd272e1deed4", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Ringtone volume has been turned down.']\nassert False", "time": {"start": 1754401051937, "stop": 1754401063083, "duration": 11146}}], "categories": [], "tags": ["smoke"]}, "source": "81616d50f4291bda.json", "parameterValues": []}