{"uid": "81941d3cfb50de0e", "name": "测试how's the weather today in shanghai能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_s_the_weather_today_in_shanghai.TestEllaWhatSWeatherLikeShanghaiToday#test_what_s_the_weather_like_in_shanghai_today", "historyId": "bd4d204a449f3a4013b03af9a9101446", "time": {"start": 1754398352397, "stop": 1754398372537, "duration": 20140}, "description": "how's the weather today in shanghai", "descriptionHtml": "<p>how's the weather today in shanghai</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398339635, "stop": 1754398352396, "duration": 12761}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398352397, "stop": 1754398352397, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "how's the weather today in shanghai", "status": "passed", "steps": [{"name": "执行命令: how's the weather today in shanghai", "time": {"start": 1754398352398, "stop": 1754398372259, "duration": 19861}, "status": "passed", "steps": [{"name": "执行命令: how's the weather today in shanghai", "time": {"start": 1754398352398, "stop": 1754398371992, "duration": 19594}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398371992, "stop": 1754398372258, "duration": 266}, "status": "passed", "steps": [], "attachments": [{"uid": "45a7f91dd5c0c462", "name": "测试总结", "source": "45a7f91dd5c0c462.txt", "type": "text/plain", "size": 269}, {"uid": "fca1dad51f361481", "name": "test_completed", "source": "fca1dad51f361481.png", "type": "image/png", "size": 502174}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398372259, "stop": 1754398372262, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398372262, "stop": 1754398372536, "duration": 274}, "status": "passed", "steps": [], "attachments": [{"uid": "1f4dc0ce8f498399", "name": "测试总结", "source": "1f4dc0ce8f498399.txt", "type": "text/plain", "size": 269}, {"uid": "516a5e1966498136", "name": "test_completed", "source": "516a5e1966498136.png", "type": "image/png", "size": 502174}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "f9a9b4db6916f187", "name": "stdout", "source": "f9a9b4db6916f187.txt", "type": "text/plain", "size": 11914}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398372538, "stop": 1754398372538, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398372539, "stop": 1754398373782, "duration": 1243}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_s_the_weather_today_in_shanghai"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherLikeShanghaiToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_s_the_weather_today_in_shanghai"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "81941d3cfb50de0e.json", "parameterValues": []}