{"uid": "8251282b801233c8", "name": "测试how's the weather today?返回正确的不支持响应", "fullName": "testcases.test_ella.dialogue.test_how_s_the_weather_today.TestEllaHowSWeatherToday#test_how_s_the_weather_today", "historyId": "f2f6762c5ec83e110ace25b47e3112d5", "time": {"start": 1754398318149, "stop": 1754398338348, "duration": 20199}, "description": "验证how's the weather today?指令返回预期的不支持响应", "descriptionHtml": "<p>验证how's the weather today?指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398305161, "stop": 1754398318147, "duration": 12986}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398318147, "stop": 1754398318147, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证how's the weather today?指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: how's the weather today?", "time": {"start": 1754398318149, "stop": 1754398338053, "duration": 19904}, "status": "passed", "steps": [{"name": "执行命令: how's the weather today?", "time": {"start": 1754398318149, "stop": 1754398337759, "duration": 19610}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398337759, "stop": 1754398338052, "duration": 293}, "status": "passed", "steps": [], "attachments": [{"uid": "c13777c1066aca3a", "name": "测试总结", "source": "c13777c1066aca3a.txt", "type": "text/plain", "size": 255}, {"uid": "70ebd95a0d09bb3c", "name": "test_completed", "source": "70ebd95a0d09bb3c.png", "type": "image/png", "size": 490753}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754398338053, "stop": 1754398338058, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398338058, "stop": 1754398338346, "duration": 288}, "status": "passed", "steps": [], "attachments": [{"uid": "c62784dd8fe269fc", "name": "测试总结", "source": "c62784dd8fe269fc.txt", "type": "text/plain", "size": 255}, {"uid": "6de146506f4642f9", "name": "test_completed", "source": "6de146506f4642f9.png", "type": "image/png", "size": 489567}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "fb2cf4ec0d115f4c", "name": "stdout", "source": "fb2cf4ec0d115f4c.txt", "type": "text/plain", "size": 11932}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398338349, "stop": 1754398338349, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398338351, "stop": 1754398339632, "duration": 1281}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_s_the_weather_today"}, {"name": "subSuite", "value": "TestEllaHowSWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_s_the_weather_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "8251282b801233c8.json", "parameterValues": []}