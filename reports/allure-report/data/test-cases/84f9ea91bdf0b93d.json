{"uid": "84f9ea91bdf0b93d", "name": "测试open bt", "fullName": "testcases.test_ella.system_coupling.test_open_bt.TestEllaOpenBluetooth#test_open_bt", "historyId": "9511be8e6426d5078713c6e78f3b02e3", "time": {"start": 1754449920126, "stop": 1754449933865, "duration": 13739}, "description": "测试open bt指令", "descriptionHtml": "<p>测试open bt指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449907625, "stop": 1754449920124, "duration": 12499}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449920124, "stop": 1754449920124, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试open bt指令", "status": "passed", "steps": [{"name": "执行命令: open bt", "time": {"start": 1754449920126, "stop": 1754449933678, "duration": 13552}, "status": "passed", "steps": [{"name": "执行命令: open bt", "time": {"start": 1754449920126, "stop": 1754449933477, "duration": 13351}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449933477, "stop": 1754449933677, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "e1b9cc3ddc549a54", "name": "测试总结", "source": "e1b9cc3ddc549a54.txt", "type": "text/plain", "size": 172}, {"uid": "fa3df8f81bcc3aeb", "name": "test_completed", "source": "fa3df8f81bcc3aeb.png", "type": "image/png", "size": 516589}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754449933678, "stop": 1754449933679, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证bluetooth已打开", "time": {"start": 1754449933679, "stop": 1754449933679, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449933679, "stop": 1754449933865, "duration": 186}, "status": "passed", "steps": [], "attachments": [{"uid": "1358294c9e4604a8", "name": "测试总结", "source": "1358294c9e4604a8.txt", "type": "text/plain", "size": 172}, {"uid": "20eb9a90033cc408", "name": "test_completed", "source": "20eb9a90033cc408.png", "type": "image/png", "size": 516155}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "2d242083393a666a", "name": "stdout", "source": "2d242083393a666a.txt", "type": "text/plain", "size": 12618}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449933866, "stop": 1754449933866, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449933867, "stop": 1754449935233, "duration": 1366}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_open_bt"}, {"name": "subSuite", "value": "TestEllaOpenBluetooth"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_open_bt"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "63d04327f0971163", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Bluetooth is turned on now']\nassert False", "time": {"start": 1754400098871, "stop": 1754400111246, "duration": 12375}}], "categories": [], "tags": ["smoke"]}, "source": "84f9ea91bdf0b93d.json", "parameterValues": []}