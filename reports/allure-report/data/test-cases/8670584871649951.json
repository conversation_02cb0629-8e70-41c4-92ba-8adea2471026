{"uid": "8670584871649951", "name": "测试jump to adaptive brightness settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_adaptive_brightness_settings.TestEllaJumpAdaptiveBrightnessSettings#test_jump_to_adaptive_brightness_settings", "historyId": "c796c03cca51cea23bdc87f3f9d6fa95", "time": {"start": 1754453101441, "stop": 1754453115702, "duration": 14261}, "description": "验证jump to adaptive brightness settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to adaptive brightness settings指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453089224, "stop": 1754453101440, "duration": 12216}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453101440, "stop": 1754453101440, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证jump to adaptive brightness settings指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: jump to adaptive brightness settings", "time": {"start": 1754453101441, "stop": 1754453115523, "duration": 14082}, "status": "passed", "steps": [{"name": "执行命令: jump to adaptive brightness settings", "time": {"start": 1754453101441, "stop": 1754453115293, "duration": 13852}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453115293, "stop": 1754453115522, "duration": 229}, "status": "passed", "steps": [], "attachments": [{"uid": "e8c8a40ff5369167", "name": "测试总结", "source": "e8c8a40ff5369167.txt", "type": "text/plain", "size": 261}, {"uid": "f87d4c6fffbccb62", "name": "test_completed", "source": "f87d4c6fffbccb62.png", "type": "image/png", "size": 530889}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453115523, "stop": 1754453115524, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453115524, "stop": 1754453115702, "duration": 178}, "status": "passed", "steps": [], "attachments": [{"uid": "953d21397a031320", "name": "测试总结", "source": "953d21397a031320.txt", "type": "text/plain", "size": 261}, {"uid": "ad1e6e8d17344224", "name": "test_completed", "source": "ad1e6e8d17344224.png", "type": "image/png", "size": 529790}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "25984812b6ec1858", "name": "stdout", "source": "25984812b6ec1858.txt", "type": "text/plain", "size": 12193}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453115703, "stop": 1754453115703, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453115704, "stop": 1754453117074, "duration": 1370}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_adaptive_brightness_settings"}, {"name": "subSuite", "value": "TestEllaJumpAdaptiveBrightnessSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_adaptive_brightness_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "5d3c0e1fecb3a3f1", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403115613, "stop": 1754403132332, "duration": 16719}}], "categories": [], "tags": ["smoke"]}, "source": "8670584871649951.json", "parameterValues": []}