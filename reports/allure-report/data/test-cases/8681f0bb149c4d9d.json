{"uid": "8681f0bb149c4d9d", "name": "测试switching charging speed能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_switching_charging_speed.TestEllaSwitchingChargingSpeed#test_switching_charging_speed", "historyId": "e8c3c7bb72cf538a9e89a7b790c5e689", "time": {"start": 1754455325004, "stop": 1754455338649, "duration": 13645}, "description": "switching charging speed", "descriptionHtml": "<p>switching charging speed</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455313082, "stop": 1754455325003, "duration": 11921}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455325003, "stop": 1754455325003, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "switching charging speed", "status": "passed", "steps": [{"name": "执行命令: switching charging speed", "time": {"start": 1754455325004, "stop": 1754455338477, "duration": 13473}, "status": "passed", "steps": [{"name": "执行命令: switching charging speed", "time": {"start": 1754455325004, "stop": 1754455338322, "duration": 13318}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455338322, "stop": 1754455338477, "duration": 155}, "status": "passed", "steps": [], "attachments": [{"uid": "bca2d85ea932104b", "name": "测试总结", "source": "bca2d85ea932104b.txt", "type": "text/plain", "size": 236}, {"uid": "ea33022633369294", "name": "test_completed", "source": "ea33022633369294.png", "type": "image/png", "size": 509870}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754455338477, "stop": 1754455338478, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455338478, "stop": 1754455338649, "duration": 171}, "status": "passed", "steps": [], "attachments": [{"uid": "ca8f041dc8664c7e", "name": "测试总结", "source": "ca8f041dc8664c7e.txt", "type": "text/plain", "size": 236}, {"uid": "a2a3bde99c3810f9", "name": "test_completed", "source": "a2a3bde99c3810f9.png", "type": "image/png", "size": 509257}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "f054b790cc000ef1", "name": "stdout", "source": "f054b790cc000ef1.txt", "type": "text/plain", "size": 11350}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455338650, "stop": 1754455338650, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455338651, "stop": 1754455340054, "duration": 1403}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switching_charging_speed"}, {"name": "subSuite", "value": "TestEllaSwitchingChargingSpeed"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switching_charging_speed"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "2ae612c65a28130b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405363731, "stop": 1754405379363, "duration": 15632}}], "categories": [], "tags": ["smoke"]}, "source": "8681f0bb149c4d9d.json", "parameterValues": []}