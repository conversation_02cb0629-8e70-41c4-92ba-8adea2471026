{"uid": "878c21bfb8fab771", "name": "测试open folax能正常执行", "fullName": "testcases.test_ella.component_coupling.test_open_folax.TestEllaCommandConcise#test_open_folax", "historyId": "9da64d3434f91a12d693ed9c71b62e87", "time": {"start": 1754397312634, "stop": 1754397325399, "duration": 12765}, "description": "open folax", "descriptionHtml": "<p>open folax</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397299546, "stop": 1754397312634, "duration": 13088}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397312634, "stop": 1754397312634, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "open folax", "status": "passed", "steps": [{"name": "执行命令: open folax", "time": {"start": 1754397312634, "stop": 1754397325087, "duration": 12453}, "status": "passed", "steps": [{"name": "执行命令: open folax", "time": {"start": 1754397312634, "stop": 1754397324818, "duration": 12184}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397324818, "stop": 1754397325085, "duration": 267}, "status": "passed", "steps": [], "attachments": [{"uid": "f7e6080efa565ee9", "name": "测试总结", "source": "f7e6080efa565ee9.txt", "type": "text/plain", "size": 147}, {"uid": "3fa05d970e756b72", "name": "test_completed", "source": "3fa05d970e756b72.png", "type": "image/png", "size": 583784}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1754397325087, "stop": 1754397325093, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397325093, "stop": 1754397325398, "duration": 305}, "status": "passed", "steps": [], "attachments": [{"uid": "a77db406ac79153", "name": "测试总结", "source": "a77db406ac79153.txt", "type": "text/plain", "size": 147}, {"uid": "80bf8623b849d34f", "name": "test_completed", "source": "80bf8623b849d34f.png", "type": "image/png", "size": 583823}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "cd56f3eaea36dbec", "name": "stdout", "source": "cd56f3eaea36dbec.txt", "type": "text/plain", "size": 11248}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397325401, "stop": 1754397325401, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397325405, "stop": 1754397326701, "duration": 1296}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_folax"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_folax"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "878c21bfb8fab771.json", "parameterValues": []}