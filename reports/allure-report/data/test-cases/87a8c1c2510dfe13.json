{"uid": "87a8c1c2510dfe13", "name": "测试switch to performance mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_switch_to_performance_mode.TestEllaSwitchPerformanceMode#test_switch_to_performance_mode", "historyId": "34f3c9cc9098f792051e7099b7a9fdc1", "time": {"start": 1754455268962, "stop": 1754455282666, "duration": 13704}, "description": "验证switch to performance mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证switch to performance mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455256885, "stop": 1754455268961, "duration": 12076}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455268961, "stop": 1754455268961, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证switch to performance mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: switch to performance mode", "time": {"start": 1754455268963, "stop": 1754455282459, "duration": 13496}, "status": "passed", "steps": [{"name": "执行命令: switch to performance mode", "time": {"start": 1754455268963, "stop": 1754455282265, "duration": 13302}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455282265, "stop": 1754455282459, "duration": 194}, "status": "passed", "steps": [], "attachments": [{"uid": "4a55c4f35d6e812d", "name": "测试总结", "source": "4a55c4f35d6e812d.txt", "type": "text/plain", "size": 247}, {"uid": "59c811ae6d6acc01", "name": "test_completed", "source": "59c811ae6d6acc01.png", "type": "image/png", "size": 505240}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455282459, "stop": 1754455282460, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455282460, "stop": 1754455282666, "duration": 206}, "status": "passed", "steps": [], "attachments": [{"uid": "ae161219caee573c", "name": "测试总结", "source": "ae161219caee573c.txt", "type": "text/plain", "size": 247}, {"uid": "81fd0890998ecc47", "name": "test_completed", "source": "81fd0890998ecc47.png", "type": "image/png", "size": 505090}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d8ba3b2a90d58d71", "name": "stdout", "source": "d8ba3b2a90d58d71.txt", "type": "text/plain", "size": 11387}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455282666, "stop": 1754455282666, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455282667, "stop": 1754455284076, "duration": 1409}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switch_to_performance_mode"}, {"name": "subSuite", "value": "TestEllaSwitchPerformanceMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switch_to_performance_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "7cadc9bc33f94b5e", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405312469, "stop": 1754405323635, "duration": 11166}}], "categories": [], "tags": ["smoke"]}, "source": "87a8c1c2510dfe13.json", "parameterValues": []}