{"uid": "88ea7dfcbacf2d15", "name": "测试set app auto rotate返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_app_auto_rotate.TestEllaSetAppAutoRotate#test_set_app_auto_rotate", "historyId": "eda16efd838471b84f33f12ec91662c9", "time": {"start": 1754454179151, "stop": 1754454193174, "duration": 14023}, "description": "验证set app auto rotate指令返回预期的不支持响应", "descriptionHtml": "<p>验证set app auto rotate指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454166411, "stop": 1754454179150, "duration": 12739}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454179150, "stop": 1754454179150, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set app auto rotate指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set app auto rotate", "time": {"start": 1754454179151, "stop": 1754454192980, "duration": 13829}, "status": "passed", "steps": [{"name": "执行命令: set app auto rotate", "time": {"start": 1754454179151, "stop": 1754454192753, "duration": 13602}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454192753, "stop": 1754454192979, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "739df53d11ff3c45", "name": "测试总结", "source": "739df53d11ff3c45.txt", "type": "text/plain", "size": 227}, {"uid": "35d08b49164b1cd4", "name": "test_completed", "source": "35d08b49164b1cd4.png", "type": "image/png", "size": 478720}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454192980, "stop": 1754454192982, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454192982, "stop": 1754454193174, "duration": 192}, "status": "passed", "steps": [], "attachments": [{"uid": "fffb05fb01b658ef", "name": "测试总结", "source": "fffb05fb01b658ef.txt", "type": "text/plain", "size": 227}, {"uid": "cf1458da74509bcd", "name": "test_completed", "source": "cf1458da74509bcd.png", "type": "image/png", "size": 478573}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "c61ee87fcf91fbc1", "name": "stdout", "source": "c61ee87fcf91fbc1.txt", "type": "text/plain", "size": 11296}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454193175, "stop": 1754454193175, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454193175, "stop": 1754454194526, "duration": 1351}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_app_auto_rotate"}, {"name": "subSuite", "value": "TestEllaSetAppAutoRotate"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_app_auto_rotate"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "25b8505da9a1a699", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404187688, "stop": 1754404198725, "duration": 11037}}], "categories": [], "tags": ["smoke"]}, "source": "88ea7dfcbacf2d15.json", "parameterValues": []}