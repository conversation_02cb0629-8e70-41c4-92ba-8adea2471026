{"uid": "8a51f504d5b44159", "name": "测试play music", "fullName": "testcases.test_ella.component_coupling.test_play_music.TestEllaOpenVisha#test_play_music", "historyId": "148d3ba280bfe2b41b8464beec5f6763", "time": {"start": 1754447235725, "stop": 1754447258400, "duration": 22675}, "description": "测试play music指令", "descriptionHtml": "<p>测试play music指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447223365, "stop": 1754447235724, "duration": 12359}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447235724, "stop": 1754447235724, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play music指令", "status": "passed", "steps": [{"name": "执行命令: play music", "time": {"start": 1754447235725, "stop": 1754447258174, "duration": 22449}, "status": "passed", "steps": [{"name": "执行命令: play music", "time": {"start": 1754447235725, "stop": 1754447257978, "duration": 22253}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447257978, "stop": 1754447258173, "duration": 195}, "status": "passed", "steps": [], "attachments": [{"uid": "5ee02e34d55022c3", "name": "测试总结", "source": "5ee02e34d55022c3.txt", "type": "text/plain", "size": 485}, {"uid": "62edba3487b28233", "name": "test_completed", "source": "62edba3487b28233.png", "type": "image/png", "size": 590553}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447258174, "stop": 1754447258174, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证visha已打开", "time": {"start": 1754447258174, "stop": 1754447258174, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447258175, "stop": 1754447258400, "duration": 225}, "status": "passed", "steps": [], "attachments": [{"uid": "655811420480759e", "name": "测试总结", "source": "655811420480759e.txt", "type": "text/plain", "size": 485}, {"uid": "3369170f41f71d38", "name": "test_completed", "source": "3369170f41f71d38.png", "type": "image/png", "size": 589946}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "e63e559dd4431304", "name": "stdout", "source": "e63e559dd4431304.txt", "type": "text/plain", "size": 15054}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447258401, "stop": 1754447258401, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447258402, "stop": 1754447259808, "duration": 1406}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_music"}, {"name": "subSuite", "value": "TestEllaOpen<PERSON>a"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "c7e528d9e62712d9", "status": "passed", "time": {"start": 1754397536624, "stop": 1754397556389, "duration": 19765}}], "categories": [], "tags": ["smoke"]}, "source": "8a51f504d5b44159.json", "parameterValues": []}