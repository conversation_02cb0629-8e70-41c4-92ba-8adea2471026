{"uid": "8b366b5074a3353e", "name": "测试set smart hub返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_smart_hub.TestEllaSetSmartHub#test_set_smart_hub", "historyId": "61e923bb9b35a687b231b0c27b5ec620", "time": {"start": 1754455021098, "stop": 1754455034934, "duration": 13836}, "description": "验证set smart hub指令返回预期的不支持响应", "descriptionHtml": "<p>验证set smart hub指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455009107, "stop": 1754455021097, "duration": 11990}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455021097, "stop": 1754455021097, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set smart hub指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set smart hub", "time": {"start": 1754455021098, "stop": 1754455034733, "duration": 13635}, "status": "passed", "steps": [{"name": "执行命令: set smart hub", "time": {"start": 1754455021098, "stop": 1754455034505, "duration": 13407}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455034505, "stop": 1754455034732, "duration": 227}, "status": "passed", "steps": [], "attachments": [{"uid": "a5022177bf4322d5", "name": "测试总结", "source": "a5022177bf4322d5.txt", "type": "text/plain", "size": 209}, {"uid": "ab9544c3e2d99d3c", "name": "test_completed", "source": "ab9544c3e2d99d3c.png", "type": "image/png", "size": 474988}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455034733, "stop": 1754455034734, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455034734, "stop": 1754455034933, "duration": 199}, "status": "passed", "steps": [], "attachments": [{"uid": "a7789bc6ec740996", "name": "测试总结", "source": "a7789bc6ec740996.txt", "type": "text/plain", "size": 209}, {"uid": "ea64248ef63fb66c", "name": "test_completed", "source": "ea64248ef63fb66c.png", "type": "image/png", "size": 474701}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "159cc1e0b79f4f6e", "name": "stdout", "source": "159cc1e0b79f4f6e.txt", "type": "text/plain", "size": 11214}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455034934, "stop": 1754455034934, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455034935, "stop": 1754455036290, "duration": 1355}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_smart_hub"}, {"name": "subSuite", "value": "TestEllaSetSmartHub"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_smart_hub"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "baa3c99991f59110", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405042678, "stop": 1754405053505, "duration": 10827}}], "categories": [], "tags": ["smoke"]}, "source": "8b366b5074a3353e.json", "parameterValues": []}