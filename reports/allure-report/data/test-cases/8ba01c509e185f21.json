{"uid": "8ba01c509e185f21", "name": "测试navigation to the lucky能正常执行", "fullName": "testcases.test_ella.third_coupling.test_navigation_to_the_lucky.TestEllaNavigationToTheLucky#test_navigation_to_the_lucky", "historyId": "75c56947a7b1061f7ec858fb20919b50", "time": {"start": 1754451526706, "stop": 1754451543802, "duration": 17096}, "description": "navigation to the lucky", "descriptionHtml": "<p>navigation to the lucky</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451513904, "stop": 1754451526705, "duration": 12801}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451526705, "stop": 1754451526705, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "navigation to the lucky", "status": "passed", "steps": [{"name": "执行命令: navigation to the lucky", "time": {"start": 1754451526706, "stop": 1754451543582, "duration": 16876}, "status": "passed", "steps": [{"name": "执行命令: navigation to the lucky", "time": {"start": 1754451526706, "stop": 1754451543372, "duration": 16666}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451543372, "stop": 1754451543581, "duration": 209}, "status": "passed", "steps": [], "attachments": [{"uid": "6845ebc9f869272e", "name": "测试总结", "source": "6845ebc9f869272e.txt", "type": "text/plain", "size": 367}, {"uid": "e486f3329f85676a", "name": "test_completed", "source": "e486f3329f85676a.png", "type": "image/png", "size": 694456}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证应用已打开", "time": {"start": 1754451543582, "stop": 1754451543582, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451543582, "stop": 1754451543802, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "ff419a6cd9cb1d6b", "name": "测试总结", "source": "ff419a6cd9cb1d6b.txt", "type": "text/plain", "size": 367}, {"uid": "c2bb87b06a1ff85f", "name": "test_completed", "source": "c2bb87b06a1ff85f.png", "type": "image/png", "size": 703472}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "eeb2cbd9b361111f", "name": "stdout", "source": "eeb2cbd9b361111f.txt", "type": "text/plain", "size": 12618}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451543803, "stop": 1754451543803, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451543803, "stop": 1754451545197, "duration": 1394}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigation_to_the_lucky"}, {"name": "subSuite", "value": "TestEllaNavigationToTheLucky"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigation_to_the_lucky"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "5a4f4a2734888530", "status": "passed", "time": {"start": 1754401677753, "stop": 1754401691699, "duration": 13946}}], "categories": [], "tags": ["smoke"]}, "source": "8ba01c509e185f21.json", "parameterValues": []}