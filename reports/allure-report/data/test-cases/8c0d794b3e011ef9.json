{"uid": "8c0d794b3e011ef9", "name": "测试set smart panel返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_smart_panel.TestEllaSetSmartPanel#test_set_smart_panel", "historyId": "5fc780d1e7f790011f0e4a521e125a16", "time": {"start": 1754455048351, "stop": 1754455062107, "duration": 13756}, "description": "验证set smart panel指令返回预期的不支持响应", "descriptionHtml": "<p>验证set smart panel指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455036295, "stop": 1754455048350, "duration": 12055}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455048350, "stop": 1754455048350, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set smart panel指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set smart panel", "time": {"start": 1754455048351, "stop": 1754455061923, "duration": 13572}, "status": "passed", "steps": [{"name": "执行命令: set smart panel", "time": {"start": 1754455048351, "stop": 1754455061750, "duration": 13399}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455061751, "stop": 1754455061922, "duration": 171}, "status": "passed", "steps": [], "attachments": [{"uid": "3d3c41b716c9a979", "name": "测试总结", "source": "3d3c41b716c9a979.txt", "type": "text/plain", "size": 215}, {"uid": "2d54e985ff6f6097", "name": "test_completed", "source": "2d54e985ff6f6097.png", "type": "image/png", "size": 489490}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455061923, "stop": 1754455061924, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455061924, "stop": 1754455062107, "duration": 183}, "status": "passed", "steps": [], "attachments": [{"uid": "411c802b8c6cfd56", "name": "测试总结", "source": "411c802b8c6cfd56.txt", "type": "text/plain", "size": 215}, {"uid": "57bf08ff92f8e18c", "name": "test_completed", "source": "57bf08ff92f8e18c.png", "type": "image/png", "size": 489433}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "660636b0baf90bea", "name": "stdout", "source": "660636b0baf90bea.txt", "type": "text/plain", "size": 11251}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455062108, "stop": 1754455062108, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455062109, "stop": 1754455063499, "duration": 1390}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_smart_panel"}, {"name": "subSuite", "value": "TestEllaSetSmartPanel"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_smart_panel"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "c77ed09dfead7039", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405068350, "stop": 1754405084000, "duration": 15650}}], "categories": [], "tags": ["smoke"]}, "source": "8c0d794b3e011ef9.json", "parameterValues": []}