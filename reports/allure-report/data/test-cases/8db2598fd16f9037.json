{"uid": "8db2598fd16f9037", "name": "测试play afro strut", "fullName": "testcases.test_ella.component_coupling.test_play_afro_strut.TestEllaOpenPlayAfroStrut#test_play_afro_strut", "historyId": "ffb0a39af30beaa699329479ec564117", "time": {"start": 1754447125376, "stop": 1754447154145, "duration": 28769}, "description": "测试play afro strut指令", "descriptionHtml": "<p>测试play afro strut指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447112624, "stop": 1754447125375, "duration": 12751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447125375, "stop": 1754447125375, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play afro strut指令", "status": "passed", "steps": [{"name": "执行命令: play afro strut", "time": {"start": 1754447125376, "stop": 1754447153938, "duration": 28562}, "status": "passed", "steps": [{"name": "执行命令: play afro strut", "time": {"start": 1754447125376, "stop": 1754447153682, "duration": 28306}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447153682, "stop": 1754447153938, "duration": 256}, "status": "passed", "steps": [], "attachments": [{"uid": "66e756c81f7fd90", "name": "测试总结", "source": "66e756c81f7fd90.txt", "type": "text/plain", "size": 496}, {"uid": "362eb5107919aef2", "name": "test_completed", "source": "362eb5107919aef2.png", "type": "image/png", "size": 550636}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447153938, "stop": 1754447153940, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证play afro strut已打开", "time": {"start": 1754447153940, "stop": 1754447153940, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447153940, "stop": 1754447154145, "duration": 205}, "status": "passed", "steps": [], "attachments": [{"uid": "615b1c813a880be7", "name": "测试总结", "source": "615b1c813a880be7.txt", "type": "text/plain", "size": 496}, {"uid": "72da1b950f665722", "name": "test_completed", "source": "72da1b950f665722.png", "type": "image/png", "size": 550636}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "6a47aac23105777", "name": "stdout", "source": "6a47aac23105777.txt", "type": "text/plain", "size": 15068}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447154146, "stop": 1754447154146, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447154147, "stop": 1754447155564, "duration": 1417}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_afro_strut"}, {"name": "subSuite", "value": "TestEllaOpenPlayAfroStrut"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_afro_strut"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "d43ecfde8b649b6b", "status": "passed", "time": {"start": 1754397434366, "stop": 1754397456408, "duration": 22042}}], "categories": [], "tags": ["smoke"]}, "source": "8db2598fd16f9037.json", "parameterValues": []}