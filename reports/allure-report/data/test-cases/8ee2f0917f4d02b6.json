{"uid": "8ee2f0917f4d02b6", "name": "测试show scores between livepool and manchester city能正常执行", "fullName": "testcases.test_ella.dialogue.test_show_scores_between_livepool_and_manchester_city.TestEllaShowScoresBetweenLivepoolManchesterCity#test_show_scores_between_livepool_and_manchester_city", "historyId": "83e3a1b41834e87017b680efd7c16b92", "time": {"start": 1754448555853, "stop": 1754448583869, "duration": 28016}, "description": "show scores between livepool and manchester city", "descriptionHtml": "<p>show scores between livepool and manchester city</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448543260, "stop": 1754448555851, "duration": 12591}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448555851, "stop": 1754448555851, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "show scores between livepool and manchester city", "status": "passed", "steps": [{"name": "执行命令: show scores between livepool and manchester city", "time": {"start": 1754448555853, "stop": 1754448583612, "duration": 27759}, "status": "passed", "steps": [{"name": "执行命令: show scores between livepool and manchester city", "time": {"start": 1754448555853, "stop": 1754448583367, "duration": 27514}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448583367, "stop": 1754448583612, "duration": 245}, "status": "passed", "steps": [], "attachments": [{"uid": "e23c745baa6ec8ab", "name": "测试总结", "source": "e23c745baa6ec8ab.txt", "type": "text/plain", "size": 840}, {"uid": "dae017134a783fa0", "name": "test_completed", "source": "dae017134a783fa0.png", "type": "image/png", "size": 675583}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448583612, "stop": 1754448583614, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448583614, "stop": 1754448583868, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "1a7fa56bb650b426", "name": "测试总结", "source": "1a7fa56bb650b426.txt", "type": "text/plain", "size": 840}, {"uid": "1bc3982fb585b3cb", "name": "test_completed", "source": "1bc3982fb585b3cb.png", "type": "image/png", "size": 675943}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "a799424fdba84ee8", "name": "stdout", "source": "a799424fdba84ee8.txt", "type": "text/plain", "size": 14955}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448583869, "stop": 1754448583869, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448583870, "stop": 1754448585297, "duration": 1427}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_show_scores_between_livepool_and_manchester_city"}, {"name": "subSuite", "value": "TestEllaShowScoresBetweenLivepoolManchesterCity"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_show_scores_between_livepool_and_manchester_city"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "a0eaae1088e3dc8", "status": "passed", "time": {"start": 1754398800120, "stop": 1754398814640, "duration": 14520}}], "categories": [], "tags": ["smoke"]}, "source": "8ee2f0917f4d02b6.json", "parameterValues": []}