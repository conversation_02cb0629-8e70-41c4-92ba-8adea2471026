{"uid": "8f31162e952b18d0", "name": "测试take a joke能正常执行", "fullName": "testcases.test_ella.dialogue.test_take_a_joke.TestEllaTakeJoke#test_take_a_joke", "historyId": "543965b4120af95548616c95b1b70ef1", "time": {"start": 1754448767966, "stop": 1754448782951, "duration": 14985}, "description": "take a joke", "descriptionHtml": "<p>take a joke</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448755317, "stop": 1754448767965, "duration": 12648}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448767965, "stop": 1754448767965, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "take a joke", "status": "passed", "steps": [{"name": "执行命令: take a joke", "time": {"start": 1754448767966, "stop": 1754448782736, "duration": 14770}, "status": "passed", "steps": [{"name": "执行命令: take a joke", "time": {"start": 1754448767966, "stop": 1754448782547, "duration": 14581}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448782547, "stop": 1754448782736, "duration": 189}, "status": "passed", "steps": [], "attachments": [{"uid": "56e4b92ee0c5513d", "name": "测试总结", "source": "56e4b92ee0c5513d.txt", "type": "text/plain", "size": 504}, {"uid": "81ff9417c6ead66c", "name": "test_completed", "source": "81ff9417c6ead66c.png", "type": "image/png", "size": 583566}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448782736, "stop": 1754448782739, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448782739, "stop": 1754448782951, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "da59b620a2612502", "name": "测试总结", "source": "da59b620a2612502.txt", "type": "text/plain", "size": 504}, {"uid": "5fdc3735eee6372a", "name": "test_completed", "source": "5fdc3735eee6372a.png", "type": "image/png", "size": 583797}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "9cebb9f68dba3a71", "name": "stdout", "source": "9cebb9f68dba3a71.txt", "type": "text/plain", "size": 13223}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448782952, "stop": 1754448782952, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448782953, "stop": 1754448784378, "duration": 1425}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_a_joke"}, {"name": "subSuite", "value": "TestEllaTakeJoke"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_a_joke"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "6d749867b8a13d8e", "status": "passed", "time": {"start": 1754398965423, "stop": 1754398979207, "duration": 13784}}], "categories": [], "tags": ["smoke"]}, "source": "8f31162e952b18d0.json", "parameterValues": []}