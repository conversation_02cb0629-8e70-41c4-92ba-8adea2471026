{"uid": "8f7de97d7fdf87b6", "name": "测试disable unfreeze返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_unfreeze.TestEllaDisableUnfreeze#test_disable_unfreeze", "historyId": "1695232002b2ad29ffa1faf52965470d", "time": {"start": 1754452442992, "stop": 1754452457124, "duration": 14132}, "description": "验证disable unfreeze指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable unfreeze指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452430259, "stop": 1754452442990, "duration": 12731}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452442990, "stop": 1754452442990, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证disable unfreeze指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable unfreeze", "time": {"start": 1754452442992, "stop": 1754452456913, "duration": 13921}, "status": "passed", "steps": [{"name": "执行命令: disable unfreeze", "time": {"start": 1754452442992, "stop": 1754452456730, "duration": 13738}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452456730, "stop": 1754452456912, "duration": 182}, "status": "passed", "steps": [], "attachments": [{"uid": "55dd8bc3d4bb3405", "name": "测试总结", "source": "55dd8bc3d4bb3405.txt", "type": "text/plain", "size": 221}, {"uid": "3fd3a29cb658808a", "name": "test_completed", "source": "3fd3a29cb658808a.png", "type": "image/png", "size": 481876}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452456913, "stop": 1754452456914, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452456914, "stop": 1754452457123, "duration": 209}, "status": "passed", "steps": [], "attachments": [{"uid": "42589ff4e95ddccd", "name": "测试总结", "source": "42589ff4e95ddccd.txt", "type": "text/plain", "size": 221}, {"uid": "39b8900bcc5ae18a", "name": "test_completed", "source": "39b8900bcc5ae18a.png", "type": "image/png", "size": 481666}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "df70dc2de5111422", "name": "stdout", "source": "df70dc2de5111422.txt", "type": "text/plain", "size": 11267}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452457124, "stop": 1754452457124, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452457125, "stop": 1754452458533, "duration": 1408}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_unfreeze"}, {"name": "subSuite", "value": "TestEllaDisableUnfreeze"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_unfreeze"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "84b9b1a573ce6760", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402476742, "stop": 1754402487775, "duration": 11033}}], "categories": [], "tags": ["smoke"]}, "source": "8f7de97d7fdf87b6.json", "parameterValues": []}