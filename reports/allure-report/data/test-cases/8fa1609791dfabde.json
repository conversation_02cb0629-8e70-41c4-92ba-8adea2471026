{"uid": "8fa1609791dfabde", "name": "测试navigation to the address in thie image能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_navigation_to_the_address_in_the_image.TestEllaNavigationAddressTheImage#test_navigation_to_the_address_in_the_image", "historyId": "488c24d02f5d5348f35881280e505f32", "time": {"start": 1754453483088, "stop": 1754453498792, "duration": 15704}, "description": "navigation to the address in thie image", "descriptionHtml": "<p>navigation to the address in thie image</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453471150, "stop": 1754453483087, "duration": 11937}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453483087, "stop": 1754453483087, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "navigation to the address in thie image", "status": "passed", "steps": [{"name": "执行命令: navigation to the address in thie image", "time": {"start": 1754453483088, "stop": 1754453498569, "duration": 15481}, "status": "passed", "steps": [{"name": "执行命令: navigation to the address in thie image", "time": {"start": 1754453483088, "stop": 1754453498345, "duration": 15257}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453498345, "stop": 1754453498569, "duration": 224}, "status": "passed", "steps": [], "attachments": [{"uid": "15c34784459e8336", "name": "测试总结", "source": "15c34784459e8336.txt", "type": "text/plain", "size": 615}, {"uid": "21647ea4b118eb5d", "name": "test_completed", "source": "21647ea4b118eb5d.png", "type": "image/png", "size": 505398}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证GoogleMap应用已打开", "time": {"start": 1754453498569, "stop": 1754453498569, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453498569, "stop": 1754453498792, "duration": 223}, "status": "passed", "steps": [], "attachments": [{"uid": "7d1d1fec54b3e7a5", "name": "测试总结", "source": "7d1d1fec54b3e7a5.txt", "type": "text/plain", "size": 615}, {"uid": "80a4fe4b4a55170f", "name": "test_completed", "source": "80a4fe4b4a55170f.png", "type": "image/png", "size": 505275}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "88a438d3ec6b1d28", "name": "stdout", "source": "88a438d3ec6b1d28.txt", "type": "text/plain", "size": 13275}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453498793, "stop": 1754453498794, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453498794, "stop": 1754453500145, "duration": 1351}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_navigation_to_the_address_in_the_image"}, {"name": "subSuite", "value": "TestEllaNavigationAddressTheImage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_navigation_to_the_address_in_the_image"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "2a584a3fa4306c62", "status": "passed", "time": {"start": 1754403506408, "stop": 1754403520174, "duration": 13766}}], "categories": [], "tags": ["smoke"]}, "source": "8fa1609791dfabde.json", "parameterValues": []}