{"uid": "90e2769c8ce04c27", "name": "测试turn on location services能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_location_services.TestEllaTurnLocationServices#test_turn_on_location_services", "historyId": "8a87a1d0f534afc4770901f3d1dfa316", "time": {"start": 1754451088417, "stop": 1754451103456, "duration": 15039}, "description": "turn on location services", "descriptionHtml": "<p>turn on location services</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451075951, "stop": 1754451088415, "duration": 12464}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451088416, "stop": 1754451088416, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "turn on location services", "status": "passed", "steps": [{"name": "执行命令: turn on location services", "time": {"start": 1754451088417, "stop": 1754451103278, "duration": 14861}, "status": "passed", "steps": [{"name": "执行命令: turn on location services", "time": {"start": 1754451088417, "stop": 1754451103092, "duration": 14675}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451103092, "stop": 1754451103278, "duration": 186}, "status": "passed", "steps": [], "attachments": [{"uid": "3a9528a7c1a021c8", "name": "测试总结", "source": "3a9528a7c1a021c8.txt", "type": "text/plain", "size": 206}, {"uid": "301a5f9baca03bde", "name": "test_completed", "source": "301a5f9baca03bde.png", "type": "image/png", "size": 398934}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451103278, "stop": 1754451103279, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754451103279, "stop": 1754451103279, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451103279, "stop": 1754451103453, "duration": 174}, "status": "passed", "steps": [], "attachments": [{"uid": "a25aa34c1774dc4a", "name": "测试总结", "source": "a25aa34c1774dc4a.txt", "type": "text/plain", "size": 206}, {"uid": "7d29ff36d747fd51", "name": "test_completed", "source": "7d29ff36d747fd51.png", "type": "image/png", "size": 399335}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "4ac492290e0d17e", "name": "stdout", "source": "4ac492290e0d17e.txt", "type": "text/plain", "size": 12145}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451103457, "stop": 1754451103457, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451103458, "stop": 1754451104858, "duration": 1400}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_location_services"}, {"name": "subSuite", "value": "TestEllaTurnLocationServices"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_location_services"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "68e32977e11e378", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Location is turned on now']\nassert False", "time": {"start": 1754401246956, "stop": 1754401263142, "duration": 16186}}], "categories": [], "tags": ["smoke"]}, "source": "90e2769c8ce04c27.json", "parameterValues": []}