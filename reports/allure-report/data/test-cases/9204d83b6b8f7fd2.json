{"uid": "9204d83b6b8f7fd2", "name": "测试turn on light theme能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_light_theme.TestEllaTurnLightTheme#test_turn_on_light_theme", "historyId": "16f38913a9d7e0ffc4c5ac51d5acf5c7", "time": {"start": 1754451033561, "stop": 1754451047165, "duration": 13604}, "description": "turn on light theme", "descriptionHtml": "<p>turn on light theme</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451021012, "stop": 1754451033560, "duration": 12548}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451033560, "stop": 1754451033561, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "turn on light theme", "status": "passed", "steps": [{"name": "执行命令: turn on light theme", "time": {"start": 1754451033561, "stop": 1754451046950, "duration": 13389}, "status": "passed", "steps": [{"name": "执行命令: turn on light theme", "time": {"start": 1754451033561, "stop": 1754451046750, "duration": 13189}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451046750, "stop": 1754451046950, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "e2f67dbdefec6ed1", "name": "测试总结", "source": "e2f67dbdefec6ed1.txt", "type": "text/plain", "size": 175}, {"uid": "40659d0d44b4b811", "name": "test_completed", "source": "40659d0d44b4b811.png", "type": "image/png", "size": 500519}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451046950, "stop": 1754451046953, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754451046953, "stop": 1754451046953, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451046953, "stop": 1754451047165, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "7f967f002e17bb6d", "name": "测试总结", "source": "7f967f002e17bb6d.txt", "type": "text/plain", "size": 175}, {"uid": "b8e09de1ae1b9c22", "name": "test_completed", "source": "b8e09de1ae1b9c22.png", "type": "image/png", "size": 499919}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d5e33a709b08344f", "name": "stdout", "source": "d5e33a709b08344f.txt", "type": "text/plain", "size": 13041}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451047166, "stop": 1754451047166, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451047167, "stop": 1754451048575, "duration": 1408}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_light_theme"}, {"name": "subSuite", "value": "TestEllaTurnLightTheme"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_light_theme"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "9438516b06c8c4e2", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754401185554, "stop": 1754401201577, "duration": 16023}}], "categories": [], "tags": ["smoke"]}, "source": "9204d83b6b8f7fd2.json", "parameterValues": []}