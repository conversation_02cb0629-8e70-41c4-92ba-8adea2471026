{"uid": "927966aed0675c11", "name": "测试continue music能正常执行", "fullName": "testcases.test_ella.component_coupling.test_continue_music.TestEllaContinueMusic#test_continue_music", "historyId": "87f3dc53ab72c729262e053c16a3dbcb", "time": {"start": 1754396953010, "stop": 1754396965571, "duration": 12561}, "description": "continue music", "descriptionHtml": "<p>continue music</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754396940084, "stop": 1754396953010, "duration": 12926}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754396953010, "stop": 1754396953010, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "continue music", "status": "passed", "steps": [{"name": "执行命令: continue music", "time": {"start": 1754396953011, "stop": 1754396965249, "duration": 12238}, "status": "passed", "steps": [{"name": "执行命令: continue music", "time": {"start": 1754396953011, "stop": 1754396964963, "duration": 11952}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754396964963, "stop": 1754396965248, "duration": 285}, "status": "passed", "steps": [], "attachments": [{"uid": "c4cf4e6f35e21d2e", "name": "测试总结", "source": "c4cf4e6f35e21d2e.txt", "type": "text/plain", "size": 185}, {"uid": "43c6dc1f2bc7dee5", "name": "test_completed", "source": "43c6dc1f2bc7dee5.png", "type": "image/png", "size": 557254}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754396965249, "stop": 1754396965254, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754396965254, "stop": 1754396965570, "duration": 316}, "status": "passed", "steps": [], "attachments": [{"uid": "a82c8e6c1ecb8d31", "name": "测试总结", "source": "a82c8e6c1ecb8d31.txt", "type": "text/plain", "size": 185}, {"uid": "f1657e7e4829db17", "name": "test_completed", "source": "f1657e7e4829db17.png", "type": "image/png", "size": 557254}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "db776a311b69b214", "name": "stdout", "source": "db776a311b69b214.txt", "type": "text/plain", "size": 11444}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754396965572, "stop": 1754396965572, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754396965574, "stop": 1754396966853, "duration": 1279}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_continue_music"}, {"name": "subSuite", "value": "TestEllaContinueMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_continue_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "927966aed0675c11.json", "parameterValues": []}