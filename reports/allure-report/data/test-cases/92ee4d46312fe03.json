{"uid": "92ee4d46312fe03", "name": "测试display the route go company", "fullName": "testcases.test_ella.component_coupling.test_display_the_route_go_company.TestEllaOpenMaps#test_display_the_route_go_company", "historyId": "f4d12b1367b35df96178a58e48fe8f5e", "time": {"start": 1754446729951, "stop": 1754446745234, "duration": 15283}, "description": "测试display the route go company指令", "descriptionHtml": "<p>测试display the route go company指令</p>\n", "status": "failed", "statusMessage": "AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', 'No company address set up yet.', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.component_coupling.test_display_the_route_go_company.TestEllaOpenMaps object at 0x000001E389273350>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38998E790>\n\n    @allure.title(\"测试display the route go company\")\n    @allure.description(\"测试display the route go company指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_display_the_route_go_company(self, ella_app):\n        \"\"\"测试display the route go company命令\"\"\"\n        command = \"display the route go company\"\n        app_name = 'maps'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     expected_text = ['done']\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', 'No company address set up yet.', '', '']'\nE           assert False\n\ntestcases\\test_ella\\component_coupling\\test_display_the_route_go_company.py:34: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754446718183, "stop": 1754446729950, "duration": 11767}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754446729950, "stop": 1754446729950, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试display the route go company指令", "status": "failed", "statusMessage": "AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', 'No company address set up yet.', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.component_coupling.test_display_the_route_go_company.TestEllaOpenMaps object at 0x000001E389273350>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38998E790>\n\n    @allure.title(\"测试display the route go company\")\n    @allure.description(\"测试display the route go company指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_display_the_route_go_company(self, ella_app):\n        \"\"\"测试display the route go company命令\"\"\"\n        command = \"display the route go company\"\n        app_name = 'maps'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     expected_text = ['done']\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', 'No company address set up yet.', '', '']'\nE           assert False\n\ntestcases\\test_ella\\component_coupling\\test_display_the_route_go_company.py:34: AssertionError", "steps": [{"name": "执行命令: display the route go company", "time": {"start": 1754446729951, "stop": 1754446745233, "duration": 15282}, "status": "passed", "steps": [{"name": "执行命令: display the route go company", "time": {"start": 1754446729951, "stop": 1754446745046, "duration": 15095}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446745046, "stop": 1754446745233, "duration": 187}, "status": "passed", "steps": [], "attachments": [{"uid": "8bf030af63e76ddb", "name": "测试总结", "source": "8bf030af63e76ddb.txt", "type": "text/plain", "size": 210}, {"uid": "94becd76d64c5a30", "name": "test_completed", "source": "94becd76d64c5a30.png", "type": "image/png", "size": 558135}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证maps已打开", "time": {"start": 1754446745233, "stop": 1754446745233, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', 'No company address set up yet.', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\component_coupling\\test_display_the_route_go_company.py\", line 34, in test_display_the_route_go_company\n    assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "e8a4a9a2dd02a3bd", "name": "stdout", "source": "e8a4a9a2dd02a3bd.txt", "type": "text/plain", "size": 11547}], "parameters": [], "attachmentStep": false, "stepsCount": 4, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754446745238, "stop": 1754446745413, "duration": 175}, "status": "passed", "steps": [], "attachments": [{"uid": "e33d8ffbef5f6587", "name": "失败截图-TestEllaOpenMaps", "source": "e33d8ffbef5f6587.png", "type": "image/png", "size": 558952}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754446745414, "stop": 1754446746798, "duration": 1384}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_display_the_route_go_company"}, {"name": "subSuite", "value": "TestEllaOpenMaps"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_display_the_route_go_company"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "cb4e44e83a418051", "status": "failed", "statusDetails": "AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', 'No company address set up yet.', '', '']'\nassert False", "time": {"start": 1754397041032, "stop": 1754397054977, "duration": 13945}}], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "92ee4d46312fe03.json", "parameterValues": []}