{"uid": "930f24c0b46c4205", "name": "测试enable unfreeze返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_unfreeze.TestEllaEnableUnfreeze#test_enable_unfreeze", "historyId": "afa6af304cfb25a990764680de5fa777", "time": {"start": 1754452822505, "stop": 1754452836329, "duration": 13824}, "description": "验证enable unfreeze指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable unfreeze指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452810664, "stop": 1754452822504, "duration": 11840}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452822504, "stop": 1754452822505, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证enable unfreeze指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable unfreeze", "time": {"start": 1754452822506, "stop": 1754452836160, "duration": 13654}, "status": "passed", "steps": [{"name": "执行命令: enable unfreeze", "time": {"start": 1754452822506, "stop": 1754452835973, "duration": 13467}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452835973, "stop": 1754452836160, "duration": 187}, "status": "passed", "steps": [], "attachments": [{"uid": "d59380f2088f8c8a", "name": "测试总结", "source": "d59380f2088f8c8a.txt", "type": "text/plain", "size": 219}, {"uid": "8b659235740ac6e9", "name": "test_completed", "source": "8b659235740ac6e9.png", "type": "image/png", "size": 485572}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452836160, "stop": 1754452836162, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452836162, "stop": 1754452836329, "duration": 167}, "status": "passed", "steps": [], "attachments": [{"uid": "fba0a072d00ce78e", "name": "测试总结", "source": "fba0a072d00ce78e.txt", "type": "text/plain", "size": 219}, {"uid": "a5a108487459b669", "name": "test_completed", "source": "a5a108487459b669.png", "type": "image/png", "size": 485835}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "469cb99d88a8830d", "name": "stdout", "source": "469cb99d88a8830d.txt", "type": "text/plain", "size": 11256}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452836330, "stop": 1754452836330, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452836331, "stop": 1754452837641, "duration": 1310}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_unfreeze"}, {"name": "subSuite", "value": "TestEllaEnableUnfreeze"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_unfreeze"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "18fdeabe9705935a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402854717, "stop": 1754402865637, "duration": 10920}}], "categories": [], "tags": ["smoke"]}, "source": "930f24c0b46c4205.json", "parameterValues": []}