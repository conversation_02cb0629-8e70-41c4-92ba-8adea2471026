{"uid": "93130e85f011449", "name": "测试What's the weather like in Shanghai today能正常执行", "fullName": "testcases.test_ella.component_coupling.test_what_s_the_weather_like_in_shanghai_today.TestEllaWhatSWeatherLikeShanghaiToday#test_what_s_the_weather_like_in_shanghai_today", "historyId": "53ec4c77118606257c016fc2f7b22065", "time": {"start": 1754447528618, "stop": 1754447549421, "duration": 20803}, "description": "What's the weather like in Shanghai today", "descriptionHtml": "<p>What's the weather like in Shanghai today</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447516089, "stop": 1754447528617, "duration": 12528}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447528617, "stop": 1754447528617, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "What's the weather like in Shanghai today", "status": "passed", "steps": [{"name": "执行命令: What's the weather like in Shanghai today", "time": {"start": 1754447528618, "stop": 1754447549229, "duration": 20611}, "status": "passed", "steps": [{"name": "执行命令: What's the weather like in Shanghai today", "time": {"start": 1754447528618, "stop": 1754447549050, "duration": 20432}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447549050, "stop": 1754447549229, "duration": 179}, "status": "passed", "steps": [], "attachments": [{"uid": "d80c601a1c889362", "name": "测试总结", "source": "d80c601a1c889362.txt", "type": "text/plain", "size": 274}, {"uid": "e0e77d7dfd68d1fd", "name": "test_completed", "source": "e0e77d7dfd68d1fd.png", "type": "image/png", "size": 544723}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447549229, "stop": 1754447549231, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447549231, "stop": 1754447549421, "duration": 190}, "status": "passed", "steps": [], "attachments": [{"uid": "7fb52c1c70654edd", "name": "测试总结", "source": "7fb52c1c70654edd.txt", "type": "text/plain", "size": 274}, {"uid": "545b353e6467eca4", "name": "test_completed", "source": "545b353e6467eca4.png", "type": "image/png", "size": 545132}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "1b2e3ea5afccd7a8", "name": "stdout", "source": "1b2e3ea5afccd7a8.txt", "type": "text/plain", "size": 12069}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447549422, "stop": 1754447549422, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447549423, "stop": 1754447550794, "duration": 1371}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_what_s_the_weather_like_in_shanghai_today"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherLikeShanghaiToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_what_s_the_weather_like_in_shanghai_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "d0bc974b322cdb0c", "status": "passed", "time": {"start": 1754397807292, "stop": 1754397827627, "duration": 20335}}], "categories": [], "tags": ["smoke"]}, "source": "93130e85f011449.json", "parameterValues": []}