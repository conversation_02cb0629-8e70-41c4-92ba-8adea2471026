{"uid": "9583fdfb28ae5b4d", "name": "测试close whatsapp能正常执行", "fullName": "testcases.test_ella.dialogue.test_close_whatsapp.TestEllaCloseWhatsapp#test_close_whatsapp", "historyId": "876e77318cece5d1079b726f0c97bc45", "time": {"start": 1754447750854, "stop": 1754447765708, "duration": 14854}, "description": "close whatsapp", "descriptionHtml": "<p>close whatsapp</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447738263, "stop": 1754447750851, "duration": 12588}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447750851, "stop": 1754447750851, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "close whatsapp", "status": "passed", "steps": [{"name": "执行命令: close whatsapp", "time": {"start": 1754447750854, "stop": 1754447765532, "duration": 14678}, "status": "passed", "steps": [{"name": "执行命令: close whatsapp", "time": {"start": 1754447750854, "stop": 1754447765344, "duration": 14490}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447765344, "stop": 1754447765532, "duration": 188}, "status": "passed", "steps": [], "attachments": [{"uid": "211c9d0f1f36437b", "name": "测试总结", "source": "211c9d0f1f36437b.txt", "type": "text/plain", "size": 154}, {"uid": "8115c70aaebbd1e3", "name": "test_completed", "source": "8115c70aaebbd1e3.png", "type": "image/png", "size": 556134}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447765532, "stop": 1754447765533, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447765533, "stop": 1754447765708, "duration": 175}, "status": "passed", "steps": [], "attachments": [{"uid": "a9b4bfed265e37e5", "name": "测试总结", "source": "a9b4bfed265e37e5.txt", "type": "text/plain", "size": 154}, {"uid": "8f9da64ccb9ac93c", "name": "test_completed", "source": "8f9da64ccb9ac93c.png", "type": "image/png", "size": 556177}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "98c37405c1e24ac0", "name": "stdout", "source": "98c37405c1e24ac0.txt", "type": "text/plain", "size": 11460}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447765709, "stop": 1754447765709, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447765710, "stop": 1754447767129, "duration": 1419}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_close_whatsapp"}, {"name": "subSuite", "value": "TestEllaCloseWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_close_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "271ea11d2cdbc1c0", "status": "passed", "time": {"start": 1754398017912, "stop": 1754398032366, "duration": 14454}}], "categories": [], "tags": ["smoke"]}, "source": "9583fdfb28ae5b4d.json", "parameterValues": []}