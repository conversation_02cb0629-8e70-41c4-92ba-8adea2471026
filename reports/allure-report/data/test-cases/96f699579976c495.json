{"uid": "96f699579976c495", "name": "测试open bluetooth", "fullName": "testcases.test_ella.system_coupling.test_open_bluetooth.TestEllaOpenBluetooth#test_open_bluetooth", "historyId": "733cc57b9e666f7c16017a85f41c410d", "time": {"start": 1754449892699, "stop": 1754449906211, "duration": 13512}, "description": "测试open bluetooth指令", "descriptionHtml": "<p>测试open bluetooth指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449880180, "stop": 1754449892697, "duration": 12517}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449892699, "stop": 1754449892699, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试open bluetooth指令", "status": "passed", "steps": [{"name": "执行命令: open bluetooth", "time": {"start": 1754449892699, "stop": 1754449906027, "duration": 13328}, "status": "passed", "steps": [{"name": "执行命令: open bluetooth", "time": {"start": 1754449892699, "stop": 1754449905852, "duration": 13153}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449905852, "stop": 1754449906027, "duration": 175}, "status": "passed", "steps": [], "attachments": [{"uid": "f12f6b38b87fddb3", "name": "测试总结", "source": "f12f6b38b87fddb3.txt", "type": "text/plain", "size": 187}, {"uid": "4c80e87fb4a6e751", "name": "test_completed", "source": "4c80e87fb4a6e751.png", "type": "image/png", "size": 517804}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754449906027, "stop": 1754449906028, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证bluetooth已打开", "time": {"start": 1754449906028, "stop": 1754449906028, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449906028, "stop": 1754449906211, "duration": 183}, "status": "passed", "steps": [], "attachments": [{"uid": "22ce2a29df191ca4", "name": "测试总结", "source": "22ce2a29df191ca4.txt", "type": "text/plain", "size": 187}, {"uid": "fc228e0d4f0deec6", "name": "test_completed", "source": "fc228e0d4f0deec6.png", "type": "image/png", "size": 517143}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "6cdc124d3213fdfa", "name": "stdout", "source": "6cdc124d3213fdfa.txt", "type": "text/plain", "size": 12683}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449906212, "stop": 1754449906212, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449906213, "stop": 1754449907615, "duration": 1402}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_open_bluetooth"}, {"name": "subSuite", "value": "TestEllaOpenBluetooth"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_open_bluetooth"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "2cdea1c03876fbed", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Bluetooth is turned on now']\nassert False", "time": {"start": 1754400071923, "stop": 1754400084101, "duration": 12178}}], "categories": [], "tags": ["smoke"]}, "source": "96f699579976c495.json", "parameterValues": []}