{"uid": "97384b55cfa58ab3", "name": "测试how to say hello in french能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_to_say_hello_in_french.TestEllaHowSayHelloFrench#test_how_to_say_hello_in_french", "historyId": "cc44ad4097a589726631a345e0cd01ad", "time": {"start": 1754448117033, "stop": 1754448129631, "duration": 12598}, "description": "how to say hello in french", "descriptionHtml": "<p>how to say hello in french</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448104178, "stop": 1754448117031, "duration": 12853}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448117031, "stop": 1754448117031, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "how to say hello in french", "status": "passed", "steps": [{"name": "执行命令: how to say hello in french", "time": {"start": 1754448117033, "stop": 1754448129430, "duration": 12397}, "status": "passed", "steps": [{"name": "执行命令: how to say hello in french", "time": {"start": 1754448117033, "stop": 1754448129239, "duration": 12206}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448129239, "stop": 1754448129430, "duration": 191}, "status": "passed", "steps": [], "attachments": [{"uid": "942022a78b0f9817", "name": "测试总结", "source": "942022a78b0f9817.txt", "type": "text/plain", "size": 178}, {"uid": "a7890351e55c751f", "name": "test_completed", "source": "a7890351e55c751f.png", "type": "image/png", "size": 445633}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448129430, "stop": 1754448129431, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448129431, "stop": 1754448129631, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "69a45e7799d06223", "name": "测试总结", "source": "69a45e7799d06223.txt", "type": "text/plain", "size": 178}, {"uid": "542b08ba2f2fc86e", "name": "test_completed", "source": "542b08ba2f2fc86e.png", "type": "image/png", "size": 446159}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "f024230fc8d91f55", "name": "stdout", "source": "f024230fc8d91f55.txt", "type": "text/plain", "size": 11265}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448129632, "stop": 1754448129632, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448129632, "stop": 1754448131059, "duration": 1427}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_to_say_hello_in_french"}, {"name": "subSuite", "value": "TestEllaHowSayHelloFrench"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_to_say_hello_in_french"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "5da7d9303360c66f", "status": "passed", "time": {"start": 1754398386919, "stop": 1754398398598, "duration": 11679}}], "categories": [], "tags": ["smoke"]}, "source": "97384b55cfa58ab3.json", "parameterValues": []}