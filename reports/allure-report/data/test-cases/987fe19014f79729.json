{"uid": "987fe19014f79729", "name": "测试Switch to Barrage Notification能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switch_to_barrage_notification.TestEllaSwitchBarrageNotification#test_switch_to_barrage_notification", "historyId": "79b68fd9ac84793c5f55250aad03649a", "time": {"start": 1754450500684, "stop": 1754450514283, "duration": 13599}, "description": "Switch to Barrage Notification", "descriptionHtml": "<p>Switch to Barrage Notification</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450488360, "stop": 1754450500682, "duration": 12322}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450500682, "stop": 1754450500682, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "Switch to Barrage Notification", "status": "passed", "steps": [{"name": "执行命令: Switch to Barrage Notification", "time": {"start": 1754450500684, "stop": 1754450514086, "duration": 13402}, "status": "passed", "steps": [{"name": "执行命令: Switch to Barrage Notification", "time": {"start": 1754450500684, "stop": 1754450513871, "duration": 13187}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450513871, "stop": 1754450514085, "duration": 214}, "status": "passed", "steps": [], "attachments": [{"uid": "ce45ed3caa58ba58", "name": "测试总结", "source": "ce45ed3caa58ba58.txt", "type": "text/plain", "size": 260}, {"uid": "4f84b593fbeb03e4", "name": "test_completed", "source": "4f84b593fbeb03e4.png", "type": "image/png", "size": 648920}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450514086, "stop": 1754450514086, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450514086, "stop": 1754450514283, "duration": 197}, "status": "passed", "steps": [], "attachments": [{"uid": "83df670cbda040f5", "name": "测试总结", "source": "83df670cbda040f5.txt", "type": "text/plain", "size": 260}, {"uid": "3dedf6b458fdbaae", "name": "test_completed", "source": "3dedf6b458fdbaae.png", "type": "image/png", "size": 648403}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "bbdfbe363377f95", "name": "stdout", "source": "bbdfbe363377f95.txt", "type": "text/plain", "size": 11575}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450514283, "stop": 1754450514283, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450514284, "stop": 1754450515667, "duration": 1383}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_barrage_notification"}, {"name": "subSuite", "value": "TestEllaSwitchBarrageNotification"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_barrage_notification"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "6a6d8fe8c58d434f", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754400667033, "stop": 1754400683004, "duration": 15971}}], "categories": [], "tags": ["smoke"]}, "source": "987fe19014f79729.json", "parameterValues": []}