{"uid": "99fd43b3cb1bc7f8", "name": "测试set edge mistouch prevention返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_edge_mistouch_prevention.TestEllaSetEdgeMistouchPrevention#test_set_edge_mistouch_prevention", "historyId": "2b2dcc407b5c428f968f62d94fe8025c", "time": {"start": 1754454441043, "stop": 1754454454848, "duration": 13805}, "description": "验证set edge mistouch prevention指令返回预期的不支持响应", "descriptionHtml": "<p>验证set edge mistouch prevention指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454428589, "stop": 1754454441043, "duration": 12454}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454441043, "stop": 1754454441043, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set edge mistouch prevention指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set edge mistouch prevention", "time": {"start": 1754454441043, "stop": 1754454454618, "duration": 13575}, "status": "passed", "steps": [{"name": "执行命令: set edge mistouch prevention", "time": {"start": 1754454441043, "stop": 1754454454426, "duration": 13383}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454454426, "stop": 1754454454618, "duration": 192}, "status": "passed", "steps": [], "attachments": [{"uid": "46208758c5c8ee85", "name": "测试总结", "source": "46208758c5c8ee85.txt", "type": "text/plain", "size": 254}, {"uid": "26bb9d861fa5f0fe", "name": "test_completed", "source": "26bb9d861fa5f0fe.png", "type": "image/png", "size": 509500}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454454618, "stop": 1754454454619, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454454619, "stop": 1754454454848, "duration": 229}, "status": "passed", "steps": [], "attachments": [{"uid": "2306096f9fee68f8", "name": "测试总结", "source": "2306096f9fee68f8.txt", "type": "text/plain", "size": 254}, {"uid": "5b41468f18167e2a", "name": "test_completed", "source": "5b41468f18167e2a.png", "type": "image/png", "size": 509749}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "52959f98cade403d", "name": "stdout", "source": "52959f98cade403d.txt", "type": "text/plain", "size": 11431}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454454848, "stop": 1754454454848, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454454849, "stop": 1754454456230, "duration": 1381}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_edge_mistouch_prevention"}, {"name": "subSuite", "value": "TestEllaSetEdgeMistouchPrevention"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_edge_mistouch_prevention"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "99ed628eb665a54a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404444272, "stop": 1754404456485, "duration": 12213}}], "categories": [], "tags": ["smoke"]}, "source": "99fd43b3cb1bc7f8.json", "parameterValues": []}