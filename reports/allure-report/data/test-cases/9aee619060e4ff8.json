{"uid": "9aee619060e4ff8", "name": "测试turn on light theme能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_light_theme.TestEllaTurnLightTheme#test_turn_on_daka_theme", "historyId": "ae86b8d534909e1e7c8c7adb4ee39e5c", "time": {"start": 1754451061346, "stop": 1754451074571, "duration": 13225}, "description": "turn on light theme", "descriptionHtml": "<p>turn on light theme</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451048581, "stop": 1754451061345, "duration": 12764}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451061345, "stop": 1754451061345, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "turn on light theme", "status": "passed", "steps": [{"name": "执行命令: turn on dark theme", "time": {"start": 1754451061346, "stop": 1754451074404, "duration": 13058}, "status": "passed", "steps": [{"name": "执行命令: turn on dark theme", "time": {"start": 1754451061346, "stop": 1754451074215, "duration": 12869}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451074215, "stop": 1754451074404, "duration": 189}, "status": "passed", "steps": [], "attachments": [{"uid": "ad1854f3af9fab8f", "name": "测试总结", "source": "ad1854f3af9fab8f.txt", "type": "text/plain", "size": 174}, {"uid": "149e85db5c03eeb0", "name": "test_completed", "source": "149e85db5c03eeb0.png", "type": "image/png", "size": 377294}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451074404, "stop": 1754451074406, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754451074406, "stop": 1754451074406, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451074406, "stop": 1754451074570, "duration": 164}, "status": "passed", "steps": [], "attachments": [{"uid": "f4096d05e3f1409d", "name": "测试总结", "source": "f4096d05e3f1409d.txt", "type": "text/plain", "size": 174}, {"uid": "9eb475d42783808f", "name": "test_completed", "source": "9eb475d42783808f.png", "type": "image/png", "size": 377545}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "4e8505f0f7706f09", "name": "stdout", "source": "4e8505f0f7706f09.txt", "type": "text/plain", "size": 13038}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451074571, "stop": 1754451074571, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451074572, "stop": 1754451075947, "duration": 1375}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_light_theme"}, {"name": "subSuite", "value": "TestEllaTurnLightTheme"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_light_theme"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "cdc10dab0dde50b2", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754401215884, "stop": 1754401232339, "duration": 16455}}], "categories": [], "tags": ["smoke"]}, "source": "9aee619060e4ff8.json", "parameterValues": []}