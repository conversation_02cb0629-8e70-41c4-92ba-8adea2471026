{"uid": "9bc8f1759283437a", "name": "测试open camera", "fullName": "testcases.test_ella.open_app.test_open_camera.TestEllaOpenCamera#test_open_camera", "historyId": "cf4d81285cd0bfb49bf81dabe5eaa538", "time": {"start": 1754449318689, "stop": 1754449336910, "duration": 18221}, "description": "测试open camera指令", "descriptionHtml": "<p>测试open camera指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449305953, "stop": 1754449318688, "duration": 12735}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449318688, "stop": 1754449318688, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试open camera指令", "status": "passed", "steps": [{"name": "执行命令: open camera", "time": {"start": 1754449318689, "stop": 1754449336713, "duration": 18024}, "status": "passed", "steps": [{"name": "执行命令: open camera", "time": {"start": 1754449318689, "stop": 1754449336499, "duration": 17810}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449336499, "stop": 1754449336713, "duration": 214}, "status": "passed", "steps": [], "attachments": [{"uid": "1593530f0f27ccc9", "name": "测试总结", "source": "1593530f0f27ccc9.txt", "type": "text/plain", "size": 248}, {"uid": "5c7c29df4b89815d", "name": "test_completed", "source": "5c7c29df4b89815d.png", "type": "image/png", "size": 549563}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754449336713, "stop": 1754449336714, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证camera已打开", "time": {"start": 1754449336714, "stop": 1754449336714, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449336714, "stop": 1754449336910, "duration": 196}, "status": "passed", "steps": [], "attachments": [{"uid": "549ce4c417c8aaa4", "name": "测试总结", "source": "549ce4c417c8aaa4.txt", "type": "text/plain", "size": 248}, {"uid": "4074477b1a31c42b", "name": "test_completed", "source": "4074477b1a31c42b.png", "type": "image/png", "size": 549319}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "713e46150dca5180", "name": "stdout", "source": "713e46150dca5180.txt", "type": "text/plain", "size": 14511}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449336911, "stop": 1754449336911, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449336912, "stop": 1754449338279, "duration": 1367}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_camera"}, {"name": "subSuite", "value": "TestEllaOpenCamera"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_camera"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "1dfd12c1bd7d35e2", "status": "passed", "time": {"start": 1754399517612, "stop": 1754399534201, "duration": 16589}}], "categories": [], "tags": ["smoke"]}, "source": "9bc8f1759283437a.json", "parameterValues": []}