{"uid": "9c64e1e3896872ea", "name": "测试how is the weather today能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_is_the_weather_today.TestEllaHowIsWeatherToday#test_how_is_the_weather_today", "historyId": "3004e41c81a7ebd857f79d043aaf59df", "time": {"start": 1754398256807, "stop": 1754398276706, "duration": 19899}, "description": "how is the weather today", "descriptionHtml": "<p>how is the weather today</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398243435, "stop": 1754398256805, "duration": 13370}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398256805, "stop": 1754398256805, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "how is the weather today", "status": "passed", "steps": [{"name": "执行命令: how is the weather today", "time": {"start": 1754398256807, "stop": 1754398276414, "duration": 19607}, "status": "passed", "steps": [{"name": "执行命令: how is the weather today", "time": {"start": 1754398256807, "stop": 1754398276179, "duration": 19372}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398276179, "stop": 1754398276413, "duration": 234}, "status": "passed", "steps": [], "attachments": [{"uid": "3b3b5174db72b39a", "name": "测试总结", "source": "3b3b5174db72b39a.txt", "type": "text/plain", "size": 255}, {"uid": "33cfaf5c9c5daf02", "name": "test_completed", "source": "33cfaf5c9c5daf02.png", "type": "image/png", "size": 485479}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398276414, "stop": 1754398276422, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398276422, "stop": 1754398276705, "duration": 283}, "status": "passed", "steps": [], "attachments": [{"uid": "14d864d4d1701015", "name": "测试总结", "source": "14d864d4d1701015.txt", "type": "text/plain", "size": 255}, {"uid": "ae46cc440d631aeb", "name": "test_completed", "source": "ae46cc440d631aeb.png", "type": "image/png", "size": 485479}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3227756ade246e58", "name": "stdout", "source": "3227756ade246e58.txt", "type": "text/plain", "size": 12366}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398276707, "stop": 1754398276707, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398276709, "stop": 1754398277952, "duration": 1243}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_is_the_weather_today"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_is_the_weather_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "9c64e1e3896872ea.json", "parameterValues": []}