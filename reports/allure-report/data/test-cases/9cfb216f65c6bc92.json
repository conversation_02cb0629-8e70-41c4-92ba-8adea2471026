{"uid": "9cfb216f65c6bc92", "name": "测试play political news", "fullName": "testcases.test_ella.dialogue.test_play_political_news.TestEllaOpenPlayPoliticalNews#test_play_political_news", "historyId": "1bf9bd9c91ab7da6f818ff587cfff7da", "time": {"start": 1754398654100, "stop": 1754398669905, "duration": 15805}, "description": "测试play political news指令", "descriptionHtml": "<p>测试play political news指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398641087, "stop": 1754398654099, "duration": 13012}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398654099, "stop": 1754398654099, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play political news指令", "status": "passed", "steps": [{"name": "执行命令: play political news", "time": {"start": 1754398654100, "stop": 1754398669634, "duration": 15534}, "status": "passed", "steps": [{"name": "执行命令: play political news", "time": {"start": 1754398654100, "stop": 1754398669309, "duration": 15209}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398669309, "stop": 1754398669634, "duration": 325}, "status": "passed", "steps": [], "attachments": [{"uid": "6f34e6ef40b19a27", "name": "测试总结", "source": "6f34e6ef40b19a27.txt", "type": "text/plain", "size": 649}, {"uid": "f4993c31f427579e", "name": "test_completed", "source": "f4993c31f427579e.png", "type": "image/png", "size": 545131}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398669634, "stop": 1754398669638, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398669638, "stop": 1754398669904, "duration": 266}, "status": "passed", "steps": [], "attachments": [{"uid": "9cfb6b92f448b6e1", "name": "测试总结", "source": "9cfb6b92f448b6e1.txt", "type": "text/plain", "size": 649}, {"uid": "18d7288312ac1eb3", "name": "test_completed", "source": "18d7288312ac1eb3.png", "type": "image/png", "size": 545609}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "56dd8849a3caede6", "name": "stdout", "source": "56dd8849a3caede6.txt", "type": "text/plain", "size": 14379}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398669906, "stop": 1754398669906, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398669908, "stop": 1754398671236, "duration": 1328}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_political_news"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_political_news"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "9cfb216f65c6bc92.json", "parameterValues": []}