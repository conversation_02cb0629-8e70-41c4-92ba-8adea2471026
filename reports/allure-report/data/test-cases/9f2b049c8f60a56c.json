{"uid": "9f2b049c8f60a56c", "name": "测试play football video by youtube", "fullName": "testcases.test_ella.unsupported_commands.test_play_football_video_by_youtube.TestEllaOpenPlayPoliticalNews#test_play_political_news", "historyId": "354fa8bae544cb695857ab141abd74eb", "time": {"start": 1754453709213, "stop": 1754453726412, "duration": 17199}, "description": "测试play football video by youtube指令", "descriptionHtml": "<p>测试play football video by youtube指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453697309, "stop": 1754453709212, "duration": 11903}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453709212, "stop": 1754453709212, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play football video by youtube指令", "status": "passed", "steps": [{"name": "执行命令: play football video by youtube", "time": {"start": 1754453709213, "stop": 1754453726215, "duration": 17002}, "status": "passed", "steps": [{"name": "执行命令: play football video by youtube", "time": {"start": 1754453709213, "stop": 1754453726016, "duration": 16803}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453726016, "stop": 1754453726215, "duration": 199}, "status": "passed", "steps": [], "attachments": [{"uid": "97395ed58bf8c971", "name": "测试总结", "source": "97395ed58bf8c971.txt", "type": "text/plain", "size": 251}, {"uid": "b3cc4ee30067e8e0", "name": "test_completed", "source": "b3cc4ee30067e8e0.png", "type": "image/png", "size": 441758}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754453726215, "stop": 1754453726216, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证youtube已打开", "time": {"start": 1754453726216, "stop": 1754453726216, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453726216, "stop": 1754453726412, "duration": 196}, "status": "passed", "steps": [], "attachments": [{"uid": "b48c696bf5a33b9b", "name": "测试总结", "source": "b48c696bf5a33b9b.txt", "type": "text/plain", "size": 251}, {"uid": "75703d387b5a2037", "name": "test_completed", "source": "75703d387b5a2037.png", "type": "image/png", "size": 442394}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3f622d495c65c3a3", "name": "stdout", "source": "3f622d495c65c3a3.txt", "type": "text/plain", "size": 15404}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453726412, "stop": 1754453726412, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453726413, "stop": 1754453727805, "duration": 1392}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_football_video_by_youtube"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_football_video_by_youtube"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "129f15bf42fbd0cd", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754403724419, "stop": 1754403741204, "duration": 16785}}], "categories": [], "tags": ["smoke"]}, "source": "9f2b049c8f60a56c.json", "parameterValues": []}