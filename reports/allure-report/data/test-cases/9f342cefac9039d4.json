{"uid": "9f342cefac9039d4", "name": "测试i want to make a call能正常执行", "fullName": "testcases.test_ella.dialogue.test_i_want_to_make_a_call.TestEllaIWantMakeCall#test_i_want_to_make_a_call", "historyId": "f4da532f5d62abff197a05947efc027a", "time": {"start": 1754448200490, "stop": 1754448222977, "duration": 22487}, "description": "i want to make a call", "descriptionHtml": "<p>i want to make a call</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448187864, "stop": 1754448200489, "duration": 12625}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448200489, "stop": 1754448200489, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "i want to make a call", "status": "passed", "steps": [{"name": "执行命令: i want to make a call", "time": {"start": 1754448200490, "stop": 1754448222789, "duration": 22299}, "status": "passed", "steps": [{"name": "执行命令: i want to make a call", "time": {"start": 1754448200490, "stop": 1754448222591, "duration": 22101}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448222591, "stop": 1754448222789, "duration": 198}, "status": "passed", "steps": [], "attachments": [{"uid": "98d3018d22d2e435", "name": "测试总结", "source": "98d3018d22d2e435.txt", "type": "text/plain", "size": 193}, {"uid": "abbc118f1f88c25e", "name": "test_completed", "source": "abbc118f1f88c25e.png", "type": "image/png", "size": 577917}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448222789, "stop": 1754448222791, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448222791, "stop": 1754448222976, "duration": 185}, "status": "passed", "steps": [], "attachments": [{"uid": "518bbe40a1bd58b", "name": "测试总结", "source": "518bbe40a1bd58b.txt", "type": "text/plain", "size": 193}, {"uid": "27828017c88fdda3", "name": "test_completed", "source": "27828017c88fdda3.png", "type": "image/png", "size": 577920}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "9bb2091a3045a93d", "name": "stdout", "source": "9bb2091a3045a93d.txt", "type": "text/plain", "size": 11745}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448222977, "stop": 1754448222977, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448222978, "stop": 1754448224321, "duration": 1343}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_i_want_to_make_a_call"}, {"name": "subSuite", "value": "TestEllaIWantMakeCall"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_i_want_to_make_a_call"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "5a857d261f4a2ea1", "status": "passed", "time": {"start": 1754398468360, "stop": 1754398490137, "duration": 21777}}], "categories": [], "tags": ["smoke"]}, "source": "9f342cefac9039d4.json", "parameterValues": []}