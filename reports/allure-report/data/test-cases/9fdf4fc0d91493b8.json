{"uid": "9fdf4fc0d91493b8", "name": "测试close folax能正常执行", "fullName": "testcases.test_ella.component_coupling.test_close_folax.TestEllaCloseFolax#test_close_folax", "historyId": "8b2d3084bb429ea5def5db416bbf10a7", "time": {"start": 1754446569173, "stop": 1754446601540, "duration": 32367}, "description": "close folax", "descriptionHtml": "<p>close folax</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754446556591, "stop": 1754446569171, "duration": 12580}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754446569171, "stop": 1754446569172, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "close folax", "status": "passed", "steps": [{"name": "执行命令: close folax", "time": {"start": 1754446569173, "stop": 1754446601351, "duration": 32178}, "status": "passed", "steps": [{"name": "执行命令: close folax", "time": {"start": 1754446569173, "stop": 1754446601137, "duration": 31964}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446601137, "stop": 1754446601350, "duration": 213}, "status": "passed", "steps": [], "attachments": [{"uid": "e15550eaddfa6ecd", "name": "测试总结", "source": "e15550eaddfa6ecd.txt", "type": "text/plain", "size": 672}, {"uid": "b3edb3d07f693bfc", "name": "test_completed", "source": "b3edb3d07f693bfc.png", "type": "image/png", "size": 504805}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证已打开", "time": {"start": 1754446601351, "stop": 1754446601351, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446601351, "stop": 1754446601540, "duration": 189}, "status": "passed", "steps": [], "attachments": [{"uid": "65112ed7d0d6a3f7", "name": "测试总结", "source": "65112ed7d0d6a3f7.txt", "type": "text/plain", "size": 672}, {"uid": "9ec2f1b2411e3016", "name": "test_completed", "source": "9ec2f1b2411e3016.png", "type": "image/png", "size": 504983}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d4cf92b85b7fb6d4", "name": "stdout", "source": "d4cf92b85b7fb6d4.txt", "type": "text/plain", "size": 18970}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754446601540, "stop": 1754446601540, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754446601541, "stop": 1754446602897, "duration": 1356}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_folax"}, {"name": "subSuite", "value": "TestEllaCloseFolax"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_folax"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "4aabdb885aa35939", "status": "passed", "time": {"start": 1754396879640, "stop": 1754396910986, "duration": 31346}}], "categories": [], "tags": ["smoke"]}, "source": "9fdf4fc0d91493b8.json", "parameterValues": []}