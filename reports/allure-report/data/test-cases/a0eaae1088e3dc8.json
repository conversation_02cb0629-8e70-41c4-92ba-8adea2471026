{"uid": "a0eaae1088e3dc8", "name": "测试show scores between livepool and manchester city能正常执行", "fullName": "testcases.test_ella.dialogue.test_show_scores_between_livepool_and_manchester_city.TestEllaShowScoresBetweenLivepoolManchesterCity#test_show_scores_between_livepool_and_manchester_city", "historyId": "83e3a1b41834e87017b680efd7c16b92", "time": {"start": 1754398800120, "stop": 1754398814640, "duration": 14520}, "description": "show scores between livepool and manchester city", "descriptionHtml": "<p>show scores between livepool and manchester city</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398787100, "stop": 1754398800119, "duration": 13019}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398800119, "stop": 1754398800119, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "show scores between livepool and manchester city", "status": "passed", "steps": [{"name": "执行命令: show scores between livepool and manchester city", "time": {"start": 1754398800120, "stop": 1754398814360, "duration": 14240}, "status": "passed", "steps": [{"name": "执行命令: show scores between livepool and manchester city", "time": {"start": 1754398800120, "stop": 1754398814107, "duration": 13987}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398814107, "stop": 1754398814359, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "342517bc31daa993", "name": "测试总结", "source": "342517bc31daa993.txt", "type": "text/plain", "size": 710}, {"uid": "a029fd40c3e53d54", "name": "test_completed", "source": "a029fd40c3e53d54.png", "type": "image/png", "size": 584840}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398814360, "stop": 1754398814366, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398814366, "stop": 1754398814639, "duration": 273}, "status": "passed", "steps": [], "attachments": [{"uid": "277c36756297b843", "name": "测试总结", "source": "277c36756297b843.txt", "type": "text/plain", "size": 710}, {"uid": "71b0c2950b519b5c", "name": "test_completed", "source": "71b0c2950b519b5c.png", "type": "image/png", "size": 583915}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "1a470180cd16cb1f", "name": "stdout", "source": "1a470180cd16cb1f.txt", "type": "text/plain", "size": 14435}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398814641, "stop": 1754398814641, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398814643, "stop": 1754398815995, "duration": 1352}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_show_scores_between_livepool_and_manchester_city"}, {"name": "subSuite", "value": "TestEllaShowScoresBetweenLivepoolManchesterCity"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_show_scores_between_livepool_and_manchester_city"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "a0eaae1088e3dc8.json", "parameterValues": []}