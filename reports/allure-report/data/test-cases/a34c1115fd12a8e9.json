{"uid": "a34c1115fd12a8e9", "name": "测试next channel能正常执行", "fullName": "testcases.test_ella.component_coupling.test_next_channel.TestEllaNextChannel#test_next_channel", "historyId": "1d15cba90ae0426fa12e3218f1c542a6", "time": {"start": 1754397094717, "stop": 1754397107416, "duration": 12699}, "description": "next channel", "descriptionHtml": "<p>next channel</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397082944, "stop": 1754397094716, "duration": 11772}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397094716, "stop": 1754397094716, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "next channel", "status": "passed", "steps": [{"name": "执行命令: next channel", "time": {"start": 1754397094717, "stop": 1754397107148, "duration": 12431}, "status": "passed", "steps": [{"name": "执行命令: next channel", "time": {"start": 1754397094717, "stop": 1754397106896, "duration": 12179}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397106896, "stop": 1754397107147, "duration": 251}, "status": "passed", "steps": [], "attachments": [{"uid": "d06030bb74ccbaa6", "name": "测试总结", "source": "d06030bb74ccbaa6.txt", "type": "text/plain", "size": 204}, {"uid": "fe1e60f9e8ce7083", "name": "test_completed", "source": "fe1e60f9e8ce7083.png", "type": "image/png", "size": 623164}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754397107148, "stop": 1754397107153, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397107153, "stop": 1754397107415, "duration": 262}, "status": "passed", "steps": [], "attachments": [{"uid": "8cc6889c5ac532e0", "name": "测试总结", "source": "8cc6889c5ac532e0.txt", "type": "text/plain", "size": 204}, {"uid": "ba6873e393d65eda", "name": "test_completed", "source": "ba6873e393d65eda.png", "type": "image/png", "size": 623298}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "46a8ef070c4d6996", "name": "stdout", "source": "46a8ef070c4d6996.txt", "type": "text/plain", "size": 11249}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397107417, "stop": 1754397107417, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397107419, "stop": 1754397108668, "duration": 1249}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_next_channel"}, {"name": "subSuite", "value": "TestEllaNextChannel"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_next_channel"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "a34c1115fd12a8e9.json", "parameterValues": []}