{"uid": "a3c1f99a68a83c03", "name": "测试the second返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_the_second.TestEllaSecond#test_the_second", "historyId": "a192659952d6d75342a1c692afadb96d", "time": {"start": 1754455351920, "stop": 1754455366101, "duration": 14181}, "description": "验证the second指令返回预期的不支持响应", "descriptionHtml": "<p>验证the second指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455340062, "stop": 1754455351919, "duration": 11857}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455351919, "stop": 1754455351919, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证the second指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: the second", "time": {"start": 1754455351920, "stop": 1754455365890, "duration": 13970}, "status": "passed", "steps": [{"name": "执行命令: the second", "time": {"start": 1754455351920, "stop": 1754455365702, "duration": 13782}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455365702, "stop": 1754455365890, "duration": 188}, "status": "passed", "steps": [], "attachments": [{"uid": "7de20f516789b462", "name": "测试总结", "source": "7de20f516789b462.txt", "type": "text/plain", "size": 210}, {"uid": "99d9e3ef3d5c756b", "name": "test_completed", "source": "99d9e3ef3d5c756b.png", "type": "image/png", "size": 470470}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455365891, "stop": 1754455365891, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455365891, "stop": 1754455366100, "duration": 209}, "status": "passed", "steps": [], "attachments": [{"uid": "ab83250a6de440a9", "name": "测试总结", "source": "ab83250a6de440a9.txt", "type": "text/plain", "size": 210}, {"uid": "eb137a169b561e40", "name": "test_completed", "source": "eb137a169b561e40.png", "type": "image/png", "size": 470259}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "c4b8d4a86eb96d7c", "name": "stdout", "source": "c4b8d4a86eb96d7c.txt", "type": "text/plain", "size": 11198}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455366101, "stop": 1754455366101, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455366102, "stop": 1754455367442, "duration": 1340}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_the_second"}, {"name": "subSuite", "value": "TestEllaSecond"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_the_second"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "40bc34a3cc2c250e", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405393876, "stop": 1754405408167, "duration": 14291}}], "categories": [], "tags": ["smoke"]}, "source": "a3c1f99a68a83c03.json", "parameterValues": []}