{"uid": "a497a4f10cf03219", "name": "测试i wanna be rich能正常执行", "fullName": "testcases.test_ella.dialogue.test_i_wanna_be_rich.TestEllaIWannaBeRich#test_i_wanna_be_rich", "historyId": "6ecc7e0fc961d0d4e7e46672c033625a", "time": {"start": 1754398439998, "stop": 1754398454283, "duration": 14285}, "description": "i wanna be rich", "descriptionHtml": "<p>i wanna be rich</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398427063, "stop": 1754398439997, "duration": 12934}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398439997, "stop": 1754398439997, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "i wanna be rich", "status": "passed", "steps": [{"name": "执行命令: i wanna be rich", "time": {"start": 1754398439998, "stop": 1754398454018, "duration": 14020}, "status": "passed", "steps": [{"name": "执行命令: i wanna be rich", "time": {"start": 1754398439998, "stop": 1754398453758, "duration": 13760}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398453758, "stop": 1754398454017, "duration": 259}, "status": "passed", "steps": [], "attachments": [{"uid": "da01ad503a4c5ac7", "name": "测试总结", "source": "da01ad503a4c5ac7.txt", "type": "text/plain", "size": 179}, {"uid": "5d77b8b63f047eba", "name": "test_completed", "source": "5d77b8b63f047eba.png", "type": "image/png", "size": 538663}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398454018, "stop": 1754398454023, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398454023, "stop": 1754398454282, "duration": 259}, "status": "passed", "steps": [], "attachments": [{"uid": "83956a4803640e54", "name": "测试总结", "source": "83956a4803640e54.txt", "type": "text/plain", "size": 179}, {"uid": "70424c92762352b4", "name": "test_completed", "source": "70424c92762352b4.png", "type": "image/png", "size": 538708}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "995c1e0783ff6f67", "name": "stdout", "source": "995c1e0783ff6f67.txt", "type": "text/plain", "size": 11418}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398454284, "stop": 1754398454284, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398454286, "stop": 1754398455552, "duration": 1266}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_i_wanna_be_rich"}, {"name": "subSuite", "value": "TestEllaIWannaBeRich"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_i_wanna_be_rich"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "a497a4f10cf03219.json", "parameterValues": []}