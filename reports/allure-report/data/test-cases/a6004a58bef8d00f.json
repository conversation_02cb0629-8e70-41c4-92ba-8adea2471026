{"uid": "a6004a58bef8d00f", "name": "测试delete the 8 o'clock alarm", "fullName": "testcases.test_ella.component_coupling.test_delete_the_8_o_clock_alarm.TestEllaOpenClock#test_delete_the_8_o_clock_alarm", "historyId": "8c62567d8b8a27f77124afc90fa44336", "time": {"start": 1754397009121, "stop": 1754397026564, "duration": 17443}, "description": "测试delete the 8 o'clock alarm指令", "descriptionHtml": "<p>测试delete the 8 o'clock alarm指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754396996664, "stop": 1754397009120, "duration": 12456}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397009120, "stop": 1754397009120, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试delete the 8 o'clock alarm指令", "status": "passed", "steps": [{"name": "执行命令: delete the 8 o'clock alarm", "time": {"start": 1754397009121, "stop": 1754397026261, "duration": 17140}, "status": "passed", "steps": [{"name": "执行命令: delete the 8 o'clock alarm", "time": {"start": 1754397009121, "stop": 1754397025944, "duration": 16823}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397025944, "stop": 1754397026259, "duration": 315}, "status": "passed", "steps": [], "attachments": [{"uid": "e3b4b14b225cf513", "name": "测试总结", "source": "e3b4b14b225cf513.txt", "type": "text/plain", "size": 214}, {"uid": "ef33587bc85e4a74", "name": "test_completed", "source": "ef33587bc85e4a74.png", "type": "image/png", "size": 578395}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754397026261, "stop": 1754397026265, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397026265, "stop": 1754397026562, "duration": 297}, "status": "passed", "steps": [], "attachments": [{"uid": "7b1ebaf62557b24", "name": "测试总结", "source": "7b1ebaf62557b24.txt", "type": "text/plain", "size": 214}, {"uid": "36b8d07f8c52d5ea", "name": "test_completed", "source": "36b8d07f8c52d5ea.png", "type": "image/png", "size": 578395}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "bd39db6c17a2088f", "name": "stdout", "source": "bd39db6c17a2088f.txt", "type": "text/plain", "size": 11745}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397026565, "stop": 1754397026565, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397026566, "stop": 1754397027852, "duration": 1286}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_delete_the_8_o_clock_alarm"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_delete_the_8_o_clock_alarm"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "a6004a58bef8d00f.json", "parameterValues": []}