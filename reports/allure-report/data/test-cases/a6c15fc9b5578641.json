{"uid": "a6c15fc9b5578641", "name": "测试set gesture navigation返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_gesture_navigation.TestEllaSetGestureNavigation#test_set_gesture_navigation", "historyId": "e14cd5a605f26d24de7f5f63d4667c68", "time": {"start": 1754454607356, "stop": 1754454621555, "duration": 14199}, "description": "验证set gesture navigation指令返回预期的不支持响应", "descriptionHtml": "<p>验证set gesture navigation指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454594807, "stop": 1754454607355, "duration": 12548}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454607355, "stop": 1754454607355, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set gesture navigation指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set gesture navigation", "time": {"start": 1754454607356, "stop": 1754454621374, "duration": 14018}, "status": "passed", "steps": [{"name": "执行命令: set gesture navigation", "time": {"start": 1754454607356, "stop": 1754454621205, "duration": 13849}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454621205, "stop": 1754454621374, "duration": 169}, "status": "passed", "steps": [], "attachments": [{"uid": "795ee91655cfdd1c", "name": "测试总结", "source": "795ee91655cfdd1c.txt", "type": "text/plain", "size": 226}, {"uid": "b5eaebe7f4b9050", "name": "test_completed", "source": "b5eaebe7f4b9050.png", "type": "image/png", "size": 495828}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454621374, "stop": 1754454621375, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454621375, "stop": 1754454621554, "duration": 179}, "status": "passed", "steps": [], "attachments": [{"uid": "7ad7b86d7110a6b0", "name": "测试总结", "source": "7ad7b86d7110a6b0.txt", "type": "text/plain", "size": 226}, {"uid": "22c01383f063b49", "name": "test_completed", "source": "22c01383f063b49.png", "type": "image/png", "size": 495642}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "e221d02703813e71", "name": "stdout", "source": "e221d02703813e71.txt", "type": "text/plain", "size": 11766}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454621555, "stop": 1754454621555, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454621556, "stop": 1754454622950, "duration": 1394}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_gesture_navigation"}, {"name": "subSuite", "value": "TestEllaSetGestureNavigation"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_gesture_navigation"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "6528b6950a6c68ad", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404609497, "stop": 1754404624950, "duration": 15453}}], "categories": [], "tags": ["smoke"]}, "source": "a6c15fc9b5578641.json", "parameterValues": []}