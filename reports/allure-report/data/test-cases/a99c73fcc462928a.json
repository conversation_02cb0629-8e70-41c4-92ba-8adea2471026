{"uid": "a99c73fcc462928a", "name": "测试appeler maman能正常执行", "fullName": "testcases.test_ella.dialogue.test_appeler_maman.TestEllaAppelerMaman#test_appeler_maman", "historyId": "c5050ea089fe0f7a5b962119cd32b32e", "time": {"start": 1754447563860, "stop": 1754447577353, "duration": 13493}, "description": "appeler maman", "descriptionHtml": "<p>appeler maman</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447550804, "stop": 1754447563859, "duration": 13055}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447563859, "stop": 1754447563859, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "appeler maman", "status": "passed", "steps": [{"name": "执行命令: appeler maman", "time": {"start": 1754447563860, "stop": 1754447577141, "duration": 13281}, "status": "passed", "steps": [{"name": "执行命令: appeler maman", "time": {"start": 1754447563860, "stop": 1754447576930, "duration": 13070}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447576930, "stop": 1754447577140, "duration": 210}, "status": "passed", "steps": [], "attachments": [{"uid": "f8c4d880acc75bd9", "name": "测试总结", "source": "f8c4d880acc75bd9.txt", "type": "text/plain", "size": 192}, {"uid": "19eeab6a47669b06", "name": "test_completed", "source": "19eeab6a47669b06.png", "type": "image/png", "size": 591691}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447577141, "stop": 1754447577142, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447577142, "stop": 1754447577353, "duration": 211}, "status": "passed", "steps": [], "attachments": [{"uid": "8f3fb11609dcc455", "name": "测试总结", "source": "8f3fb11609dcc455.txt", "type": "text/plain", "size": 192}, {"uid": "17ac294fad0e42dd", "name": "test_completed", "source": "17ac294fad0e42dd.png", "type": "image/png", "size": 591504}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "2a2e425c653ff204", "name": "stdout", "source": "2a2e425c653ff204.txt", "type": "text/plain", "size": 11182}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447577353, "stop": 1754447577353, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447577354, "stop": 1754447578793, "duration": 1439}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_appeler_maman"}, {"name": "subSuite", "value": "Test<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_appeler_maman"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "73784dd66b275a1b", "status": "passed", "time": {"start": 1754397841751, "stop": 1754397854561, "duration": 12810}}], "categories": [], "tags": ["smoke"]}, "source": "a99c73fcc462928a.json", "parameterValues": []}