{"uid": "aa6637247543187a", "name": "测试set screen timeout返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_screen_timeout.TestEllaSetScreenTimeout#test_set_screen_timeout", "historyId": "e76af38ac3a594aa2b7d7173d57e98ad", "time": {"start": 1754454939113, "stop": 1754454952936, "duration": 13823}, "description": "验证set screen timeout指令返回预期的不支持响应", "descriptionHtml": "<p>验证set screen timeout指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454927266, "stop": 1754454939112, "duration": 11846}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454939113, "stop": 1754454939113, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set screen timeout指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set screen timeout", "time": {"start": 1754454939113, "stop": 1754454952722, "duration": 13609}, "status": "passed", "steps": [{"name": "执行命令: set screen timeout", "time": {"start": 1754454939113, "stop": 1754454952537, "duration": 13424}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454952537, "stop": 1754454952722, "duration": 185}, "status": "passed", "steps": [], "attachments": [{"uid": "2cf174cf3c51d856", "name": "测试总结", "source": "2cf174cf3c51d856.txt", "type": "text/plain", "size": 224}, {"uid": "f6495f0d04417f98", "name": "test_completed", "source": "f6495f0d04417f98.png", "type": "image/png", "size": 477689}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454952722, "stop": 1754454952724, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454952724, "stop": 1754454952936, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "29d1a9d00e848a25", "name": "测试总结", "source": "29d1a9d00e848a25.txt", "type": "text/plain", "size": 224}, {"uid": "cc2a098a89ad110e", "name": "test_completed", "source": "cc2a098a89ad110e.png", "type": "image/png", "size": 477577}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "4b92d0c166f5e602", "name": "stdout", "source": "4b92d0c166f5e602.txt", "type": "text/plain", "size": 11284}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454952937, "stop": 1754454952937, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454952938, "stop": 1754454954298, "duration": 1360}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_screen_timeout"}, {"name": "subSuite", "value": "TestEllaSetScreenTimeout"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_screen_timeout"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "10451cd4b3c7db1c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404948799, "stop": 1754404964797, "duration": 15998}}], "categories": [], "tags": ["smoke"]}, "source": "aa6637247543187a.json", "parameterValues": []}