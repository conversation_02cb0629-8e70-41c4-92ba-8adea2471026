{"uid": "aaf2e2359901cf45", "name": "测试whatsapp能正常执行", "fullName": "testcases.test_ella.third_coupling.test_whatsapp.TestEllaWhatsapp#test_whatsapp", "historyId": "fae56e9bcf9e0511ef4a7c93775731e3", "time": {"start": 1754451704338, "stop": 1754451719424, "duration": 15086}, "description": "whatsapp", "descriptionHtml": "<p>whatsapp</p>\n", "status": "broken", "statusMessage": "ValueError: too many values to unpack (expected 3)", "statusTrace": "self = <testcases.test_ella.third_coupling.test_whatsapp.TestEllaWhatsapp object at 0x000001E3894C2350>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B468650>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_whatsapp(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n>           initial_status, final_status, files_status = self.simple_command_test(\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\nE           ValueError: too many values to unpack (expected 3)\n\ntestcases\\test_ella\\third_coupling\\test_whatsapp.py:26: ValueError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451691614, "stop": 1754451704337, "duration": 12723}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451704337, "stop": 1754451704337, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "whatsapp", "status": "broken", "statusMessage": "ValueError: too many values to unpack (expected 3)", "statusTrace": "self = <testcases.test_ella.third_coupling.test_whatsapp.TestEllaWhatsapp object at 0x000001E3894C2350>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B468650>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_whatsapp(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n>           initial_status, final_status, files_status = self.simple_command_test(\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\nE           ValueError: too many values to unpack (expected 3)\n\ntestcases\\test_ella\\third_coupling\\test_whatsapp.py:26: ValueError", "steps": [{"name": "执行命令: whatsapp", "time": {"start": 1754451704338, "stop": 1754451719423, "duration": 15085}, "status": "broken", "statusMessage": "ValueError: too many values to unpack (expected 3)\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\third_coupling\\test_whatsapp.py\", line 26, in test_whatsapp\n    initial_status, final_status, files_status = self.simple_command_test(\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "steps": [{"name": "执行命令: whatsapp", "time": {"start": 1754451704338, "stop": 1754451719187, "duration": 14849}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451719188, "stop": 1754451719423, "duration": 235}, "status": "passed", "steps": [], "attachments": [{"uid": "9e62cc5cd4392a04", "name": "测试总结", "source": "9e62cc5cd4392a04.txt", "type": "text/plain", "size": 208}, {"uid": "2c4a66273f9f7657", "name": "test_completed", "source": "2c4a66273f9f7657.png", "type": "image/png", "size": 471341}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 2}], "attachments": [{"uid": "a566a6f0f5b91a1a", "name": "stdout", "source": "a566a6f0f5b91a1a.txt", "type": "text/plain", "size": 11296}], "parameters": [], "attachmentStep": false, "stepsCount": 3, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451719427, "stop": 1754451719618, "duration": 191}, "status": "passed", "steps": [], "attachments": [{"uid": "377930e87e481be3", "name": "失败截图-TestEllaWhatsapp", "source": "377930e87e481be3.png", "type": "image/png", "size": 470470}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754451719619, "stop": 1754451720992, "duration": 1373}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_whatsapp"}, {"name": "subSuite", "value": "TestEllaW<PERSON>sapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_whatsapp"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "77a207bf58510ea3", "status": "broken", "statusDetails": "ValueError: too many values to unpack (expected 3)", "time": {"start": 1754401843149, "stop": 1754401855503, "duration": 12354}}], "categories": [{"name": "Test defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "aaf2e2359901cf45.json", "parameterValues": []}