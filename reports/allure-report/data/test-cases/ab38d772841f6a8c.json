{"uid": "ab38d772841f6a8c", "name": "测试set app notifications返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_app_notifications.TestEllaSetAppNotifications#test_set_app_notifications", "historyId": "9458f45d3c37d9141658da9964a470f5", "time": {"start": 1754454206931, "stop": 1754454221093, "duration": 14162}, "description": "验证set app notifications指令返回预期的不支持响应", "descriptionHtml": "<p>验证set app notifications指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454194530, "stop": 1754454206930, "duration": 12400}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454206930, "stop": 1754454206930, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set app notifications指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set app notifications", "time": {"start": 1754454206931, "stop": 1754454220929, "duration": 13998}, "status": "passed", "steps": [{"name": "执行命令: set app notifications", "time": {"start": 1754454206931, "stop": 1754454220711, "duration": 13780}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454220711, "stop": 1754454220928, "duration": 217}, "status": "passed", "steps": [], "attachments": [{"uid": "8b4041639cd1bab2", "name": "测试总结", "source": "8b4041639cd1bab2.txt", "type": "text/plain", "size": 233}, {"uid": "b7aaf0bb8f2053a7", "name": "test_completed", "source": "b7aaf0bb8f2053a7.png", "type": "image/png", "size": 491967}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454220929, "stop": 1754454220930, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454220930, "stop": 1754454221093, "duration": 163}, "status": "passed", "steps": [], "attachments": [{"uid": "c56fd3560c3595b6", "name": "测试总结", "source": "c56fd3560c3595b6.txt", "type": "text/plain", "size": 233}, {"uid": "7019dd5d5f8af53c", "name": "test_completed", "source": "7019dd5d5f8af53c.png", "type": "image/png", "size": 490910}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "af2d7403719ba256", "name": "stdout", "source": "af2d7403719ba256.txt", "type": "text/plain", "size": 11326}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754454221094, "stop": 1754454222543, "duration": 1449}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754454221094, "stop": 1754454221094, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_app_notifications"}, {"name": "subSuite", "value": "TestEllaSetAppNotifications"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_app_notifications"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "56effd5d424bdbc7", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404213403, "stop": 1754404228453, "duration": 15050}}], "categories": [], "tags": ["smoke"]}, "source": "ab38d772841f6a8c.json", "parameterValues": []}