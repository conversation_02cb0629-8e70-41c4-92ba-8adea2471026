{"uid": "ad16fe59d0da691", "name": "测试play rock music", "fullName": "testcases.test_ella.component_coupling.test_play_rock_music.TestEllaOpenVisha#test_play_rock_music", "historyId": "6075008522e5d0ae1667c4ac4be759eb", "time": {"start": 1754397570511, "stop": 1754397586519, "duration": 16008}, "description": "测试play rock music指令", "descriptionHtml": "<p>测试play rock music指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397557663, "stop": 1754397570509, "duration": 12846}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397570509, "stop": 1754397570509, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play rock music指令", "status": "passed", "steps": [{"name": "执行命令: play rock music", "time": {"start": 1754397570511, "stop": 1754397586270, "duration": 15759}, "status": "passed", "steps": [{"name": "执行命令: play rock music", "time": {"start": 1754397570511, "stop": 1754397585981, "duration": 15470}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397585981, "stop": 1754397586269, "duration": 288}, "status": "passed", "steps": [], "attachments": [{"uid": "99308f0a9f7734d3", "name": "测试总结", "source": "99308f0a9f7734d3.txt", "type": "text/plain", "size": 157}, {"uid": "1a7c025156d22da4", "name": "test_completed", "source": "1a7c025156d22da4.png", "type": "image/png", "size": 488135}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754397586270, "stop": 1754397586274, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证visha已打开", "time": {"start": 1754397586274, "stop": 1754397586274, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397586274, "stop": 1754397586518, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "ba5f0aa824781155", "name": "测试总结", "source": "ba5f0aa824781155.txt", "type": "text/plain", "size": 157}, {"uid": "f73a147e281028fb", "name": "test_completed", "source": "f73a147e281028fb.png", "type": "image/png", "size": 489113}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "ca9a3d23e084a7d9", "name": "stdout", "source": "ca9a3d23e084a7d9.txt", "type": "text/plain", "size": 13520}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397586520, "stop": 1754397586520, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397586522, "stop": 1754397587795, "duration": 1273}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_rock_music"}, {"name": "subSuite", "value": "TestEllaOpen<PERSON>a"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_rock_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "ad16fe59d0da691.json", "parameterValues": []}