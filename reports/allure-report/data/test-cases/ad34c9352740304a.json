{"uid": "ad34c9352740304a", "name": "测试pls open the newest whatsapp activity", "fullName": "testcases.test_ella.third_coupling.test_pls_open_the_newest_whatsapp_activity.TestEllaOpenPlsNewestWhatsappActivity#test_pls_open_the_newest_whatsapp_activity", "historyId": "fc477656b55eea3a3906a5bdcaa93554", "time": {"start": 1754451675001, "stop": 1754451690219, "duration": 15218}, "description": "测试pls open the newest whatsapp activity指令", "descriptionHtml": "<p>测试pls open the newest whatsapp activity指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451662371, "stop": 1754451675000, "duration": 12629}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451675000, "stop": 1754451675000, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试pls open the newest whatsapp activity指令", "status": "passed", "steps": [{"name": "执行命令: pls open the newest whatsapp activity", "time": {"start": 1754451675001, "stop": 1754451690044, "duration": 15043}, "status": "passed", "steps": [{"name": "执行命令: pls open the newest whatsapp activity", "time": {"start": 1754451675001, "stop": 1754451689842, "duration": 14841}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451689842, "stop": 1754451690044, "duration": 202}, "status": "passed", "steps": [], "attachments": [{"uid": "363bea6ab0bb1d73", "name": "测试总结", "source": "363bea6ab0bb1d73.txt", "type": "text/plain", "size": 238}, {"uid": "ac874a6ae9068eff", "name": "test_completed", "source": "ac874a6ae9068eff.png", "type": "image/png", "size": 515042}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451690044, "stop": 1754451690045, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451690045, "stop": 1754451690219, "duration": 174}, "status": "passed", "steps": [], "attachments": [{"uid": "22df3230c44abe00", "name": "测试总结", "source": "22df3230c44abe00.txt", "type": "text/plain", "size": 238}, {"uid": "74ce48872f9ff120", "name": "test_completed", "source": "74ce48872f9ff120.png", "type": "image/png", "size": 514015}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3d7dbdc0460adf22", "name": "stdout", "source": "3d7dbdc0460adf22.txt", "type": "text/plain", "size": 11981}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451690220, "stop": 1754451690220, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451690221, "stop": 1754451691609, "duration": 1388}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_pls_open_the_newest_whatsapp_activity"}, {"name": "subSuite", "value": "TestEllaOpenPlsNewestWhatsappActivity"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_pls_open_the_newest_whatsapp_activity"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "c1aa2c3ecd7e41d8", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['I need to download WhatsApp to continue']\nassert False", "time": {"start": 1754401815867, "stop": 1754401828049, "duration": 12182}}], "categories": [], "tags": ["smoke"]}, "source": "ad34c9352740304a.json", "parameterValues": []}