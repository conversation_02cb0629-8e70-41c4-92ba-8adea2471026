{"uid": "aec6e1a96ee9d4f5", "name": "测试Search for addresses on the screen能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen.TestEllaSearchAddressesScreen#test_search_for_addresses_on_the_screen", "historyId": "8814f1dafa698e785ee1f58faa6e745d", "time": {"start": 1754454028008, "stop": 1754454043073, "duration": 15065}, "description": "Search for addresses on the screen", "descriptionHtml": "<p>Search for addresses on the screen</p>\n", "status": "failed", "statusMessage": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', \"12:20 Dialogue Explore <PERSON><PERSON><PERSON> at UFC 108 What is Ask About Screen? <PERSON> Backs <PERSON>'s Ad, Critiques Swift Search for addresses on the screen I am sorry, I am unable to search for addresses on the screen. Generated by AI, for reference only Artemis program timeline SpaceX lunar missions Moon mission costs analysis DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert None", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen.TestEllaSearchAddressesScreen object at 0x000001E38960DF10>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B872790>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_search_for_addresses_on_the_screen(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     expected_text = self.expected_text\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证GoogleMap应用已打开\"):\n>           assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', \"12:20 Dialogue Explore Rebecki Stuns Duncan at UFC 108 What is Ask About Screen? Trump Backs Sweeney's Ad, Critiques Swift Search for addresses on the screen I am sorry, I am unable to search for addresses on the screen. Generated by AI, for reference only Artemis program timeline SpaceX lunar missions Moon mission costs analysis DeepSeek-R1 Feel free to ask me any questions…\"]'\nE           assert None\n\ntestcases\\test_ella\\unsupported_commands\\test_search_for_addresses_on_the_screen.py:36: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454015654, "stop": 1754454028006, "duration": 12352}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454028006, "stop": 1754454028006, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "Search for addresses on the screen", "status": "failed", "statusMessage": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', \"12:20 Dialogue Explore <PERSON><PERSON><PERSON> at UFC 108 What is Ask About Screen? <PERSON> Backs <PERSON>'s Ad, Critiques Swift Search for addresses on the screen I am sorry, I am unable to search for addresses on the screen. Generated by AI, for reference only Artemis program timeline SpaceX lunar missions Moon mission costs analysis DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert None", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen.TestEllaSearchAddressesScreen object at 0x000001E38960DF10>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B872790>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_search_for_addresses_on_the_screen(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     expected_text = self.expected_text\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证GoogleMap应用已打开\"):\n>           assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', \"12:20 Dialogue Explore Rebecki Stuns Duncan at UFC 108 What is Ask About Screen? Trump Backs Sweeney's Ad, Critiques Swift Search for addresses on the screen I am sorry, I am unable to search for addresses on the screen. Generated by AI, for reference only Artemis program timeline SpaceX lunar missions Moon mission costs analysis DeepSeek-R1 Feel free to ask me any questions…\"]'\nE           assert None\n\ntestcases\\test_ella\\unsupported_commands\\test_search_for_addresses_on_the_screen.py:36: AssertionError", "steps": [{"name": "执行命令: Search for addresses on the screen", "time": {"start": 1754454028008, "stop": 1754454043072, "duration": 15064}, "status": "passed", "steps": [{"name": "执行命令: Search for addresses on the screen", "time": {"start": 1754454028008, "stop": 1754454042867, "duration": 14859}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454042867, "stop": 1754454043072, "duration": 205}, "status": "passed", "steps": [], "attachments": [{"uid": "3554265d9a7288fa", "name": "测试总结", "source": "3554265d9a7288fa.txt", "type": "text/plain", "size": 574}, {"uid": "70373c490b64ba7a", "name": "test_completed", "source": "70373c490b64ba7a.png", "type": "image/png", "size": 484922}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证GoogleMap应用已打开", "time": {"start": 1754454043072, "stop": 1754454043072, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', \"12:20 Dialogue Explore <PERSON><PERSON><PERSON> at UFC 108 What is Ask About Screen? <PERSON> Backs <PERSON>'s Ad, Critiques Swift Search for addresses on the screen I am sorry, I am unable to search for addresses on the screen. Generated by AI, for reference only Artemis program timeline SpaceX lunar missions Moon mission costs analysis DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert None\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_search_for_addresses_on_the_screen.py\", line 36, in test_search_for_addresses_on_the_screen\n    assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "923e1b9470b25ea1", "name": "stdout", "source": "923e1b9470b25ea1.txt", "type": "text/plain", "size": 12975}], "parameters": [], "attachmentStep": false, "stepsCount": 4, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454043077, "stop": 1754454043288, "duration": 211}, "status": "passed", "steps": [], "attachments": [{"uid": "86a855548bc222f4", "name": "失败截图-TestEllaSearchAddressesScreen", "source": "86a855548bc222f4.png", "type": "image/png", "size": 483639}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754454043289, "stop": 1754454044736, "duration": 1447}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_search_for_addresses_on_the_screen"}, {"name": "subSuite", "value": "TestEllaSearchAddressesScreen"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "b48c90dea488094b", "status": "failed", "statusDetails": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', '如果你想在屏幕上搜索地址，以下是一些可行的方法：\\n\\n使用手机浏览器 ：在大多数手机浏览器中，地址栏通常位于屏幕顶部 ，你可以直接在地址栏中输入你要搜索的地址，浏览器会自动识别并给出相应的结果。部分浏览器还会在地址栏中根据你输入的内容提供丰富的相关搜索建议1。\\n使用图片文字识别工具 ：如果地址在图片上，你可以借助一些图片文字识别工具来提取文字信息，进而获取地址。比如，微信长按图片，选择“提取文字”选项，即可提取出图片中的文字，找到你需要的地址5 ；QQ也是类似操作，在QQ里找到需要提取文字的图片并点击放大，再用手指长按图片，等待下方菜单栏跳转出来，找到“提取文字”选项即可2 ；还有一些专业的图片处理软件，如迅捷图片转换器、WPS、搜狗输', '10:27 对话 发现 10 篇参考资料 如果你想在屏幕上搜索地址，以下是一些可行的方法：&#10;&#10;使用手机浏览器 ：在大多数手机浏览器中，地址栏通常位于屏幕顶部 ，你可以直接在地址栏中输入你要搜索的地址，浏览器会自动识别并给出相应的结果。部分浏览器还会在地址栏中根据你输入的内容提供丰富的相关搜索建议1。&#10;使用图片文字识别工具 ：如果地址在图片上，你可以借助一些图片文字识别工具来提取文字信息，进而获取地址。比如，微信长按图片，选择“提取文字”选项，即可提取出图片中的文字，找到你需要的地址5 ；QQ也是类似操作，在QQ里找到需要提取文字的图片并点击放大，再用手指长按图片，等待下方菜单栏跳转出来，找到“提取文字”选项即可2 ；还有一些专业的图片处理软件，如迅捷图片转换器、WPS、搜狗输入法、Adobe Acrobat Pro DC等，也能帮 AI生成，仅作参考 DeepSeek-R1 有问题尽管问我…']'\nassert None", "time": {"start": 1754404041726, "stop": 1754404056282, "duration": 14556}}], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "aec6e1a96ee9d4f5.json", "parameterValues": []}