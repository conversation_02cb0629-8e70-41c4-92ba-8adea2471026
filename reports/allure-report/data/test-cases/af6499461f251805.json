{"uid": "af6499461f251805", "name": "测试wake me up at 7:00 am tomorrow能正常执行", "fullName": "testcases.test_ella.system_coupling.test_wake_me_up_at_am_tomorrow.TestEllaWakeMeUpAmTomorrow#test_wake_me_up_at_am_tomorrow", "historyId": "********************************", "time": {"start": 1754451240559, "stop": 1754451254442, "duration": 13883}, "description": "wake me up at 7:00 am tomorrow", "descriptionHtml": "<p>wake me up at 7:00 am tomorrow</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451228079, "stop": 1754451240558, "duration": 12479}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451240558, "stop": 1754451240558, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "wake me up at 7:00 am tomorrow", "status": "passed", "steps": [{"name": "执行命令: wake me up at 7:00 am tomorrow", "time": {"start": 1754451240559, "stop": 1754451254272, "duration": 13713}, "status": "passed", "steps": [{"name": "执行命令: wake me up at 7:00 am tomorrow", "time": {"start": 1754451240559, "stop": 1754451254081, "duration": 13522}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451254081, "stop": 1754451254272, "duration": 191}, "status": "passed", "steps": [], "attachments": [{"uid": "20f802bd79c0ae3", "name": "测试总结", "source": "20f802bd79c0ae3.txt", "type": "text/plain", "size": 217}, {"uid": "e3c591b6c8a4b91b", "name": "test_completed", "source": "e3c591b6c8a4b91b.png", "type": "image/png", "size": 380347}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451254272, "stop": 1754451254274, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451254274, "stop": 1754451254442, "duration": 168}, "status": "passed", "steps": [], "attachments": [{"uid": "9d3c02c2463fc21a", "name": "测试总结", "source": "9d3c02c2463fc21a.txt", "type": "text/plain", "size": 217}, {"uid": "17968eddb299fc69", "name": "test_completed", "source": "17968eddb299fc69.png", "type": "image/png", "size": 380772}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "be6f21b323da22e2", "name": "stdout", "source": "be6f21b323da22e2.txt", "type": "text/plain", "size": 11574}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754451254443, "stop": 1754451255794, "duration": 1351}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754451254443, "stop": 1754451254443, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_wake_me_up_at_am_tomorrow"}, {"name": "subSuite", "value": "TestEllaWakeMeUpAmTomorrow"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_wake_me_up_at_am_tomorrow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "66f0cdbcc09a1110", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The alarm']\nassert False", "time": {"start": 1754401396308, "stop": 1754401408555, "duration": 12247}}], "categories": [], "tags": ["smoke"]}, "source": "af6499461f251805.json", "parameterValues": []}