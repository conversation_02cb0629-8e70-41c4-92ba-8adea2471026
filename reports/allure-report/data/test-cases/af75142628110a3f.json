{"uid": "af75142628110a3f", "name": "测试stop workout能正常执行", "fullName": "testcases.test_ella.dialogue.test_stop_workout.TestEllaStopWorkout#test_stop_workout", "historyId": "95e68fb1d20b8d7ff190c67b0bbc2ee8", "time": {"start": 1754398883448, "stop": 1754398896386, "duration": 12938}, "description": "stop workout", "descriptionHtml": "<p>stop workout</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398870227, "stop": 1754398883447, "duration": 13220}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398883447, "stop": 1754398883447, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "stop workout", "status": "passed", "steps": [{"name": "执行命令: stop workout", "time": {"start": 1754398883448, "stop": 1754398896090, "duration": 12642}, "status": "passed", "steps": [{"name": "执行命令: stop workout", "time": {"start": 1754398883448, "stop": 1754398895855, "duration": 12407}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398895855, "stop": 1754398896088, "duration": 233}, "status": "passed", "steps": [], "attachments": [{"uid": "584c65fedab77f26", "name": "测试总结", "source": "584c65fedab77f26.txt", "type": "text/plain", "size": 181}, {"uid": "8cce6b01f8b32d1", "name": "test_completed", "source": "8cce6b01f8b32d1.png", "type": "image/png", "size": 570301}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398896090, "stop": 1754398896094, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398896094, "stop": 1754398896385, "duration": 291}, "status": "passed", "steps": [], "attachments": [{"uid": "5dc5642c67da41af", "name": "测试总结", "source": "5dc5642c67da41af.txt", "type": "text/plain", "size": 181}, {"uid": "7af27f8221a20bc9", "name": "test_completed", "source": "7af27f8221a20bc9.png", "type": "image/png", "size": 570637}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "45afd642a8ad95b", "name": "stdout", "source": "45afd642a8ad95b.txt", "type": "text/plain", "size": 11157}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398896388, "stop": 1754398896388, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398896391, "stop": 1754398897668, "duration": 1277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_stop_workout"}, {"name": "subSuite", "value": "TestEllaStopWorkout"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_stop_workout"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "af75142628110a3f.json", "parameterValues": []}