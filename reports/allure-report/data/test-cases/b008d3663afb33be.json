{"uid": "b008d3663afb33be", "name": "测试set sim1 ringtone返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_sim_ringtone.TestEllaSetSimRingtone#test_set_sim_ringtone", "historyId": "a5a3cc08eb97e600c97acb65a7439ec0", "time": {"start": 1754454993624, "stop": 1754455007813, "duration": 14189}, "description": "验证set sim1 ringtone指令返回预期的不支持响应", "descriptionHtml": "<p>验证set sim1 ringtone指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454981765, "stop": 1754454993624, "duration": 11859}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454993624, "stop": 1754454993624, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set sim1 ringtone指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set sim1 ringtone", "time": {"start": 1754454993625, "stop": 1754455007624, "duration": 13999}, "status": "passed", "steps": [{"name": "执行命令: set sim1 ringtone", "time": {"start": 1754454993625, "stop": 1754455007427, "duration": 13802}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455007427, "stop": 1754455007624, "duration": 197}, "status": "passed", "steps": [], "attachments": [{"uid": "ae9e7bc0c2d34e9c", "name": "测试总结", "source": "ae9e7bc0c2d34e9c.txt", "type": "text/plain", "size": 221}, {"uid": "70fe9a85e325ad37", "name": "test_completed", "source": "70fe9a85e325ad37.png", "type": "image/png", "size": 487361}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455007624, "stop": 1754455007626, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455007626, "stop": 1754455007812, "duration": 186}, "status": "passed", "steps": [], "attachments": [{"uid": "38ba69eeed1442ab", "name": "测试总结", "source": "38ba69eeed1442ab.txt", "type": "text/plain", "size": 221}, {"uid": "495bc32f357177e8", "name": "test_completed", "source": "495bc32f357177e8.png", "type": "image/png", "size": 486289}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "cf7eb5c34a25ba86", "name": "stdout", "source": "cf7eb5c34a25ba86.txt", "type": "text/plain", "size": 11268}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455007813, "stop": 1754455007813, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455007814, "stop": 1754455009103, "duration": 1289}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_sim_ringtone"}, {"name": "subSuite", "value": "TestEllaSetSimRingtone"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_sim_ringtone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "5cec133f5450d84e", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405011273, "stop": 1754405027766, "duration": 16493}}], "categories": [], "tags": ["smoke"]}, "source": "b008d3663afb33be.json", "parameterValues": []}