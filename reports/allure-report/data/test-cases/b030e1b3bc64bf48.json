{"uid": "b030e1b3bc64bf48", "name": "测试who is harry potter能正常执行", "fullName": "testcases.test_ella.dialogue.test_who_is_harry_potter.TestEllaWhoIsHarryPotter#test_who_is_harry_potter", "historyId": "56a09613cdb882018377e1c2c4e78472", "time": {"start": 1754449163121, "stop": 1754449179047, "duration": 15926}, "description": "who is harry potter", "descriptionHtml": "<p>who is harry potter</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449150739, "stop": 1754449163121, "duration": 12382}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449163121, "stop": 1754449163121, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "who is harry potter", "status": "passed", "steps": [{"name": "执行命令: who is harry potter", "time": {"start": 1754449163123, "stop": 1754449178820, "duration": 15697}, "status": "passed", "steps": [{"name": "执行命令: who is harry potter", "time": {"start": 1754449163123, "stop": 1754449178602, "duration": 15479}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449178602, "stop": 1754449178820, "duration": 218}, "status": "passed", "steps": [], "attachments": [{"uid": "cfecf603f35fe577", "name": "测试总结", "source": "cfecf603f35fe577.txt", "type": "text/plain", "size": 792}, {"uid": "60741681542332c5", "name": "test_completed", "source": "60741681542332c5.png", "type": "image/png", "size": 708220}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754449178820, "stop": 1754449178821, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449178821, "stop": 1754449179046, "duration": 225}, "status": "passed", "steps": [], "attachments": [{"uid": "ee15ffb9a749ba3b", "name": "测试总结", "source": "ee15ffb9a749ba3b.txt", "type": "text/plain", "size": 792}, {"uid": "c74a8bb6b0ede3bf", "name": "test_completed", "source": "c74a8bb6b0ede3bf.png", "type": "image/png", "size": 708017}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3be5016f5976a510", "name": "stdout", "source": "3be5016f5976a510.txt", "type": "text/plain", "size": 14408}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449179047, "stop": 1754449179047, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449179048, "stop": 1754449180472, "duration": 1424}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_who_is_harry_potter"}, {"name": "subSuite", "value": "TestEllaWhoIsHarry<PERSON>otter"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_who_is_harry_potter"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "4bf08d2af72213b8", "status": "passed", "time": {"start": 1754399353992, "stop": 1754399369403, "duration": 15411}}], "categories": [], "tags": ["smoke"]}, "source": "b030e1b3bc64bf48.json", "parameterValues": []}