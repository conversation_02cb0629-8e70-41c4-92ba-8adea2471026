{"uid": "b355a4d55fe3cc24", "name": "测试fly to the moon返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_fly_to_the_moon.TestEllaFlyMoon#test_fly_to_the_moon", "historyId": "c0f5aa9491c612f6ca4b065dfff62ae7", "time": {"start": 1754402932044, "stop": 1754402945598, "duration": 13554}, "description": "验证fly to the moon指令返回预期的不支持响应", "descriptionHtml": "<p>验证fly to the moon指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "AssertionError: 响应未包含期望内容: [\"Sorry, I can't help with that\"]\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_fly_to_the_moon.TestEllaFlyMoon object at 0x00000240FF2A56D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000024082F503D0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_fly_to_the_moon(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n>           result = self.verify_expected_in_response(expected_text, response_text)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntestcases\\test_ella\\unsupported_commands\\test_fly_to_the_moon.py:32: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <testcases.test_ella.unsupported_commands.test_fly_to_the_moon.TestEllaFlyMoon object at 0x00000240FF2A56D0>\nexpected_text = [\"Sorry, I can't help with that\"]\nresponse_text = ['fly to the moon', '', '', '', '目前我没办法直接帮你实现飞到月球的操作啦。不过要是从现实角度来说，飞向月球可不是一件简单的事，需要借助像火箭这样的航天器，经过一系列复杂的训练和准备工作才行。你怎么突然想...件简单的事，需要借助像火箭这样的航天器，经过一系列复杂的训练和准备工作才行。你怎么突然想到要飞向月球呀，是对太空探索很感兴趣吗 ？ AI生成，仅作参考 太空探索 宇宙飞船 登月计划 DeepSeek-R1 有问题尽管问我… 10:09']\n\n    def verify_expected_in_response(self, expected_text, response_text):\n        \"\"\"\n        验证期望内容是否在响应中\n    \n        Args:\n            expected_text: 期望的文本内容，可以是字符串或字符串列表\n            response_text: 响应文本，可以是字符串或字符串列表\n    \n        Returns:\n            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含\n        \"\"\"\n        log.info(f\"verify_expected_in_response 响应类型: {type(response_text)}, 内容: {response_text}\")\n    \n        # 处理 expected_text 参数\n        if isinstance(expected_text, str):\n            expected_list = [expected_text]\n        elif isinstance(expected_text, list):\n            expected_list = expected_text\n        else:\n            log.error(f\"❌ expected_text类型错误: {type(expected_text)}\")\n            return False\n    \n        # 处理 response_text 参数，统一转换为字符串进行搜索\n        if isinstance(response_text, str):\n            # 如果是字符串，直接使用\n            search_text = response_text\n            log.debug(f\"响应文本(字符串): {search_text}\")\n        elif isinstance(response_text, list):\n            # 如果是列表，过滤空值并合并为一个字符串\n            filtered_texts = [text for text in response_text if text and text.strip()]\n            search_text = \" \".join(filtered_texts)\n            log.debug(f\"响应文本(列表转换): 原始列表={response_text}, 过滤后={filtered_texts}, 合并后={search_text}\")\n        else:\n            log.error(f\"❌ response_text类型错误: {type(response_text)}\")\n            return False\n    \n        # 如果合并后的文本为空，记录警告\n        if not search_text or not search_text.strip():\n            log.warning(\"⚠️ 响应文本为空或只包含空白字符\")\n            search_text = \"\"\n    \n        # 记录所有验证结果\n        all_found = True\n        found_items = []\n        missing_items = []\n    \n        # 遍历所有期望内容\n        for expected_item in expected_list:\n            if not expected_item or not expected_item.strip():\n                log.warning(f\"⚠️ 跳过空的期望内容: '{expected_item}'\")\n                continue\n    \n            # 在合并的文本中搜索\n            if expected_item.lower() in search_text.lower():\n                found_items.append(expected_item)\n                log.info(f\"✅ 响应包含期望内容: '{expected_item}'\")\n            else:\n                missing_items.append(expected_item)\n                log.warning(f\"⚠️ 响应未包含期望内容: '{expected_item}'\")\n                all_found = False\n    \n        # 输出总结\n        if all_found:\n            log.info(f\"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})\")\n        else:\n            log.warning(f\"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})\")\n            log.warning(f\"缺失内容: {missing_items}\")\n            log.warning(f\"搜索文本: '{search_text}'\")\n    \n>       assert all_found, f\"响应未包含期望内容: {missing_items}\"\nE       AssertionError: 响应未包含期望内容: [\"Sorry, I can't help with that\"]\nE       assert False\n\ntestcases\\test_ella\\base_ella_test.py:923: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754402919201, "stop": 1754402932043, "duration": 12842}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754402932043, "stop": 1754402932043, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证fly to the moon指令返回预期的不支持响应", "status": "failed", "statusMessage": "AssertionError: 响应未包含期望内容: [\"Sorry, I can't help with that\"]\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_fly_to_the_moon.TestEllaFlyMoon object at 0x00000240FF2A56D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000024082F503D0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_fly_to_the_moon(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n>           result = self.verify_expected_in_response(expected_text, response_text)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntestcases\\test_ella\\unsupported_commands\\test_fly_to_the_moon.py:32: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <testcases.test_ella.unsupported_commands.test_fly_to_the_moon.TestEllaFlyMoon object at 0x00000240FF2A56D0>\nexpected_text = [\"Sorry, I can't help with that\"]\nresponse_text = ['fly to the moon', '', '', '', '目前我没办法直接帮你实现飞到月球的操作啦。不过要是从现实角度来说，飞向月球可不是一件简单的事，需要借助像火箭这样的航天器，经过一系列复杂的训练和准备工作才行。你怎么突然想...件简单的事，需要借助像火箭这样的航天器，经过一系列复杂的训练和准备工作才行。你怎么突然想到要飞向月球呀，是对太空探索很感兴趣吗 ？ AI生成，仅作参考 太空探索 宇宙飞船 登月计划 DeepSeek-R1 有问题尽管问我… 10:09']\n\n    def verify_expected_in_response(self, expected_text, response_text):\n        \"\"\"\n        验证期望内容是否在响应中\n    \n        Args:\n            expected_text: 期望的文本内容，可以是字符串或字符串列表\n            response_text: 响应文本，可以是字符串或字符串列表\n    \n        Returns:\n            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含\n        \"\"\"\n        log.info(f\"verify_expected_in_response 响应类型: {type(response_text)}, 内容: {response_text}\")\n    \n        # 处理 expected_text 参数\n        if isinstance(expected_text, str):\n            expected_list = [expected_text]\n        elif isinstance(expected_text, list):\n            expected_list = expected_text\n        else:\n            log.error(f\"❌ expected_text类型错误: {type(expected_text)}\")\n            return False\n    \n        # 处理 response_text 参数，统一转换为字符串进行搜索\n        if isinstance(response_text, str):\n            # 如果是字符串，直接使用\n            search_text = response_text\n            log.debug(f\"响应文本(字符串): {search_text}\")\n        elif isinstance(response_text, list):\n            # 如果是列表，过滤空值并合并为一个字符串\n            filtered_texts = [text for text in response_text if text and text.strip()]\n            search_text = \" \".join(filtered_texts)\n            log.debug(f\"响应文本(列表转换): 原始列表={response_text}, 过滤后={filtered_texts}, 合并后={search_text}\")\n        else:\n            log.error(f\"❌ response_text类型错误: {type(response_text)}\")\n            return False\n    \n        # 如果合并后的文本为空，记录警告\n        if not search_text or not search_text.strip():\n            log.warning(\"⚠️ 响应文本为空或只包含空白字符\")\n            search_text = \"\"\n    \n        # 记录所有验证结果\n        all_found = True\n        found_items = []\n        missing_items = []\n    \n        # 遍历所有期望内容\n        for expected_item in expected_list:\n            if not expected_item or not expected_item.strip():\n                log.warning(f\"⚠️ 跳过空的期望内容: '{expected_item}'\")\n                continue\n    \n            # 在合并的文本中搜索\n            if expected_item.lower() in search_text.lower():\n                found_items.append(expected_item)\n                log.info(f\"✅ 响应包含期望内容: '{expected_item}'\")\n            else:\n                missing_items.append(expected_item)\n                log.warning(f\"⚠️ 响应未包含期望内容: '{expected_item}'\")\n                all_found = False\n    \n        # 输出总结\n        if all_found:\n            log.info(f\"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})\")\n        else:\n            log.warning(f\"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})\")\n            log.warning(f\"缺失内容: {missing_items}\")\n            log.warning(f\"搜索文本: '{search_text}'\")\n    \n>       assert all_found, f\"响应未包含期望内容: {missing_items}\"\nE       AssertionError: 响应未包含期望内容: [\"Sorry, I can't help with that\"]\nE       assert False\n\ntestcases\\test_ella\\base_ella_test.py:923: AssertionError", "steps": [{"name": "执行命令: fly to the moon", "time": {"start": 1754402932044, "stop": 1754402945588, "duration": 13544}, "status": "passed", "steps": [{"name": "执行命令: fly to the moon", "time": {"start": 1754402932044, "stop": 1754402945294, "duration": 13250}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754402945294, "stop": 1754402945586, "duration": 292}, "status": "passed", "steps": [], "attachments": [{"uid": "a4149fbace151b74", "name": "测试总结", "source": "a4149fbace151b74.txt", "type": "text/plain", "size": 1021}, {"uid": "935a44c2aeb3ede6", "name": "test_completed", "source": "935a44c2aeb3ede6.png", "type": "image/png", "size": 660048}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754402945588, "stop": 1754402945595, "duration": 7}, "status": "failed", "statusMessage": "AssertionError: 响应未包含期望内容: [\"Sorry, I can't help with that\"]\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_fly_to_the_moon.py\", line 32, in test_fly_to_the_moon\n    result = self.verify_expected_in_response(expected_text, response_text)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 923, in verify_expected_in_response\n    assert all_found, f\"响应未包含期望内容: {missing_items}\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "4d7e966d8b3bbca7", "name": "stdout", "source": "4d7e966d8b3bbca7.txt", "type": "text/plain", "size": 17391}], "parameters": [], "attachmentStep": false, "stepsCount": 4, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754402945642, "stop": 1754402945912, "duration": 270}, "status": "passed", "steps": [], "attachments": [{"uid": "273389a81069d8de", "name": "失败截图-TestEllaFlyMoon", "source": "273389a81069d8de.png", "type": "image/png", "size": 659358}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754402945914, "stop": 1754402947240, "duration": 1326}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_fly_to_the_moon"}, {"name": "subSuite", "value": "TestEllaFlyMoon"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_fly_to_the_moon"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "b355a4d55fe3cc24.json", "parameterValues": []}