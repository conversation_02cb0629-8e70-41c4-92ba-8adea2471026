{"uid": "b3f6ff1329da975e", "name": "测试summarize what i'm reading能正常执行", "fullName": "testcases.test_ella.dialogue.test_summarize_what_i_m_reading.TestEllaSummarizeWhatIMReading#test_summarize_what_i_m_reading", "historyId": "edb3a77ed85c79b290dd8cce24f372c0", "time": {"start": 1754448740309, "stop": 1754448753925, "duration": 13616}, "description": "summarize what i'm reading", "descriptionHtml": "<p>summarize what i'm reading</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448727477, "stop": 1754448740308, "duration": 12831}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448740308, "stop": 1754448740308, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "summarize what i'm reading", "status": "passed", "steps": [{"name": "执行命令: summarize what i'm reading", "time": {"start": 1754448740309, "stop": 1754448753728, "duration": 13419}, "status": "passed", "steps": [{"name": "执行命令: summarize what i'm reading", "time": {"start": 1754448740309, "stop": 1754448753513, "duration": 13204}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448753513, "stop": 1754448753727, "duration": 214}, "status": "passed", "steps": [], "attachments": [{"uid": "c7bbaf51e431a5dd", "name": "测试总结", "source": "c7bbaf51e431a5dd.txt", "type": "text/plain", "size": 269}, {"uid": "99b034cd4e8c2610", "name": "test_completed", "source": "99b034cd4e8c2610.png", "type": "image/png", "size": 629807}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448753728, "stop": 1754448753728, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448753728, "stop": 1754448753924, "duration": 196}, "status": "passed", "steps": [], "attachments": [{"uid": "d8bf5c7457fed617", "name": "测试总结", "source": "d8bf5c7457fed617.txt", "type": "text/plain", "size": 269}, {"uid": "f250b6646a629267", "name": "test_completed", "source": "f250b6646a629267.png", "type": "image/png", "size": 629584}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "6406b0e905c8a378", "name": "stdout", "source": "6406b0e905c8a378.txt", "type": "text/plain", "size": 11469}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448753925, "stop": 1754448753925, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448753926, "stop": 1754448755309, "duration": 1383}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_summarize_what_i_m_reading"}, {"name": "subSuite", "value": "TestEllaSummarizeWhatIMReading"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_summarize_what_i_m_reading"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "2e8dd23e79a70f77", "status": "passed", "time": {"start": 1754398937975, "stop": 1754398951009, "duration": 13034}}], "categories": [], "tags": ["smoke"]}, "source": "b3f6ff1329da975e.json", "parameterValues": []}