{"uid": "b48c90dea488094b", "name": "测试Search for addresses on the screen能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen.TestEllaSearchAddressesScreen#test_search_for_addresses_on_the_screen", "historyId": "8814f1dafa698e785ee1f58faa6e745d", "time": {"start": 1754404041726, "stop": 1754404056282, "duration": 14556}, "description": "Search for addresses on the screen", "descriptionHtml": "<p>Search for addresses on the screen</p>\n", "status": "failed", "statusMessage": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', '如果你想在屏幕上搜索地址，以下是一些可行的方法：\\n\\n使用手机浏览器 ：在大多数手机浏览器中，地址栏通常位于屏幕顶部 ，你可以直接在地址栏中输入你要搜索的地址，浏览器会自动识别并给出相应的结果。部分浏览器还会在地址栏中根据你输入的内容提供丰富的相关搜索建议1。\\n使用图片文字识别工具 ：如果地址在图片上，你可以借助一些图片文字识别工具来提取文字信息，进而获取地址。比如，微信长按图片，选择“提取文字”选项，即可提取出图片中的文字，找到你需要的地址5 ；QQ也是类似操作，在QQ里找到需要提取文字的图片并点击放大，再用手指长按图片，等待下方菜单栏跳转出来，找到“提取文字”选项即可2 ；还有一些专业的图片处理软件，如迅捷图片转换器、WPS、搜狗输', '10:27 对话 发现 10 篇参考资料 如果你想在屏幕上搜索地址，以下是一些可行的方法：&#10;&#10;使用手机浏览器 ：在大多数手机浏览器中，地址栏通常位于屏幕顶部 ，你可以直接在地址栏中输入你要搜索的地址，浏览器会自动识别并给出相应的结果。部分浏览器还会在地址栏中根据你输入的内容提供丰富的相关搜索建议1。&#10;使用图片文字识别工具 ：如果地址在图片上，你可以借助一些图片文字识别工具来提取文字信息，进而获取地址。比如，微信长按图片，选择“提取文字”选项，即可提取出图片中的文字，找到你需要的地址5 ；QQ也是类似操作，在QQ里找到需要提取文字的图片并点击放大，再用手指长按图片，等待下方菜单栏跳转出来，找到“提取文字”选项即可2 ；还有一些专业的图片处理软件，如迅捷图片转换器、WPS、搜狗输入法、Adobe Acrobat Pro DC等，也能帮 AI生成，仅作参考 DeepSeek-R1 有问题尽管问我…']'\nassert None", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen.TestEllaSearchAddressesScreen object at 0x00000240FF30B210>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000240831A2050>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_search_for_addresses_on_the_screen(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     expected_text = self.expected_text\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证GoogleMap应用已打开\"):\n>           assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', '如果你想在屏幕上搜索地址，以下是一些可行的方法：\\n\\n使用手机浏览器 ：在大多数手机浏览器中，地址栏通常位于屏幕顶部 ，你可以直接在地址栏中输入你要搜索的地址，浏览器会自动识别并给出相应的结果。部分浏览器还会在地址栏中根据你输入的内容提供丰富的相关搜索建议1。\\n使用图片文字识别工具 ：如果地址在图片上，你可以借助一些图片文字识别工具来提取文字信息，进而获取地址。比如，微信长按图片，选择“提取文字”选项，即可提取出图片中的文字，找到你需要的地址5 ；QQ也是类似操作，在QQ里找到需要提取文字的图片并点击放大，再用手指长按图片，等待下方菜单栏跳转出来，找到“提取文字”选项即可2 ；还有一些专业的图片处理软件，如迅捷图片转换器、WPS、搜狗输', '10:27 对话 发现 10 篇参考资料 如果你想在屏幕上搜索地址，以下是一些可行的方法：&#10;&#10;使用手机浏览器 ：在大多数手机浏览器中，地址栏通常位于屏幕顶部 ，你可以直接在地址栏中输入你要搜索的地址，浏览器会自动识别并给出相应的结果。部分浏览器还会在地址栏中根据你输入的内容提供丰富的相关搜索建议1。&#10;使用图片文字识别工具 ：如果地址在图片上，你可以借助一些图片文字识别工具来提取文字信息，进而获取地址。比如，微信长按图片，选择“提取文字”选项，即可提取出图片中的文字，找到你需要的地址5 ；QQ也是类似操作，在QQ里找到需要提取文字的图片并点击放大，再用手指长按图片，等待下方菜单栏跳转出来，找到“提取文字”选项即可2 ；还有一些专业的图片处理软件，如迅捷图片转换器、WPS、搜狗输入法、Adobe Acrobat Pro DC等，也能帮 AI生成，仅作参考 DeepSeek-R1 有问题尽管问我…']'\nE           assert None\n\ntestcases\\test_ella\\unsupported_commands\\test_search_for_addresses_on_the_screen.py:36: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754404028964, "stop": 1754404041725, "duration": 12761}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754404041725, "stop": 1754404041725, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "Search for addresses on the screen", "status": "failed", "statusMessage": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', '如果你想在屏幕上搜索地址，以下是一些可行的方法：\\n\\n使用手机浏览器 ：在大多数手机浏览器中，地址栏通常位于屏幕顶部 ，你可以直接在地址栏中输入你要搜索的地址，浏览器会自动识别并给出相应的结果。部分浏览器还会在地址栏中根据你输入的内容提供丰富的相关搜索建议1。\\n使用图片文字识别工具 ：如果地址在图片上，你可以借助一些图片文字识别工具来提取文字信息，进而获取地址。比如，微信长按图片，选择“提取文字”选项，即可提取出图片中的文字，找到你需要的地址5 ；QQ也是类似操作，在QQ里找到需要提取文字的图片并点击放大，再用手指长按图片，等待下方菜单栏跳转出来，找到“提取文字”选项即可2 ；还有一些专业的图片处理软件，如迅捷图片转换器、WPS、搜狗输', '10:27 对话 发现 10 篇参考资料 如果你想在屏幕上搜索地址，以下是一些可行的方法：&#10;&#10;使用手机浏览器 ：在大多数手机浏览器中，地址栏通常位于屏幕顶部 ，你可以直接在地址栏中输入你要搜索的地址，浏览器会自动识别并给出相应的结果。部分浏览器还会在地址栏中根据你输入的内容提供丰富的相关搜索建议1。&#10;使用图片文字识别工具 ：如果地址在图片上，你可以借助一些图片文字识别工具来提取文字信息，进而获取地址。比如，微信长按图片，选择“提取文字”选项，即可提取出图片中的文字，找到你需要的地址5 ；QQ也是类似操作，在QQ里找到需要提取文字的图片并点击放大，再用手指长按图片，等待下方菜单栏跳转出来，找到“提取文字”选项即可2 ；还有一些专业的图片处理软件，如迅捷图片转换器、WPS、搜狗输入法、Adobe Acrobat Pro DC等，也能帮 AI生成，仅作参考 DeepSeek-R1 有问题尽管问我…']'\nassert None", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen.TestEllaSearchAddressesScreen object at 0x00000240FF30B210>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000240831A2050>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_search_for_addresses_on_the_screen(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     expected_text = self.expected_text\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证GoogleMap应用已打开\"):\n>           assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', '如果你想在屏幕上搜索地址，以下是一些可行的方法：\\n\\n使用手机浏览器 ：在大多数手机浏览器中，地址栏通常位于屏幕顶部 ，你可以直接在地址栏中输入你要搜索的地址，浏览器会自动识别并给出相应的结果。部分浏览器还会在地址栏中根据你输入的内容提供丰富的相关搜索建议1。\\n使用图片文字识别工具 ：如果地址在图片上，你可以借助一些图片文字识别工具来提取文字信息，进而获取地址。比如，微信长按图片，选择“提取文字”选项，即可提取出图片中的文字，找到你需要的地址5 ；QQ也是类似操作，在QQ里找到需要提取文字的图片并点击放大，再用手指长按图片，等待下方菜单栏跳转出来，找到“提取文字”选项即可2 ；还有一些专业的图片处理软件，如迅捷图片转换器、WPS、搜狗输', '10:27 对话 发现 10 篇参考资料 如果你想在屏幕上搜索地址，以下是一些可行的方法：&#10;&#10;使用手机浏览器 ：在大多数手机浏览器中，地址栏通常位于屏幕顶部 ，你可以直接在地址栏中输入你要搜索的地址，浏览器会自动识别并给出相应的结果。部分浏览器还会在地址栏中根据你输入的内容提供丰富的相关搜索建议1。&#10;使用图片文字识别工具 ：如果地址在图片上，你可以借助一些图片文字识别工具来提取文字信息，进而获取地址。比如，微信长按图片，选择“提取文字”选项，即可提取出图片中的文字，找到你需要的地址5 ；QQ也是类似操作，在QQ里找到需要提取文字的图片并点击放大，再用手指长按图片，等待下方菜单栏跳转出来，找到“提取文字”选项即可2 ；还有一些专业的图片处理软件，如迅捷图片转换器、WPS、搜狗输入法、Adobe Acrobat Pro DC等，也能帮 AI生成，仅作参考 DeepSeek-R1 有问题尽管问我…']'\nE           assert None\n\ntestcases\\test_ella\\unsupported_commands\\test_search_for_addresses_on_the_screen.py:36: AssertionError", "steps": [{"name": "执行命令: Search for addresses on the screen", "time": {"start": 1754404041726, "stop": 1754404056280, "duration": 14554}, "status": "passed", "steps": [{"name": "执行命令: Search for addresses on the screen", "time": {"start": 1754404041726, "stop": 1754404055952, "duration": 14226}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754404055952, "stop": 1754404056278, "duration": 326}, "status": "passed", "steps": [], "attachments": [{"uid": "ac5a48a6810e4082", "name": "测试总结", "source": "ac5a48a6810e4082.txt", "type": "text/plain", "size": 2211}, {"uid": "12724595f11dfa4e", "name": "test_completed", "source": "12724595f11dfa4e.png", "type": "image/png", "size": 877116}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证GoogleMap应用已打开", "time": {"start": 1754404056280, "stop": 1754404056280, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', '如果你想在屏幕上搜索地址，以下是一些可行的方法：\\n\\n使用手机浏览器 ：在大多数手机浏览器中，地址栏通常位于屏幕顶部 ，你可以直接在地址栏中输入你要搜索的地址，浏览器会自动识别并给出相应的结果。部分浏览器还会在地址栏中根据你输入的内容提供丰富的相关搜索建议1。\\n使用图片文字识别工具 ：如果地址在图片上，你可以借助一些图片文字识别工具来提取文字信息，进而获取地址。比如，微信长按图片，选择“提取文字”选项，即可提取出图片中的文字，找到你需要的地址5 ；QQ也是类似操作，在QQ里找到需要提取文字的图片并点击放大，再用手指长按图片，等待下方菜单栏跳转出来，找到“提取文字”选项即可2 ；还有一些专业的图片处理软件，如迅捷图片转换器、WPS、搜狗输', '10:27 对话 发现 10 篇参考资料 如果你想在屏幕上搜索地址，以下是一些可行的方法：&#10;&#10;使用手机浏览器 ：在大多数手机浏览器中，地址栏通常位于屏幕顶部 ，你可以直接在地址栏中输入你要搜索的地址，浏览器会自动识别并给出相应的结果。部分浏览器还会在地址栏中根据你输入的内容提供丰富的相关搜索建议1。&#10;使用图片文字识别工具 ：如果地址在图片上，你可以借助一些图片文字识别工具来提取文字信息，进而获取地址。比如，微信长按图片，选择“提取文字”选项，即可提取出图片中的文字，找到你需要的地址5 ；QQ也是类似操作，在QQ里找到需要提取文字的图片并点击放大，再用手指长按图片，等待下方菜单栏跳转出来，找到“提取文字”选项即可2 ；还有一些专业的图片处理软件，如迅捷图片转换器、WPS、搜狗输入法、Adobe Acrobat Pro DC等，也能帮 AI生成，仅作参考 DeepSeek-R1 有问题尽管问我…']'\nassert None\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_search_for_addresses_on_the_screen.py\", line 36, in test_search_for_addresses_on_the_screen\n    assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "335d09447767666d", "name": "stdout", "source": "335d09447767666d.txt", "type": "text/plain", "size": 19166}], "parameters": [], "attachmentStep": false, "stepsCount": 4, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754404056292, "stop": 1754404056615, "duration": 323}, "status": "passed", "steps": [], "attachments": [{"uid": "f37e10b06eabf9b7", "name": "失败截图-TestEllaSearchAddressesScreen", "source": "f37e10b06eabf9b7.png", "type": "image/png", "size": 893486}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754404056619, "stop": 1754404057892, "duration": 1273}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_search_for_addresses_on_the_screen"}, {"name": "subSuite", "value": "TestEllaSearchAddressesScreen"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "b48c90dea488094b.json", "parameterValues": []}