{"uid": "b57d9700043bf78", "name": "测试book a flight to paris返回正确的不支持响应", "fullName": "testcases.test_ella.dialogue.test_book_a_flight_to_paris.TestEllaBookFlightParis#test_book_a_flight_to_paris", "historyId": "7b1fce8b7d3ff59dca969b439bb82f75", "time": {"start": 1754397868550, "stop": 1754397883663, "duration": 15113}, "description": "验证book a flight to paris指令返回预期的不支持响应", "descriptionHtml": "<p>验证book a flight to paris指令返回预期的不支持响应</p>\n", "status": "broken", "statusMessage": "TypeError: a bytes-like object is required, not 'dict'", "statusTrace": "self = <testcases.test_ella.dialogue.test_book_a_flight_to_paris.TestEllaBookFlightParis object at 0x00000240FEFF76D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000240811EF950>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_book_a_flight_to_paris(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(\"记录测试结果\"):\n            summary = {\n                \"command\": command,\n                \"response_text\": response_text,\n                \"expected_text\": expected_text,\n                \"test_type\": \"unsupported_command\",\n                \"result\": \"PASS\" if result else \"FAIL\"\n            }\n>           self.attach_test_summary(summary)\n\ntestcases\\test_ella\\dialogue\\test_book_a_flight_to_paris.py:43: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\ntestcases\\test_ella\\base_ella_test.py:846: in attach_test_summary\n    allure.attach(summary, name=\"测试总结\", attachment_type=allure.attachment_type.TEXT)\nD:\\SystemApplication\\Lib\\site-packages\\allure_commons\\_allure.py:210: in __call__\n    plugin_manager.hook.attach_data(body=body, name=name, attachment_type=attachment_type, extension=extension)\nD:\\SystemApplication\\Lib\\site-packages\\pluggy\\_hooks.py:512: in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nD:\\SystemApplication\\Lib\\site-packages\\pluggy\\_manager.py:120: in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nD:\\SystemApplication\\Lib\\site-packages\\allure_pytest\\listener.py:252: in attach_data\n    self.allure_logger.attach_data(uuid4(), body, name=name, attachment_type=attachment_type, extension=extension)\nD:\\SystemApplication\\Lib\\site-packages\\allure_commons\\reporter.py:165: in attach_data\n    plugin_manager.hook.report_attached_data(body=body, file_name=file_name)\nD:\\SystemApplication\\Lib\\site-packages\\pluggy\\_hooks.py:512: in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nD:\\SystemApplication\\Lib\\site-packages\\pluggy\\_manager.py:120: in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <allure_commons.logger.AllureFileLogger object at 0x00000240FED82BD0>\nbody = {'command': 'book a flight to paris', 'expected_text': ['flight', 'paris'], 'response_text': ['book a flight to paris'...flights to Generated by AI, for reference only DeepSeek-R1 Feel free to ask me any questions…'], 'result': 'PASS', ...}\nfile_name = '5997bfbe-2a35-4952-a930-89a37d3d1b42-attachment.txt'\n\n    @hookimpl\n    def report_attached_data(self, body, file_name):\n        destination = self._report_dir / file_name\n        with open(destination, 'wb') as attached_file:\n            if isinstance(body, str):\n                attached_file.write(body.encode('utf-8'))\n            else:\n>               attached_file.write(body)\nE               TypeError: a bytes-like object is required, not 'dict'\n\nD:\\SystemApplication\\Lib\\site-packages\\allure_commons\\logger.py:48: TypeError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397855875, "stop": 1754397868549, "duration": 12674}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397868549, "stop": 1754397868549, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证book a flight to paris指令返回预期的不支持响应", "status": "broken", "statusMessage": "TypeError: a bytes-like object is required, not 'dict'", "statusTrace": "self = <testcases.test_ella.dialogue.test_book_a_flight_to_paris.TestEllaBookFlightParis object at 0x00000240FEFF76D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000240811EF950>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_book_a_flight_to_paris(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(\"记录测试结果\"):\n            summary = {\n                \"command\": command,\n                \"response_text\": response_text,\n                \"expected_text\": expected_text,\n                \"test_type\": \"unsupported_command\",\n                \"result\": \"PASS\" if result else \"FAIL\"\n            }\n>           self.attach_test_summary(summary)\n\ntestcases\\test_ella\\dialogue\\test_book_a_flight_to_paris.py:43: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\ntestcases\\test_ella\\base_ella_test.py:846: in attach_test_summary\n    allure.attach(summary, name=\"测试总结\", attachment_type=allure.attachment_type.TEXT)\nD:\\SystemApplication\\Lib\\site-packages\\allure_commons\\_allure.py:210: in __call__\n    plugin_manager.hook.attach_data(body=body, name=name, attachment_type=attachment_type, extension=extension)\nD:\\SystemApplication\\Lib\\site-packages\\pluggy\\_hooks.py:512: in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nD:\\SystemApplication\\Lib\\site-packages\\pluggy\\_manager.py:120: in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nD:\\SystemApplication\\Lib\\site-packages\\allure_pytest\\listener.py:252: in attach_data\n    self.allure_logger.attach_data(uuid4(), body, name=name, attachment_type=attachment_type, extension=extension)\nD:\\SystemApplication\\Lib\\site-packages\\allure_commons\\reporter.py:165: in attach_data\n    plugin_manager.hook.report_attached_data(body=body, file_name=file_name)\nD:\\SystemApplication\\Lib\\site-packages\\pluggy\\_hooks.py:512: in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nD:\\SystemApplication\\Lib\\site-packages\\pluggy\\_manager.py:120: in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <allure_commons.logger.AllureFileLogger object at 0x00000240FED82BD0>\nbody = {'command': 'book a flight to paris', 'expected_text': ['flight', 'paris'], 'response_text': ['book a flight to paris'...flights to Generated by AI, for reference only DeepSeek-R1 Feel free to ask me any questions…'], 'result': 'PASS', ...}\nfile_name = '5997bfbe-2a35-4952-a930-89a37d3d1b42-attachment.txt'\n\n    @hookimpl\n    def report_attached_data(self, body, file_name):\n        destination = self._report_dir / file_name\n        with open(destination, 'wb') as attached_file:\n            if isinstance(body, str):\n                attached_file.write(body.encode('utf-8'))\n            else:\n>               attached_file.write(body)\nE               TypeError: a bytes-like object is required, not 'dict'\n\nD:\\SystemApplication\\Lib\\site-packages\\allure_commons\\logger.py:48: TypeError", "steps": [{"name": "执行命令: book a flight to paris", "time": {"start": 1754397868550, "stop": 1754397883650, "duration": 15100}, "status": "passed", "steps": [{"name": "执行命令: book a flight to paris", "time": {"start": 1754397868550, "stop": 1754397883341, "duration": 14791}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397883341, "stop": 1754397883649, "duration": 308}, "status": "passed", "steps": [], "attachments": [{"uid": "453b1f4d2ea8256b", "name": "测试总结", "source": "453b1f4d2ea8256b.txt", "type": "text/plain", "size": 805}, {"uid": "b859c1b4fb7daf66", "name": "test_completed", "source": "b859c1b4fb7daf66.png", "type": "image/png", "size": 637203}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754397883650, "stop": 1754397883654, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397883654, "stop": 1754397883655, "duration": 1}, "status": "broken", "statusMessage": "TypeError: a bytes-like object is required, not 'dict'\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\dialogue\\test_book_a_flight_to_paris.py\", line 43, in test_book_a_flight_to_paris\n    self.attach_test_summary(summary)\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 846, in attach_test_summary\n    allure.attach(summary, name=\"测试总结\", attachment_type=allure.attachment_type.TEXT)\n  File \"D:\\SystemApplication\\Lib\\site-packages\\allure_commons\\_allure.py\", line 210, in __call__\n    plugin_manager.hook.attach_data(body=body, name=name, attachment_type=attachment_type, extension=extension)\n  File \"D:\\SystemApplication\\Lib\\site-packages\\pluggy\\_hooks.py\", line 512, in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\pluggy\\_manager.py\", line 120, in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\pluggy\\_callers.py\", line 167, in _multicall\n    raise exception\n  File \"D:\\SystemApplication\\Lib\\site-packages\\pluggy\\_callers.py\", line 121, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\allure_pytest\\listener.py\", line 252, in attach_data\n    self.allure_logger.attach_data(uuid4(), body, name=name, attachment_type=attachment_type, extension=extension)\n  File \"D:\\SystemApplication\\Lib\\site-packages\\allure_commons\\reporter.py\", line 165, in attach_data\n    plugin_manager.hook.report_attached_data(body=body, file_name=file_name)\n  File \"D:\\SystemApplication\\Lib\\site-packages\\pluggy\\_hooks.py\", line 512, in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\pluggy\\_manager.py\", line 120, in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\pluggy\\_callers.py\", line 167, in _multicall\n    raise exception\n  File \"D:\\SystemApplication\\Lib\\site-packages\\pluggy\\_callers.py\", line 121, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\SystemApplication\\Lib\\site-packages\\allure_commons\\logger.py\", line 48, in report_attached_data\n    attached_file.write(body)\n", "steps": [], "attachments": [{"uid": "825ac00f1484a96c", "name": "测试总结", "source": "825ac00f1484a96c.txt", "type": "text/plain", "size": 0}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 1}], "attachments": [{"uid": "6d6e83fa9201b7c4", "name": "stdout", "source": "6d6e83fa9201b7c4.txt", "type": "text/plain", "size": 14986}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 4}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397883831, "stop": 1754397884219, "duration": 388}, "status": "passed", "steps": [], "attachments": [{"uid": "37da740608b2d34a", "name": "失败截图-TestEllaBookFlightParis", "source": "37da740608b2d34a.png", "type": "image/png", "size": 656047}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754397884221, "stop": 1754397885519, "duration": 1298}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_book_a_flight_to_paris"}, {"name": "subSuite", "value": "TestEllaBookFlightParis"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_book_a_flight_to_paris"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "b57d9700043bf78.json", "parameterValues": []}