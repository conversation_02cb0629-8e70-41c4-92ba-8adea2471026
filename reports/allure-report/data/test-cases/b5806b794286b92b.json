{"uid": "b5806b794286b92b", "name": "测试how to set screenshots返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_how_to_set_screenshots.TestEllaHowSetScreenshots#test_how_to_set_screenshots", "historyId": "bfd4a9e37b70dca0b14b0ccf5246fc4a", "time": {"start": 1754453037409, "stop": 1754453051054, "duration": 13645}, "description": "验证how to set screenshots指令返回预期的不支持响应", "descriptionHtml": "<p>验证how to set screenshots指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453025394, "stop": 1754453037409, "duration": 12015}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453037409, "stop": 1754453037409, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证how to set screenshots指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: how to set screenshots", "time": {"start": 1754453037409, "stop": 1754453050856, "duration": 13447}, "status": "passed", "steps": [{"name": "执行命令: how to set screenshots", "time": {"start": 1754453037409, "stop": 1754453050672, "duration": 13263}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453050672, "stop": 1754453050855, "duration": 183}, "status": "passed", "steps": [], "attachments": [{"uid": "2a8608798a61f7e5", "name": "测试总结", "source": "2a8608798a61f7e5.txt", "type": "text/plain", "size": 229}, {"uid": "a488f94d5af70f0b", "name": "test_completed", "source": "a488f94d5af70f0b.png", "type": "image/png", "size": 488346}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453050856, "stop": 1754453050857, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453050857, "stop": 1754453051053, "duration": 196}, "status": "passed", "steps": [], "attachments": [{"uid": "d481a624f01cd737", "name": "测试总结", "source": "d481a624f01cd737.txt", "type": "text/plain", "size": 229}, {"uid": "86669bdaa02efb4a", "name": "test_completed", "source": "86669bdaa02efb4a.png", "type": "image/png", "size": 487797}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "f7c76b267e3b0b34", "name": "stdout", "source": "f7c76b267e3b0b34.txt", "type": "text/plain", "size": 11313}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453051054, "stop": 1754453051054, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453051055, "stop": 1754453052383, "duration": 1328}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_how_to_set_screenshots"}, {"name": "subSuite", "value": "TestEllaHowSetScreenshots"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_how_to_set_screenshots"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "a1ef12101f81b7d", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403048991, "stop": 1754403064562, "duration": 15571}}], "categories": [], "tags": ["smoke"]}, "source": "b5806b794286b92b.json", "parameterValues": []}