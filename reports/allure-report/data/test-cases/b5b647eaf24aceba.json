{"uid": "b5b647eaf24aceba", "name": "测试take a screenshot能正常执行", "fullName": "testcases.test_ella.component_coupling.test_take_a_screenshot.TestEllaTakeScreenshot#test_take_a_screenshot", "historyId": "6bdbaaabff6497c5d3be4727f1a7cd8d", "time": {"start": 1754397779894, "stop": 1754397792684, "duration": 12790}, "description": "take a screenshot", "descriptionHtml": "<p>take a screenshot</p>\n", "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False", "statusTrace": "self = <testcases.test_ella.component_coupling.test_take_a_screenshot.TestEllaTakeScreenshot object at 0x00000240FEF88150>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000240810A8890>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_take_a_screenshot(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=True\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证文件存在\"):\n>           assert files_status, f\"文件不存在！\"\nE           AssertionError: 文件不存在！\nE           assert False\n\ntestcases\\test_ella\\component_coupling\\test_take_a_screenshot.py:36: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397766662, "stop": 1754397779893, "duration": 13231}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397779893, "stop": 1754397779893, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "take a screenshot", "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False", "statusTrace": "self = <testcases.test_ella.component_coupling.test_take_a_screenshot.TestEllaTakeScreenshot object at 0x00000240FEF88150>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000240810A8890>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_take_a_screenshot(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=True\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证文件存在\"):\n>           assert files_status, f\"文件不存在！\"\nE           AssertionError: 文件不存在！\nE           assert False\n\ntestcases\\test_ella\\component_coupling\\test_take_a_screenshot.py:36: AssertionError", "steps": [{"name": "执行命令: take a screenshot", "time": {"start": 1754397779894, "stop": 1754397792683, "duration": 12789}, "status": "passed", "steps": [{"name": "执行命令: take a screenshot", "time": {"start": 1754397779894, "stop": 1754397792346, "duration": 12452}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397792346, "stop": 1754397792682, "duration": 336}, "status": "passed", "steps": [], "attachments": [{"uid": "f80afc228f83a681", "name": "测试总结", "source": "f80afc228f83a681.txt", "type": "text/plain", "size": 205}, {"uid": "3f7032bd594a3fe9", "name": "test_completed", "source": "3f7032bd594a3fe9.png", "type": "image/png", "size": 561001}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证文件存在", "time": {"start": 1754397792683, "stop": 1754397792683, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: 文件不存在！\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\component_coupling\\test_take_a_screenshot.py\", line 36, in test_take_a_screenshot\n    assert files_status, f\"文件不存在！\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "20e35ae035c3bc80", "name": "stdout", "source": "20e35ae035c3bc80.txt", "type": "text/plain", "size": 11963}], "parameters": [], "attachmentStep": false, "stepsCount": 4, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397792693, "stop": 1754397792915, "duration": 222}, "status": "passed", "steps": [], "attachments": [{"uid": "718e53b38cb8414d", "name": "失败截图-TestEllaTakeScreenshot", "source": "718e53b38cb8414d.png", "type": "image/png", "size": 560838}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754397792916, "stop": 1754397794173, "duration": 1257}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_take_a_screenshot"}, {"name": "subSuite", "value": "TestEllaTakeScreenshot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_take_a_screenshot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "b5b647eaf24aceba.json", "parameterValues": []}