{"uid": "b79216f6e34836ba", "name": "测试set color style返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_color_style.TestEllaSetColorStyle#test_set_color_style", "historyId": "e2beda2a0bda4155b33d47f14bdcb9ed", "time": {"start": 1754454301881, "stop": 1754454316351, "duration": 14470}, "description": "验证set color style指令返回预期的不支持响应", "descriptionHtml": "<p>验证set color style指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454289218, "stop": 1754454301881, "duration": 12663}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454301881, "stop": 1754454301881, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set color style指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set color style", "time": {"start": 1754454301882, "stop": 1754454316169, "duration": 14287}, "status": "passed", "steps": [{"name": "执行命令: set color style", "time": {"start": 1754454301882, "stop": 1754454316004, "duration": 14122}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454316004, "stop": 1754454316169, "duration": 165}, "status": "passed", "steps": [], "attachments": [{"uid": "813f0ebb242a1854", "name": "测试总结", "source": "813f0ebb242a1854.txt", "type": "text/plain", "size": 215}, {"uid": "1ab1aad039e2bfd9", "name": "test_completed", "source": "1ab1aad039e2bfd9.png", "type": "image/png", "size": 473903}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454316169, "stop": 1754454316170, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454316170, "stop": 1754454316351, "duration": 181}, "status": "passed", "steps": [], "attachments": [{"uid": "7c04c2d95f9da218", "name": "测试总结", "source": "7c04c2d95f9da218.txt", "type": "text/plain", "size": 215}, {"uid": "64a9a33dfaf2830c", "name": "test_completed", "source": "64a9a33dfaf2830c.png", "type": "image/png", "size": 474962}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "ec47106695e8170b", "name": "stdout", "source": "ec47106695e8170b.txt", "type": "text/plain", "size": 11242}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454316351, "stop": 1754454316351, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454316352, "stop": 1754454317748, "duration": 1396}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_color_style"}, {"name": "subSuite", "value": "TestEllaSetColorStyle"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_color_style"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "4729842bdc752b23", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404309484, "stop": 1754404326639, "duration": 17155}}], "categories": [], "tags": ["smoke"]}, "source": "b79216f6e34836ba.json", "parameterValues": []}