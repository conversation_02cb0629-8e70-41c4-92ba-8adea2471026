{"uid": "b7f1fa9085faaa27", "name": "测试navigation to the first address in the image能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image.TestEllaNavigationFirstAddressImage#test_navigation_to_the_first_address_in_the_image", "historyId": "9c84b087eb7d9fde94ed5bb5370b275b", "time": {"start": 1754453512052, "stop": 1754453526933, "duration": 14881}, "description": "navigation to the first address in the image", "descriptionHtml": "<p>navigation to the first address in the image</p>\n", "status": "broken", "statusMessage": "ValueError: too many values to unpack (expected 3)", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image.TestEllaNavigationFirstAddressImage object at 0x000001E3895D9090>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B709990>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_navigation_to_the_first_address_in_the_image(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n>           initial_status, final_status, response_text = self.simple_command_test(\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\nE           ValueError: too many values to unpack (expected 3)\n\ntestcases\\test_ella\\unsupported_commands\\test_navigation_to_the_first_address_in_the_image.py:26: ValueError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453500149, "stop": 1754453512052, "duration": 11903}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453512052, "stop": 1754453512052, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "navigation to the first address in the image", "status": "broken", "statusMessage": "ValueError: too many values to unpack (expected 3)", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image.TestEllaNavigationFirstAddressImage object at 0x000001E3895D9090>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B709990>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_navigation_to_the_first_address_in_the_image(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n>           initial_status, final_status, response_text = self.simple_command_test(\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\nE           ValueError: too many values to unpack (expected 3)\n\ntestcases\\test_ella\\unsupported_commands\\test_navigation_to_the_first_address_in_the_image.py:26: ValueError", "steps": [{"name": "执行命令: navigation to the first address in the image", "time": {"start": 1754453512053, "stop": 1754453526933, "duration": 14880}, "status": "broken", "statusMessage": "ValueError: too many values to unpack (expected 3)\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_navigation_to_the_first_address_in_the_image.py\", line 26, in test_navigation_to_the_first_address_in_the_image\n    initial_status, final_status, response_text = self.simple_command_test(\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "steps": [{"name": "执行命令: navigation to the first address in the image", "time": {"start": 1754453512053, "stop": 1754453526750, "duration": 14697}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453526750, "stop": 1754453526932, "duration": 182}, "status": "passed", "steps": [], "attachments": [{"uid": "a547c817b93493d7", "name": "测试总结", "source": "a547c817b93493d7.txt", "type": "text/plain", "size": 606}, {"uid": "50d5582827fd563a", "name": "test_completed", "source": "50d5582827fd563a.png", "type": "image/png", "size": 492895}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 2}], "attachments": [{"uid": "154de93d235db420", "name": "stdout", "source": "154de93d235db420.txt", "type": "text/plain", "size": 13585}], "parameters": [], "attachmentStep": false, "stepsCount": 3, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453526937, "stop": 1754453527106, "duration": 169}, "status": "passed", "steps": [], "attachments": [{"uid": "e23609cfc10e5ede", "name": "失败截图-TestEllaNavigationFirstAddressImage", "source": "e23609cfc10e5ede.png", "type": "image/png", "size": 493265}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754453527109, "stop": 1754453528453, "duration": 1344}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_navigation_to_the_first_address_in_the_image"}, {"name": "subSuite", "value": "TestEllaNavigationFirstAddressImage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "ea81cfda8b678e9e", "status": "broken", "statusDetails": "ValueError: too many values to unpack (expected 3)", "time": {"start": 1754403534392, "stop": 1754403547438, "duration": 13046}}], "categories": [{"name": "Test defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "b7f1fa9085faaa27.json", "parameterValues": []}