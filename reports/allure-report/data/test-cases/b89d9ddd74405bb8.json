{"uid": "b89d9ddd74405bb8", "name": "测试Modify grape timbre返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_modify_grape_timbre.TestEllaEnableRunningLock#test_enable_running_lock", "historyId": "780a5f59d7531d4a5862437bbbfcf535", "time": {"start": 1754453425579, "stop": 1754453439364, "duration": 13785}, "description": "验证Modify grape timbre指令返回预期的不支持响应", "descriptionHtml": "<p>验证Modify grape timbre指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453413513, "stop": 1754453425579, "duration": 12066}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453425579, "stop": 1754453425579, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证Modify grape timbre指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: Modify grape timbre", "time": {"start": 1754453425579, "stop": 1754453439178, "duration": 13599}, "status": "passed", "steps": [{"name": "执行命令: Modify grape timbre", "time": {"start": 1754453425579, "stop": 1754453438991, "duration": 13412}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453438991, "stop": 1754453439178, "duration": 187}, "status": "passed", "steps": [], "attachments": [{"uid": "59e0019fd5a7f125", "name": "测试总结", "source": "59e0019fd5a7f125.txt", "type": "text/plain", "size": 186}, {"uid": "d2790ce36c86d9be", "name": "test_completed", "source": "d2790ce36c86d9be.png", "type": "image/png", "size": 453122}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453439178, "stop": 1754453439180, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453439180, "stop": 1754453439364, "duration": 184}, "status": "passed", "steps": [], "attachments": [{"uid": "481bb969d6d4e6c", "name": "测试总结", "source": "481bb969d6d4e6c.txt", "type": "text/plain", "size": 186}, {"uid": "9ed6f2b277fb134b", "name": "test_completed", "source": "9ed6f2b277fb134b.png", "type": "image/png", "size": 453220}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "117ac31828cb8689", "name": "stdout", "source": "117ac31828cb8689.txt", "type": "text/plain", "size": 11196}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453439364, "stop": 1754453439364, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453439365, "stop": 1754453440721, "duration": 1356}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_modify_grape_timbre"}, {"name": "subSuite", "value": "TestEllaEnableRunningLock"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_modify_grape_timbre"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "de61ee6963d31004", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['OK, the voice is switched.']\nassert False", "time": {"start": 1754403446102, "stop": 1754403461551, "duration": 15449}}], "categories": [], "tags": ["smoke"]}, "source": "b89d9ddd74405bb8.json", "parameterValues": []}