{"uid": "b9b7e92debdfb117", "name": "测试play rock music", "fullName": "testcases.test_ella.component_coupling.test_play_rock_music.TestEllaOpenVisha#test_play_rock_music", "historyId": "6075008522e5d0ae1667c4ac4be759eb", "time": {"start": 1754447272254, "stop": 1754447294725, "duration": 22471}, "description": "测试play rock music指令", "descriptionHtml": "<p>测试play rock music指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447259813, "stop": 1754447272253, "duration": 12440}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447272253, "stop": 1754447272253, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play rock music指令", "status": "passed", "steps": [{"name": "执行命令: play rock music", "time": {"start": 1754447272254, "stop": 1754447294506, "duration": 22252}, "status": "passed", "steps": [{"name": "执行命令: play rock music", "time": {"start": 1754447272254, "stop": 1754447294280, "duration": 22026}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447294280, "stop": 1754447294506, "duration": 226}, "status": "passed", "steps": [], "attachments": [{"uid": "7e3ddb0fff3db9da", "name": "测试总结", "source": "7e3ddb0fff3db9da.txt", "type": "text/plain", "size": 495}, {"uid": "7fce7eafa172a10d", "name": "test_completed", "source": "7fce7eafa172a10d.png", "type": "image/png", "size": 539380}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447294506, "stop": 1754447294507, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证visha已打开", "time": {"start": 1754447294507, "stop": 1754447294507, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447294507, "stop": 1754447294725, "duration": 218}, "status": "passed", "steps": [], "attachments": [{"uid": "43f6bade7b6037f3", "name": "测试总结", "source": "43f6bade7b6037f3.txt", "type": "text/plain", "size": 495}, {"uid": "61e396f586f7d23", "name": "test_completed", "source": "61e396f586f7d23.png", "type": "image/png", "size": 539644}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "4cbd4e22dd1cd961", "name": "stdout", "source": "4cbd4e22dd1cd961.txt", "type": "text/plain", "size": 15099}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447294726, "stop": 1754447294726, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447294727, "stop": 1754447296125, "duration": 1398}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_rock_music"}, {"name": "subSuite", "value": "TestEllaOpen<PERSON>a"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_rock_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "ad16fe59d0da691", "status": "passed", "time": {"start": 1754397570511, "stop": 1754397586519, "duration": 16008}}], "categories": [], "tags": ["smoke"]}, "source": "b9b7e92debdfb117.json", "parameterValues": []}