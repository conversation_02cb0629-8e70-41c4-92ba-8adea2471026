{"uid": "ba368628feba4062", "name": "测试set customized cover screen返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_customized_cover_screen.TestEllaSetCustomizedCoverScreen#test_set_customized_cover_screen", "historyId": "104cf8a7ef102b6850b6d14f4cb14052", "time": {"start": 1754454385198, "stop": 1754454399279, "duration": 14081}, "description": "验证set customized cover screen指令返回预期的不支持响应", "descriptionHtml": "<p>验证set customized cover screen指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454372744, "stop": 1754454385197, "duration": 12453}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454385197, "stop": 1754454385197, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set customized cover screen指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set customized cover screen", "time": {"start": 1754454385198, "stop": 1754454399075, "duration": 13877}, "status": "passed", "steps": [{"name": "执行命令: set customized cover screen", "time": {"start": 1754454385198, "stop": 1754454398909, "duration": 13711}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454398909, "stop": 1754454399074, "duration": 165}, "status": "passed", "steps": [], "attachments": [{"uid": "eadbaa1a595139cf", "name": "测试总结", "source": "eadbaa1a595139cf.txt", "type": "text/plain", "size": 247}, {"uid": "54a865067eeb53ba", "name": "test_completed", "source": "54a865067eeb53ba.png", "type": "image/png", "size": 502031}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454399075, "stop": 1754454399076, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454399076, "stop": 1754454399278, "duration": 202}, "status": "passed", "steps": [], "attachments": [{"uid": "dd9e22c7ba2ed47a", "name": "测试总结", "source": "dd9e22c7ba2ed47a.txt", "type": "text/plain", "size": 247}, {"uid": "e56aa1f885615324", "name": "test_completed", "source": "e56aa1f885615324.png", "type": "image/png", "size": 501926}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "25f89fe6960ffd4a", "name": "stdout", "source": "25f89fe6960ffd4a.txt", "type": "text/plain", "size": 11396}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454399279, "stop": 1754454399279, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454399280, "stop": 1754454400630, "duration": 1350}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_customized_cover_screen"}, {"name": "subSuite", "value": "TestEllaSetCustomizedCoverScreen"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_customized_cover_screen"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "9f731d6196e1f1ac", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404392683, "stop": 1754404403881, "duration": 11198}}], "categories": [], "tags": ["smoke"]}, "source": "ba368628feba4062.json", "parameterValues": []}