{"uid": "bb94c3185f87279a", "name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switch_magic_voice_to_mango.TestEllaSwitchMagicVoiceToMango#test_switch_magic_voice_to_mango", "historyId": "9420fd606a04614c6f09bf36f2873f93", "time": {"start": 1754450472924, "stop": 1754450486952, "duration": 14028}, "description": "switch magic voice to Mango", "descriptionHtml": "<p>switch magic voice to Mango</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450460440, "stop": 1754450472921, "duration": 12481}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450472921, "stop": 1754450472921, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "switch magic voice to Mango", "status": "passed", "steps": [{"name": "执行命令: switch magic voice to Mango", "time": {"start": 1754450472924, "stop": 1754450486733, "duration": 13809}, "status": "passed", "steps": [{"name": "执行命令: switch magic voice to Mango", "time": {"start": 1754450472924, "stop": 1754450486534, "duration": 13610}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450486534, "stop": 1754450486733, "duration": 199}, "status": "passed", "steps": [], "attachments": [{"uid": "ae9d9a2eabe36083", "name": "测试总结", "source": "ae9d9a2eabe36083.txt", "type": "text/plain", "size": 202}, {"uid": "937c88eb9c679a93", "name": "test_completed", "source": "937c88eb9c679a93.png", "type": "image/png", "size": 600487}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1754450486733, "stop": 1754450486735, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450486735, "stop": 1754450486952, "duration": 217}, "status": "passed", "steps": [], "attachments": [{"uid": "ef5d6ad874a4cc32", "name": "测试总结", "source": "ef5d6ad874a4cc32.txt", "type": "text/plain", "size": 202}, {"uid": "9310d87916b6355a", "name": "test_completed", "source": "9310d87916b6355a.png", "type": "image/png", "size": 600332}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "aa6a689fbda89469", "name": "stdout", "source": "aa6a689fbda89469.txt", "type": "text/plain", "size": 11537}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450486953, "stop": 1754450486953, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450486954, "stop": 1754450488350, "duration": 1396}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_magic_voice_to_mango"}, {"name": "subSuite", "value": "TestEllaSwitchMagicVoiceToMango"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_magic_voice_to_mango"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "679d38728b39c6c6", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['OK', 'the voice is switched']\nassert False", "time": {"start": 1754400641059, "stop": 1754400652177, "duration": 11118}}], "categories": [], "tags": ["smoke"]}, "source": "bb94c3185f87279a.json", "parameterValues": []}