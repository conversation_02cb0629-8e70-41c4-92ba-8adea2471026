{"uid": "bbf642526ea09a6", "name": "测试turn on the screen record能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_the_screen_record.TestEllaTurnScreenRecord#test_turn_on_the_screen_record", "historyId": "5cf50058091f34fd1ed89d0b8f717355", "time": {"start": 1754451146850, "stop": 1754451164847, "duration": 17997}, "description": "turn on the screen record", "descriptionHtml": "<p>turn on the screen record</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451134391, "stop": 1754451146849, "duration": 12458}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451146849, "stop": 1754451146849, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "turn on the screen record", "status": "passed", "steps": [{"name": "执行命令: turn on the screen record", "time": {"start": 1754451146850, "stop": 1754451164676, "duration": 17826}, "status": "passed", "steps": [{"name": "执行命令: turn on the screen record", "time": {"start": 1754451146850, "stop": 1754451164513, "duration": 17663}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451164513, "stop": 1754451164676, "duration": 163}, "status": "passed", "steps": [], "attachments": [{"uid": "adfae2152a500cb3", "name": "测试总结", "source": "adfae2152a500cb3.txt", "type": "text/plain", "size": 198}, {"uid": "fafeee7cf64d4b85", "name": "test_completed", "source": "fafeee7cf64d4b85.png", "type": "image/png", "size": 478044}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451164676, "stop": 1754451164677, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754451164677, "stop": 1754451164677, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451164677, "stop": 1754451164846, "duration": 169}, "status": "passed", "steps": [], "attachments": [{"uid": "b75139f8e6a5e1f", "name": "测试总结", "source": "b75139f8e6a5e1f.txt", "type": "text/plain", "size": 198}, {"uid": "32596bebf47752b1", "name": "test_completed", "source": "32596bebf47752b1.png", "type": "image/png", "size": 478574}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "6203a7476e5bce26", "name": "stdout", "source": "6203a7476e5bce26.txt", "type": "text/plain", "size": 11730}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451164847, "stop": 1754451164847, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451164847, "stop": 1754451166227, "duration": 1380}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_the_screen_record"}, {"name": "subSuite", "value": "TestEllaTurnScreenRecord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_the_screen_record"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "1557f763d7b18e05", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording started']\nassert False", "time": {"start": 1754401306113, "stop": 1754401323288, "duration": 17175}}], "categories": [], "tags": ["smoke"]}, "source": "bbf642526ea09a6.json", "parameterValues": []}