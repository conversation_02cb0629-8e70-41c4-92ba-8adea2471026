{"uid": "bc9a3f7b0d5c458d", "name": "测试turn on wifi能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_wifi.TestEllaTurnWifi#test_turn_on_wifi", "historyId": "d18ea588937139bb162adb1092a66013", "time": {"start": 1754451212018, "stop": 1754451226679, "duration": 14661}, "description": "turn on wifi", "descriptionHtml": "<p>turn on wifi</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451199484, "stop": 1754451212016, "duration": 12532}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451212016, "stop": 1754451212016, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "turn on wifi", "status": "passed", "steps": [{"name": "执行命令: turn on wifi", "time": {"start": 1754451212018, "stop": 1754451226478, "duration": 14460}, "status": "passed", "steps": [{"name": "执行命令: turn on wifi", "time": {"start": 1754451212018, "stop": 1754451226312, "duration": 14294}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451226312, "stop": 1754451226477, "duration": 165}, "status": "passed", "steps": [], "attachments": [{"uid": "551ba443b5bb839d", "name": "测试总结", "source": "551ba443b5bb839d.txt", "type": "text/plain", "size": 175}, {"uid": "e1a12ef11b55d587", "name": "test_completed", "source": "e1a12ef11b55d587.png", "type": "image/png", "size": 356398}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451226478, "stop": 1754451226479, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754451226479, "stop": 1754451226479, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451226479, "stop": 1754451226679, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "f1b1307ea6c2dcd", "name": "测试总结", "source": "f1b1307ea6c2dcd.txt", "type": "text/plain", "size": 175}, {"uid": "195a26444174fa49", "name": "test_completed", "source": "195a26444174fa49.png", "type": "image/png", "size": 356237}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "323f5ff0c76d414", "name": "stdout", "source": "323f5ff0c76d414.txt", "type": "text/plain", "size": 11705}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451226680, "stop": 1754451226680, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451226681, "stop": 1754451228073, "duration": 1392}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_wifi"}, {"name": "subSuite", "value": "TestEllaTurnWifi"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_wifi"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "b422b20f38eec2f6", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Wi-<PERSON> is turned on now']\nassert False", "time": {"start": 1754401369356, "stop": 1754401381263, "duration": 11907}}], "categories": [], "tags": ["smoke"]}, "source": "bc9a3f7b0d5c458d.json", "parameterValues": []}