{"uid": "bd18a5f48c6a81ad", "name": "测试set screen relay返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_screen_relay.TestEllaSetScreenRelay#test_set_screen_relay", "historyId": "7ba4a9d343c0f63e9f654ce03ea4fa51", "time": {"start": 1754454912121, "stop": 1754454925915, "duration": 13794}, "description": "验证set screen relay指令返回预期的不支持响应", "descriptionHtml": "<p>验证set screen relay指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454900137, "stop": 1754454912120, "duration": 11983}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454912120, "stop": 1754454912120, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set screen relay指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set screen relay", "time": {"start": 1754454912121, "stop": 1754454925733, "duration": 13612}, "status": "passed", "steps": [{"name": "执行命令: set screen relay", "time": {"start": 1754454912121, "stop": 1754454925557, "duration": 13436}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454925557, "stop": 1754454925733, "duration": 176}, "status": "passed", "steps": [], "attachments": [{"uid": "72eb19f4f0f0354c", "name": "测试总结", "source": "72eb19f4f0f0354c.txt", "type": "text/plain", "size": 218}, {"uid": "d18c4b5a31ee5d26", "name": "test_completed", "source": "d18c4b5a31ee5d26.png", "type": "image/png", "size": 479242}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454925733, "stop": 1754454925734, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454925735, "stop": 1754454925915, "duration": 180}, "status": "passed", "steps": [], "attachments": [{"uid": "af281269812227c3", "name": "测试总结", "source": "af281269812227c3.txt", "type": "text/plain", "size": 218}, {"uid": "82f801e1d051e9f2", "name": "test_completed", "source": "82f801e1d051e9f2.png", "type": "image/png", "size": 479470}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "814d93285599b940", "name": "stdout", "source": "814d93285599b940.txt", "type": "text/plain", "size": 11256}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454925916, "stop": 1754454925916, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454925917, "stop": 1754454927259, "duration": 1342}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_screen_relay"}, {"name": "subSuite", "value": "TestEllaSetScreenRelay"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_screen_relay"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "ce41b794e3ffc806", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404923078, "stop": 1754404933925, "duration": 10847}}], "categories": [], "tags": ["smoke"]}, "source": "bd18a5f48c6a81ad.json", "parameterValues": []}