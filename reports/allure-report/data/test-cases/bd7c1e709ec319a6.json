{"uid": "bd7c1e709ec319a6", "name": "测试turn on high brightness mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_turn_on_high_brightness_mode.TestEllaTurnHighBrightnessMode#test_turn_on_high_brightness_mode", "historyId": "599b7a465f619c38a4638073f59c38c0", "time": {"start": 1754455462027, "stop": 1754455476062, "duration": 14035}, "description": "验证turn on high brightness mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证turn on high brightness mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455449530, "stop": 1754455462026, "duration": 12496}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455462026, "stop": 1754455462026, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证turn on high brightness mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: turn on high brightness mode", "time": {"start": 1754455462027, "stop": 1754455475869, "duration": 13842}, "status": "passed", "steps": [{"name": "执行命令: turn on high brightness mode", "time": {"start": 1754455462027, "stop": 1754455475681, "duration": 13654}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455475681, "stop": 1754455475869, "duration": 188}, "status": "passed", "steps": [], "attachments": [{"uid": "8cad68f93f0ed511", "name": "测试总结", "source": "8cad68f93f0ed511.txt", "type": "text/plain", "size": 246}, {"uid": "7ba0392a2e6b69fc", "name": "test_completed", "source": "7ba0392a2e6b69fc.png", "type": "image/png", "size": 507795}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455475869, "stop": 1754455475870, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455475870, "stop": 1754455476062, "duration": 192}, "status": "passed", "steps": [], "attachments": [{"uid": "90455b54af52ed57", "name": "测试总结", "source": "90455b54af52ed57.txt", "type": "text/plain", "size": 246}, {"uid": "bd1031b34fbd65da", "name": "test_completed", "source": "bd1031b34fbd65da.png", "type": "image/png", "size": 507566}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3412cb8e536bb67e", "name": "stdout", "source": "3412cb8e536bb67e.txt", "type": "text/plain", "size": 12108}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455476063, "stop": 1754455476063, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455476067, "stop": 1754455477484, "duration": 1417}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_on_high_brightness_mode"}, {"name": "subSuite", "value": "TestEllaTurnHighBrightnessMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_on_high_brightness_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "32689990d4658391", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405504607, "stop": 1754405520793, "duration": 16186}}], "categories": [], "tags": ["smoke"]}, "source": "bd7c1e709ec319a6.json", "parameterValues": []}