{"uid": "be1c07cf401ff3e1", "name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "fullName": "testcases.test_ella.third_coupling.test_navigate_from_beijing_to_shanghai.TestEllaNavigateFromBeijingShanghai#test_navigate_from_beijing_to_shanghai", "historyId": "f622c7c4831272dc58cb99e6af8d9943", "time": {"start": 1754451425856, "stop": 1754451444106, "duration": 18250}, "description": "navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai", "descriptionHtml": "<p>navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451413155, "stop": 1754451425854, "duration": 12699}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451425854, "stop": 1754451425854, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai", "status": "passed", "steps": [{"name": "执行命令: navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai", "time": {"start": 1754451425856, "stop": 1754451443900, "duration": 18044}, "status": "passed", "steps": [{"name": "执行命令: navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai", "time": {"start": 1754451425856, "stop": 1754451443688, "duration": 17832}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451443688, "stop": 1754451443899, "duration": 211}, "status": "passed", "steps": [], "attachments": [{"uid": "137e754a772228ac", "name": "测试总结", "source": "137e754a772228ac.txt", "type": "text/plain", "size": 333}, {"uid": "e4e470ebe996a11", "name": "test_completed", "source": "e4e470ebe996a11.png", "type": "image/png", "size": 512161}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451443900, "stop": 1754451443901, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754451443901, "stop": 1754451443901, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451443901, "stop": 1754451444106, "duration": 205}, "status": "passed", "steps": [], "attachments": [{"uid": "2c9e16af35e95dd9", "name": "测试总结", "source": "2c9e16af35e95dd9.txt", "type": "text/plain", "size": 333}, {"uid": "5233b41f2bb17fb", "name": "test_completed", "source": "5233b41f2bb17fb.png", "type": "image/png", "size": 513742}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "993485dd60fc5eba", "name": "stdout", "source": "993485dd60fc5eba.txt", "type": "text/plain", "size": 16366}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451444107, "stop": 1754451444107, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451444108, "stop": 1754451445524, "duration": 1416}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigate_from_beijing_to_shanghai"}, {"name": "subSuite", "value": "TestEllaNavigateFromBeijingShanghai"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigate_from_beijing_to_shanghai"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "f6b5180ed5339c89", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754401576339, "stop": 1754401594234, "duration": 17895}}], "categories": [], "tags": ["smoke"]}, "source": "be1c07cf401ff3e1.json", "parameterValues": []}