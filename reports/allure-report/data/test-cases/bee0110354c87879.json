{"uid": "bee0110354c87879", "name": "测试redial", "fullName": "testcases.test_ella.unsupported_commands.test_redial.TestEllaOpenPlayPoliticalNews#test_play_political_news", "historyId": "4b05cca85015ddfc6c9bfc65934f9bcc", "time": {"start": 1754403948451, "stop": 1754403970128, "duration": 21677}, "description": "测试redial指令", "descriptionHtml": "<p>测试redial指令</p>\n", "status": "failed", "statusMessage": "AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', '没有通话记录', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_redial.TestEllaOpenPlayPoliticalNews object at 0x00000240FF305BD0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000240830BB9D0>\n\n    @allure.title(\"测试redial\")\n    @allure.description(\"测试redial指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_play_political_news(self, ella_app):\n        \"\"\"测试redial命令\"\"\"\n        command = \"redial\"\n        app_name = 'contacts'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = []\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', '没有通话记录', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_redial.py:34: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754403935300, "stop": 1754403948450, "duration": 13150}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754403948450, "stop": 1754403948450, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试redial指令", "status": "failed", "statusMessage": "AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', '没有通话记录', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_redial.TestEllaOpenPlayPoliticalNews object at 0x00000240FF305BD0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000240830BB9D0>\n\n    @allure.title(\"测试redial\")\n    @allure.description(\"测试redial指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_play_political_news(self, ella_app):\n        \"\"\"测试redial命令\"\"\"\n        command = \"redial\"\n        app_name = 'contacts'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = []\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', '没有通话记录', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_redial.py:34: AssertionError", "steps": [{"name": "执行命令: redial", "time": {"start": 1754403948452, "stop": 1754403970122, "duration": 21670}, "status": "passed", "steps": [{"name": "执行命令: redial", "time": {"start": 1754403948452, "stop": 1754403969823, "duration": 21371}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754403969823, "stop": 1754403970121, "duration": 298}, "status": "passed", "steps": [], "attachments": [{"uid": "29814e14bf5d793e", "name": "测试总结", "source": "29814e14bf5d793e.txt", "type": "text/plain", "size": 154}, {"uid": "912e60135cbaeae3", "name": "test_completed", "source": "912e60135cbaeae3.png", "type": "image/png", "size": 563791}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754403970122, "stop": 1754403970125, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证contacts已打开", "time": {"start": 1754403970125, "stop": 1754403970126, "duration": 1}, "status": "failed", "statusMessage": "AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', '没有通话记录', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_redial.py\", line 34, in test_play_political_news\n    assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "7214da6e506f0420", "name": "stdout", "source": "7214da6e506f0420.txt", "type": "text/plain", "size": 11709}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754403970138, "stop": 1754403970437, "duration": 299}, "status": "passed", "steps": [], "attachments": [{"uid": "1090519d687b0e72", "name": "失败截图-TestEllaOpenPlayPoliticalNews", "source": "1090519d687b0e72.png", "type": "image/png", "size": 563063}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754403970440, "stop": 1754403971750, "duration": 1310}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_redial"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_redial"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "bee0110354c87879.json", "parameterValues": []}