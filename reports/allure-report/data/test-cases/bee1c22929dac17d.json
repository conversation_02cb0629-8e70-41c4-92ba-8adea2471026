{"uid": "bee1c22929dac17d", "name": "测试set date & time返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_date_time.TestEllaSetDateTime#test_set_date_time", "historyId": "92e2909ea81e82011e43342b4fc06c3b", "time": {"start": 1754454413139, "stop": 1754454427224, "duration": 14085}, "description": "验证set date & time指令返回预期的不支持响应", "descriptionHtml": "<p>验证set date &amp; time指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454400634, "stop": 1754454413138, "duration": 12504}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454413138, "stop": 1754454413138, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set date & time指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set date & time", "time": {"start": 1754454413140, "stop": 1754454427021, "duration": 13881}, "status": "passed", "steps": [{"name": "执行命令: set date & time", "time": {"start": 1754454413140, "stop": 1754454426846, "duration": 13706}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454426846, "stop": 1754454427021, "duration": 175}, "status": "passed", "steps": [], "attachments": [{"uid": "9733367cb4862221", "name": "测试总结", "source": "9733367cb4862221.txt", "type": "text/plain", "size": 215}, {"uid": "c08d8b2dc1b55d77", "name": "test_completed", "source": "c08d8b2dc1b55d77.png", "type": "image/png", "size": 481499}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454427021, "stop": 1754454427023, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454427023, "stop": 1754454427223, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "a6d63a71d45c8bf0", "name": "测试总结", "source": "a6d63a71d45c8bf0.txt", "type": "text/plain", "size": 215}, {"uid": "3ab398082700f0f1", "name": "test_completed", "source": "3ab398082700f0f1.png", "type": "image/png", "size": 481561}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "884f7ad95290e77a", "name": "stdout", "source": "884f7ad95290e77a.txt", "type": "text/plain", "size": 11238}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454427224, "stop": 1754454427224, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454427226, "stop": 1754454428586, "duration": 1360}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_date_time"}, {"name": "subSuite", "value": "TestEllaSetDateTime"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_date_time"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "dff37a5ea2104e4f", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404418443, "stop": 1754404429480, "duration": 11037}}], "categories": [], "tags": ["smoke"]}, "source": "bee1c22929dac17d.json", "parameterValues": []}