{"uid": "bef9248c1ac6d9e8", "name": "测试enable touch optimization返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_touch_optimization.TestEllaEnableTouchOptimization#test_enable_touch_optimization", "historyId": "fc75b92fb4a100575b2c948dd6c5a008", "time": {"start": 1754452795257, "stop": 1754452809330, "duration": 14073}, "description": "验证enable touch optimization指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable touch optimization指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452783273, "stop": 1754452795256, "duration": 11983}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452795256, "stop": 1754452795256, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证enable touch optimization指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable touch optimization", "time": {"start": 1754452795257, "stop": 1754452809137, "duration": 13880}, "status": "passed", "steps": [{"name": "执行命令: enable touch optimization", "time": {"start": 1754452795257, "stop": 1754452808939, "duration": 13682}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452808939, "stop": 1754452809137, "duration": 198}, "status": "passed", "steps": [], "attachments": [{"uid": "412f256641d436b6", "name": "测试总结", "source": "412f256641d436b6.txt", "type": "text/plain", "size": 236}, {"uid": "995a320e1fae8d67", "name": "test_completed", "source": "995a320e1fae8d67.png", "type": "image/png", "size": 502084}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452809137, "stop": 1754452809139, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452809139, "stop": 1754452809330, "duration": 191}, "status": "passed", "steps": [], "attachments": [{"uid": "f94ea76659d93b57", "name": "测试总结", "source": "f94ea76659d93b57.txt", "type": "text/plain", "size": 236}, {"uid": "e01319a14b96b186", "name": "test_completed", "source": "e01319a14b96b186.png", "type": "image/png", "size": 501969}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "70e2c098794b7a5b", "name": "stdout", "source": "70e2c098794b7a5b.txt", "type": "text/plain", "size": 11355}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452809330, "stop": 1754452809330, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452809332, "stop": 1754452810649, "duration": 1317}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_touch_optimization"}, {"name": "subSuite", "value": "TestEllaEnableTouchOptimization"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_touch_optimization"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "1013019944286f04", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402829069, "stop": 1754402840209, "duration": 11140}}], "categories": [], "tags": ["smoke"]}, "source": "bef9248c1ac6d9e8.json", "parameterValues": []}