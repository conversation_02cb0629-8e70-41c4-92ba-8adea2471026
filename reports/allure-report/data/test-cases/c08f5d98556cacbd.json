{"uid": "c08f5d98556cacbd", "name": "测试my phone is too slow能正常执行", "fullName": "testcases.test_ella.component_coupling.test_my_phone_is_too_slow.TestEllaMyPhoneIsTooSlow#test_my_phone_is_too_slow", "historyId": "0a0a3640b2ba4adce516043bd9362070", "time": {"start": 1754446758280, "stop": 1754446772851, "duration": 14571}, "description": "my phone is too slow", "descriptionHtml": "<p>my phone is too slow</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754446746801, "stop": 1754446758278, "duration": 11477}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754446758278, "stop": 1754446758278, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "my phone is too slow", "status": "passed", "steps": [{"name": "执行命令: my phone is too slow", "time": {"start": 1754446758280, "stop": 1754446772635, "duration": 14355}, "status": "passed", "steps": [{"name": "执行命令: my phone is too slow", "time": {"start": 1754446758280, "stop": 1754446772399, "duration": 14119}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446772399, "stop": 1754446772635, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "d4005f7b3db906b6", "name": "测试总结", "source": "d4005f7b3db906b6.txt", "type": "text/plain", "size": 229}, {"uid": "2e51764e95e8c5bb", "name": "test_completed", "source": "2e51764e95e8c5bb.png", "type": "image/png", "size": 552579}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754446772635, "stop": 1754446772637, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446772637, "stop": 1754446772850, "duration": 213}, "status": "passed", "steps": [], "attachments": [{"uid": "30b3191db4d52c6a", "name": "测试总结", "source": "30b3191db4d52c6a.txt", "type": "text/plain", "size": 229}, {"uid": "33983ec19920174b", "name": "test_completed", "source": "33983ec19920174b.png", "type": "image/png", "size": 553130}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "2bb056dad9b1448b", "name": "stdout", "source": "2bb056dad9b1448b.txt", "type": "text/plain", "size": 13920}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754446772851, "stop": 1754446772851, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754446772852, "stop": 1754446774265, "duration": 1413}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_my_phone_is_too_slow"}, {"name": "subSuite", "value": "TestEllaMyPhoneIsTooSlow"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_my_phone_is_too_slow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "1b43953d17bc05b", "status": "passed", "time": {"start": 1754397068436, "stop": 1754397081716, "duration": 13280}}], "categories": [], "tags": ["smoke"]}, "source": "c08f5d98556cacbd.json", "parameterValues": []}