{"uid": "c178e4faa8523e73", "name": "测试i want to watch fireworks能正常执行", "fullName": "testcases.test_ella.dialogue.test_i_want_to_watch_fireworks.TestEllaIWantWatchFireworks#test_i_want_to_watch_fireworks", "historyId": "4ae696581fe41611547bc10ddba4f526", "time": {"start": 1754448236986, "stop": 1754448253582, "duration": 16596}, "description": "i want to watch fireworks", "descriptionHtml": "<p>i want to watch fireworks</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448224327, "stop": 1754448236985, "duration": 12658}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448236985, "stop": 1754448236985, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "i want to watch fireworks", "status": "passed", "steps": [{"name": "执行命令: i want to watch fireworks", "time": {"start": 1754448236986, "stop": 1754448253365, "duration": 16379}, "status": "passed", "steps": [{"name": "执行命令: i want to watch fireworks", "time": {"start": 1754448236986, "stop": 1754448253157, "duration": 16171}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448253157, "stop": 1754448253365, "duration": 208}, "status": "passed", "steps": [], "attachments": [{"uid": "3aed906df8cb00b0", "name": "测试总结", "source": "3aed906df8cb00b0.txt", "type": "text/plain", "size": 202}, {"uid": "cf84d9cd1c07180a", "name": "test_completed", "source": "cf84d9cd1c07180a.png", "type": "image/png", "size": 641276}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448253365, "stop": 1754448253367, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448253367, "stop": 1754448253582, "duration": 215}, "status": "passed", "steps": [], "attachments": [{"uid": "7d4963b94f219605", "name": "测试总结", "source": "7d4963b94f219605.txt", "type": "text/plain", "size": 202}, {"uid": "2cef369e97bfcc83", "name": "test_completed", "source": "2cef369e97bfcc83.png", "type": "image/png", "size": 641273}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "2d7aa027f2f8b17e", "name": "stdout", "source": "2d7aa027f2f8b17e.txt", "type": "text/plain", "size": 11530}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754448253583, "stop": 1754448255014, "duration": 1431}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754448253583, "stop": 1754448253583, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_i_want_to_watch_fireworks"}, {"name": "subSuite", "value": "TestEllaIWantWatchFireworks"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_i_want_to_watch_fireworks"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "526c5f31263fcce1", "status": "passed", "time": {"start": 1754398504513, "stop": 1754398519081, "duration": 14568}}], "categories": [], "tags": ["smoke"]}, "source": "c178e4faa8523e73.json", "parameterValues": []}