{"uid": "c31cf391c54c7cbe", "name": "测试increase settings for special functions返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_increase_settings_for_special_functions.TestEllaIncreaseSettingsSpecialFunctions#test_increase_settings_for_special_functions", "historyId": "5050d8dc816d181ca0c76dc56c8cb5f2", "time": {"start": 1754453064336, "stop": 1754453087849, "duration": 23513}, "description": "验证increase settings for special functions指令返回预期的不支持响应", "descriptionHtml": "<p>验证increase settings for special functions指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453052391, "stop": 1754453064335, "duration": 11944}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453064335, "stop": 1754453064335, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证increase settings for special functions指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: increase settings for special functions", "time": {"start": 1754453064336, "stop": 1754453087661, "duration": 23325}, "status": "passed", "steps": [{"name": "执行命令: increase settings for special functions", "time": {"start": 1754453064336, "stop": 1754453087442, "duration": 23106}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453087442, "stop": 1754453087661, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "bb21cccee00b6612", "name": "测试总结", "source": "bb21cccee00b6612.txt", "type": "text/plain", "size": 270}, {"uid": "22e8db2405960d96", "name": "test_completed", "source": "22e8db2405960d96.png", "type": "image/png", "size": 501218}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453087661, "stop": 1754453087662, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453087662, "stop": 1754453087847, "duration": 185}, "status": "passed", "steps": [], "attachments": [{"uid": "4caace0c26d708a", "name": "测试总结", "source": "4caace0c26d708a.txt", "type": "text/plain", "size": 270}, {"uid": "fc73e341170b958f", "name": "test_completed", "source": "fc73e341170b958f.png", "type": "image/png", "size": 501065}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "ad9e4931a20732a", "name": "stdout", "source": "ad9e4931a20732a.txt", "type": "text/plain", "size": 11924}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453087849, "stop": 1754453087849, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453087849, "stop": 1754453089217, "duration": 1368}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_increase_settings_for_special_functions"}, {"name": "subSuite", "value": "TestEllaIncreaseSettingsSpecialFunctions"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_increase_settings_for_special_functions"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "ab06ac7d19247ec4", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403078975, "stop": 1754403100894, "duration": 21919}}], "categories": [], "tags": ["smoke"]}, "source": "c31cf391c54c7cbe.json", "parameterValues": []}