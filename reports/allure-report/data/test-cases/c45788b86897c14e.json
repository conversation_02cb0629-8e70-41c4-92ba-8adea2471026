{"uid": "c45788b86897c14e", "name": "测试set my fonts返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_my_fonts.TestEllaSetMyFonts#test_set_my_fonts", "historyId": "7d3b4e67344145885187c529ee88a9aa", "time": {"start": 1754454692062, "stop": 1754454705824, "duration": 13762}, "description": "验证set my fonts指令返回预期的不支持响应", "descriptionHtml": "<p>验证set my fonts指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454680357, "stop": 1754454692061, "duration": 11704}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454692061, "stop": 1754454692061, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set my fonts指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set my fonts", "time": {"start": 1754454692062, "stop": 1754454705619, "duration": 13557}, "status": "passed", "steps": [{"name": "执行命令: set my fonts", "time": {"start": 1754454692062, "stop": 1754454705409, "duration": 13347}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454705409, "stop": 1754454705619, "duration": 210}, "status": "passed", "steps": [], "attachments": [{"uid": "487318b498cc2a13", "name": "测试总结", "source": "487318b498cc2a13.txt", "type": "text/plain", "size": 206}, {"uid": "896466224ae04c00", "name": "test_completed", "source": "896466224ae04c00.png", "type": "image/png", "size": 466766}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454705619, "stop": 1754454705620, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454705620, "stop": 1754454705824, "duration": 204}, "status": "passed", "steps": [], "attachments": [{"uid": "bfb136fe7f4f3b58", "name": "测试总结", "source": "bfb136fe7f4f3b58.txt", "type": "text/plain", "size": 206}, {"uid": "fcfe91ba3c9a7dd5", "name": "test_completed", "source": "fcfe91ba3c9a7dd5.png", "type": "image/png", "size": 466375}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "cf461bccea2f6097", "name": "stdout", "source": "cf461bccea2f6097.txt", "type": "text/plain", "size": 11209}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454705825, "stop": 1754454705825, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454705826, "stop": 1754454707170, "duration": 1344}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_my_fonts"}, {"name": "subSuite", "value": "TestEllaSetMyFonts"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_my_fonts"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "9b35f6ec3ea1d67b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404699227, "stop": 1754404714882, "duration": 15655}}], "categories": [], "tags": ["smoke"]}, "source": "c45788b86897c14e.json", "parameterValues": []}