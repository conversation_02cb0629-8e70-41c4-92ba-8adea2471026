{"uid": "c537288c0e1503c5", "name": "测试what's your name？能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_s_your_name.TestEllaWhatSYourName#test_what_s_your_name", "historyId": "c0d6ce0b7c5e41242c01a6e0c0186608", "time": {"start": 1754399264581, "stop": 1754399277656, "duration": 13075}, "description": "what's your name？", "descriptionHtml": "<p>what's your name？</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399251524, "stop": 1754399264580, "duration": 13056}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399264580, "stop": 1754399264580, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "what's your name？", "status": "passed", "steps": [{"name": "执行命令: what's your name？", "time": {"start": 1754399264581, "stop": 1754399277323, "duration": 12742}, "status": "passed", "steps": [{"name": "执行命令: what's your name？", "time": {"start": 1754399264581, "stop": 1754399277006, "duration": 12425}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399277006, "stop": 1754399277322, "duration": 316}, "status": "passed", "steps": [], "attachments": [{"uid": "1d09708cb944b8a4", "name": "测试总结", "source": "1d09708cb944b8a4.txt", "type": "text/plain", "size": 271}, {"uid": "3946ac2fa5ecc540", "name": "test_completed", "source": "3946ac2fa5ecc540.png", "type": "image/png", "size": 592047}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399277323, "stop": 1754399277328, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399277328, "stop": 1754399277654, "duration": 326}, "status": "passed", "steps": [], "attachments": [{"uid": "fb3055768659e60d", "name": "测试总结", "source": "fb3055768659e60d.txt", "type": "text/plain", "size": 271}, {"uid": "5d909c6b6ed04be", "name": "test_completed", "source": "5d909c6b6ed04be.png", "type": "image/png", "size": 591711}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "2cb3cfb6d9fddfb0", "name": "stdout", "source": "2cb3cfb6d9fddfb0.txt", "type": "text/plain", "size": 11565}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399277660, "stop": 1754399277660, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399277664, "stop": 1754399278920, "duration": 1256}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_s_your_name"}, {"name": "subSuite", "value": "TestEllaWhatSYourName"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_s_your_name"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "c537288c0e1503c5.json", "parameterValues": []}