{"uid": "c58e43308502a4be", "name": "测试enable auto pickup返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_auto_pickup.TestEllaEnableAutoPickup#test_enable_auto_pickup", "historyId": "57acf2797af332487c1fdb9a53a30e4f", "time": {"start": 1754452612143, "stop": 1754452626314, "duration": 14171}, "description": "验证enable auto pickup指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable auto pickup指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452599834, "stop": 1754452612143, "duration": 12309}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452612143, "stop": 1754452612143, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证enable auto pickup指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable auto pickup", "time": {"start": 1754452612143, "stop": 1754452626126, "duration": 13983}, "status": "passed", "steps": [{"name": "执行命令: enable auto pickup", "time": {"start": 1754452612144, "stop": 1754452625914, "duration": 13770}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452625914, "stop": 1754452626126, "duration": 212}, "status": "passed", "steps": [], "attachments": [{"uid": "219ed306e5ae6517", "name": "测试总结", "source": "219ed306e5ae6517.txt", "type": "text/plain", "size": 234}, {"uid": "5d8e9c51992e9a3f", "name": "test_completed", "source": "5d8e9c51992e9a3f.png", "type": "image/png", "size": 491743}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452626126, "stop": 1754452626128, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452626128, "stop": 1754452626314, "duration": 186}, "status": "passed", "steps": [], "attachments": [{"uid": "d3a9ca495d1bf726", "name": "测试总结", "source": "d3a9ca495d1bf726.txt", "type": "text/plain", "size": 234}, {"uid": "1344a21c2e2e697f", "name": "test_completed", "source": "1344a21c2e2e697f.png", "type": "image/png", "size": 491678}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "7f371dc3d3baed67", "name": "stdout", "source": "7f371dc3d3baed67.txt", "type": "text/plain", "size": 11314}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452626314, "stop": 1754452626314, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452626316, "stop": 1754452627654, "duration": 1338}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_auto_pickup"}, {"name": "subSuite", "value": "TestEllaEnableAutoPickup"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_auto_pickup"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "8be4e7e21f9f1a96", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402634452, "stop": 1754402649409, "duration": 14957}}], "categories": [], "tags": ["smoke"]}, "source": "c58e43308502a4be.json", "parameterValues": []}