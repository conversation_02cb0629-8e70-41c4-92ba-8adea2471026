{"uid": "c61bdf99a64855b6", "name": "测试enable brightness locking返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_brightness_locking.TestEllaEnableBrightnessLocking#test_enable_brightness_locking", "historyId": "42bb23fa5566b20ae050e85bbee099ef", "time": {"start": 1754452639757, "stop": 1754452653831, "duration": 14074}, "description": "验证enable brightness locking指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable brightness locking指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452627661, "stop": 1754452639756, "duration": 12095}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452639756, "stop": 1754452639756, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证enable brightness locking指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable brightness locking", "time": {"start": 1754452639757, "stop": 1754452653648, "duration": 13891}, "status": "passed", "steps": [{"name": "执行命令: enable brightness locking", "time": {"start": 1754452639757, "stop": 1754452653458, "duration": 13701}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452653458, "stop": 1754452653648, "duration": 190}, "status": "passed", "steps": [], "attachments": [{"uid": "d9c7a2e6f9872bae", "name": "测试总结", "source": "d9c7a2e6f9872bae.txt", "type": "text/plain", "size": 231}, {"uid": "61cf94c5b142260e", "name": "test_completed", "source": "61cf94c5b142260e.png", "type": "image/png", "size": 496476}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452653648, "stop": 1754452653650, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452653650, "stop": 1754452653831, "duration": 181}, "status": "passed", "steps": [], "attachments": [{"uid": "f6fdc0545e65adc1", "name": "测试总结", "source": "f6fdc0545e65adc1.txt", "type": "text/plain", "size": 231}, {"uid": "f56ffff8fb005fc2", "name": "test_completed", "source": "f56ffff8fb005fc2.png", "type": "image/png", "size": 496442}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d2ab3aa08b835d0d", "name": "stdout", "source": "d2ab3aa08b835d0d.txt", "type": "text/plain", "size": 12065}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452653832, "stop": 1754452653832, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452653833, "stop": 1754452655175, "duration": 1342}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_brightness_locking"}, {"name": "subSuite", "value": "TestEllaEnableBrightnessLocking"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_brightness_locking"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "bd8eb7c2a266edaf", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402664220, "stop": 1754402680972, "duration": 16752}}], "categories": [], "tags": ["smoke"]}, "source": "c61bdf99a64855b6.json", "parameterValues": []}