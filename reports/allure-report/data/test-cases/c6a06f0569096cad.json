{"uid": "c6a06f0569096cad", "name": "测试set personal hotspot返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_personal_hotspot.TestEllaSetPersonalHotspot#test_set_personal_hotspot", "historyId": "4f538fc772535a0c0811ad87d3aa9494", "time": {"start": 1754454775037, "stop": 1754454789125, "duration": 14088}, "description": "验证set personal hotspot指令返回预期的不支持响应", "descriptionHtml": "<p>验证set personal hotspot指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454762626, "stop": 1754454775036, "duration": 12410}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454775036, "stop": 1754454775036, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set personal hotspot指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set personal hotspot", "time": {"start": 1754454775037, "stop": 1754454788932, "duration": 13895}, "status": "passed", "steps": [{"name": "执行命令: set personal hotspot", "time": {"start": 1754454775037, "stop": 1754454788732, "duration": 13695}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454788732, "stop": 1754454788932, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "7c71f10db9a76d2f", "name": "测试总结", "source": "7c71f10db9a76d2f.txt", "type": "text/plain", "size": 230}, {"uid": "c036d11670b0b8fb", "name": "test_completed", "source": "c036d11670b0b8fb.png", "type": "image/png", "size": 481209}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454788932, "stop": 1754454788933, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454788933, "stop": 1754454789124, "duration": 191}, "status": "passed", "steps": [], "attachments": [{"uid": "85543bd6fa71f2f", "name": "测试总结", "source": "85543bd6fa71f2f.txt", "type": "text/plain", "size": 230}, {"uid": "2e7f5842083deb2f", "name": "test_completed", "source": "2e7f5842083deb2f.png", "type": "image/png", "size": 480277}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "6cead81a7e55f07c", "name": "stdout", "source": "6cead81a7e55f07c.txt", "type": "text/plain", "size": 11312}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454789126, "stop": 1754454789126, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454789127, "stop": 1754454790447, "duration": 1320}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_personal_hotspot"}, {"name": "subSuite", "value": "TestEllaSetPersonalHotspot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_personal_hotspot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "e6cb42f76280e035", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404783659, "stop": 1754404794243, "duration": 10584}}], "categories": [], "tags": ["smoke"]}, "source": "c6a06f0569096cad.json", "parameterValues": []}