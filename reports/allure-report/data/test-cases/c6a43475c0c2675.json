{"uid": "c6a43475c0c2675", "name": "测试take notes on how to build a treehouse能正常执行", "fullName": "testcases.test_ella.dialogue.test_take_notes_on_how_to_build_a_treehouse.TestEllaTakeNotesHowBuildTreehouse#test_take_notes_on_how_to_build_a_treehouse", "historyId": "772728b3468560788490a3673352724d", "time": {"start": 1754448824871, "stop": 1754448838812, "duration": 13941}, "description": "take notes on how to build a treehouse", "descriptionHtml": "<p>take notes on how to build a treehouse</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448812048, "stop": 1754448824870, "duration": 12822}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448824870, "stop": 1754448824870, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "take notes on how to build a treehouse", "status": "passed", "steps": [{"name": "执行命令: take notes on how to build a treehouse", "time": {"start": 1754448824871, "stop": 1754448838610, "duration": 13739}, "status": "passed", "steps": [{"name": "执行命令: take notes on how to build a treehouse", "time": {"start": 1754448824871, "stop": 1754448838413, "duration": 13542}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448838413, "stop": 1754448838610, "duration": 197}, "status": "passed", "steps": [], "attachments": [{"uid": "4571e9e4536e4b1a", "name": "测试总结", "source": "4571e9e4536e4b1a.txt", "type": "text/plain", "size": 234}, {"uid": "cdc66995da4079d6", "name": "test_completed", "source": "cdc66995da4079d6.png", "type": "image/png", "size": 556626}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448838610, "stop": 1754448838611, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448838611, "stop": 1754448838811, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "3e0f03ba3b6c9dcb", "name": "测试总结", "source": "3e0f03ba3b6c9dcb.txt", "type": "text/plain", "size": 234}, {"uid": "758f4f4bd254dbd2", "name": "test_completed", "source": "758f4f4bd254dbd2.png", "type": "image/png", "size": 556725}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "f0ca5a7684da54c0", "name": "stdout", "source": "f0ca5a7684da54c0.txt", "type": "text/plain", "size": 11425}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448838812, "stop": 1754448838812, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448838813, "stop": 1754448840271, "duration": 1458}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_notes_on_how_to_build_a_treehouse"}, {"name": "subSuite", "value": "TestEllaTakeNotesHowBuildTreehouse"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_notes_on_how_to_build_a_treehouse"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "784ae7c2d07c1340", "status": "passed", "time": {"start": 1754399020354, "stop": 1754399033277, "duration": 12923}}], "categories": [], "tags": ["smoke"]}, "source": "c6a43475c0c2675.json", "parameterValues": []}