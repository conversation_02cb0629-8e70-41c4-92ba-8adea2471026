{"uid": "c7e528d9e62712d9", "name": "测试play music", "fullName": "testcases.test_ella.component_coupling.test_play_music.TestEllaOpenVisha#test_play_music", "historyId": "148d3ba280bfe2b41b8464beec5f6763", "time": {"start": 1754397536624, "stop": 1754397556389, "duration": 19765}, "description": "测试play music指令", "descriptionHtml": "<p>测试play music指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397523707, "stop": 1754397536623, "duration": 12916}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397536623, "stop": 1754397536623, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play music指令", "status": "passed", "steps": [{"name": "执行命令: play music", "time": {"start": 1754397536624, "stop": 1754397556108, "duration": 19484}, "status": "passed", "steps": [{"name": "执行命令: play music", "time": {"start": 1754397536624, "stop": 1754397555824, "duration": 19200}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397555824, "stop": 1754397556107, "duration": 283}, "status": "passed", "steps": [], "attachments": [{"uid": "7d363bcb03fbde61", "name": "测试总结", "source": "7d363bcb03fbde61.txt", "type": "text/plain", "size": 484}, {"uid": "49b6c92fd381c0a5", "name": "test_completed", "source": "49b6c92fd381c0a5.png", "type": "image/png", "size": 529212}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754397556108, "stop": 1754397556111, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证visha已打开", "time": {"start": 1754397556111, "stop": 1754397556111, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397556111, "stop": 1754397556388, "duration": 277}, "status": "passed", "steps": [], "attachments": [{"uid": "2740502443a7806b", "name": "测试总结", "source": "2740502443a7806b.txt", "type": "text/plain", "size": 484}, {"uid": "784c39238e3fcb37", "name": "test_completed", "source": "784c39238e3fcb37.png", "type": "image/png", "size": 529212}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "22362825697f80ee", "name": "stdout", "source": "22362825697f80ee.txt", "type": "text/plain", "size": 15052}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397556390, "stop": 1754397556390, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397556394, "stop": 1754397557652, "duration": 1258}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_music"}, {"name": "subSuite", "value": "TestEllaOpen<PERSON>a"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "c7e528d9e62712d9.json", "parameterValues": []}