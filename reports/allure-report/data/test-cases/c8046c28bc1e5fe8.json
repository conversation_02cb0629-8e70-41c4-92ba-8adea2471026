{"uid": "c8046c28bc1e5fe8", "name": "测试make a call能正常执行", "fullName": "testcases.test_ella.dialogue.test_make_a_call.TestEllaMakeCall#test_make_a_call", "historyId": "2428ad915810150c12838b88ee13f49c", "time": {"start": 1754448296498, "stop": 1754448319012, "duration": 22514}, "description": "make a call", "descriptionHtml": "<p>make a call</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448283917, "stop": 1754448296497, "duration": 12580}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448296497, "stop": 1754448296497, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "make a call", "status": "passed", "steps": [{"name": "执行命令: make a call", "time": {"start": 1754448296498, "stop": 1754448318790, "duration": 22292}, "status": "passed", "steps": [{"name": "执行命令: make a call", "time": {"start": 1754448296498, "stop": 1754448318606, "duration": 22108}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448318606, "stop": 1754448318790, "duration": 184}, "status": "passed", "steps": [], "attachments": [{"uid": "2f684fd685d70742", "name": "测试总结", "source": "2f684fd685d70742.txt", "type": "text/plain", "size": 173}, {"uid": "97cce97a56d38c5c", "name": "test_completed", "source": "97cce97a56d38c5c.png", "type": "image/png", "size": 560384}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448318790, "stop": 1754448318792, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448318792, "stop": 1754448319012, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "9f6aa894ac2fdf04", "name": "测试总结", "source": "9f6aa894ac2fdf04.txt", "type": "text/plain", "size": 173}, {"uid": "cd52d1dbd90f5210", "name": "test_completed", "source": "cd52d1dbd90f5210.png", "type": "image/png", "size": 560260}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "7f091bd72aa82e63", "name": "stdout", "source": "7f091bd72aa82e63.txt", "type": "text/plain", "size": 11515}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448319013, "stop": 1754448319013, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448319014, "stop": 1754448320387, "duration": 1373}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_make_a_call"}, {"name": "subSuite", "value": "TestEllaMakeCall"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_make_a_call"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "7eb67937aa9b6276", "status": "passed", "time": {"start": 1754398561070, "stop": 1754398582929, "duration": 21859}}], "categories": [], "tags": ["smoke"]}, "source": "c8046c28bc1e5fe8.json", "parameterValues": []}