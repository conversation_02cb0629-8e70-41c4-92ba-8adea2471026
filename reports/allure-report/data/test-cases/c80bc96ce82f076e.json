{"uid": "c80bc96ce82f076e", "name": "测试open camera能正常执行", "fullName": "testcases.test_ella.component_coupling.test_open_camera.TestEllaCommandConcise#test_open_camera", "historyId": "ae0ee984c3712fd05ea04b52289e14fe", "time": {"start": 1754446813335, "stop": 1754446831354, "duration": 18019}, "description": "open camera", "descriptionHtml": "<p>open camera</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754446801608, "stop": 1754446813334, "duration": 11726}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754446813334, "stop": 1754446813334, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "open camera", "status": "passed", "steps": [{"name": "执行命令: open camera", "time": {"start": 1754446813335, "stop": 1754446831159, "duration": 17824}, "status": "passed", "steps": [{"name": "执行命令: open camera", "time": {"start": 1754446813335, "stop": 1754446830927, "duration": 17592}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446830927, "stop": 1754446831159, "duration": 232}, "status": "passed", "steps": [], "attachments": [{"uid": "34c08ecc86911476", "name": "测试总结", "source": "34c08ecc86911476.txt", "type": "text/plain", "size": 248}, {"uid": "b8354b201ddc6201", "name": "test_completed", "source": "b8354b201ddc6201.png", "type": "image/png", "size": 552613}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1754446831159, "stop": 1754446831160, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754446831160, "stop": 1754446831160, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446831160, "stop": 1754446831353, "duration": 193}, "status": "passed", "steps": [], "attachments": [{"uid": "5a1db3b55c62a869", "name": "测试总结", "source": "5a1db3b55c62a869.txt", "type": "text/plain", "size": 248}, {"uid": "e67c85362603d017", "name": "test_completed", "source": "e67c85362603d017.png", "type": "image/png", "size": 552813}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "ffe18811a4fe3f30", "name": "stdout", "source": "ffe18811a4fe3f30.txt", "type": "text/plain", "size": 14519}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754446831354, "stop": 1754446831354, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754446831355, "stop": 1754446832778, "duration": 1423}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_camera"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_camera"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "30504da5d19f1072", "status": "passed", "time": {"start": 1754397121136, "stop": 1754397138079, "duration": 16943}}], "categories": [], "tags": ["smoke"]}, "source": "c80bc96ce82f076e.json", "parameterValues": []}