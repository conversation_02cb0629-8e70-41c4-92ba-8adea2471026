{"uid": "c8517a0d9fe0ff9f", "name": "测试take a photo能正常执行", "fullName": "testcases.test_ella.system_coupling.test_take_a_photo.TestEllaTakePhoto#test_take_a_photo", "historyId": "c076d6e18e779bfeb810e69b30339aa2", "time": {"start": 1754450772328, "stop": 1754450802490, "duration": 30162}, "description": "take a photo", "descriptionHtml": "<p>take a photo</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450759780, "stop": 1754450772326, "duration": 12546}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450772326, "stop": 1754450772327, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "take a photo", "status": "passed", "steps": [{"name": "执行命令: take a photo", "time": {"start": 1754450772328, "stop": 1754450802227, "duration": 29899}, "status": "passed", "steps": [{"name": "执行命令: take a photo", "time": {"start": 1754450772328, "stop": 1754450801983, "duration": 29655}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450801983, "stop": 1754450802227, "duration": 244}, "status": "passed", "steps": [], "attachments": [{"uid": "42bd82901f91405f", "name": "测试总结", "source": "42bd82901f91405f.txt", "type": "text/plain", "size": 250}, {"uid": "dcea1265a9c8339e", "name": "test_completed", "source": "dcea1265a9c8339e.png", "type": "image/png", "size": 567678}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450802227, "stop": 1754450802229, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754450802229, "stop": 1754450802229, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证文件存在", "time": {"start": 1754450802229, "stop": 1754450802229, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450802229, "stop": 1754450802490, "duration": 261}, "status": "passed", "steps": [], "attachments": [{"uid": "99d736784ab55938", "name": "测试总结", "source": "99d736784ab55938.txt", "type": "text/plain", "size": 250}, {"uid": "590d73edabbe8212", "name": "test_completed", "source": "590d73edabbe8212.png", "type": "image/png", "size": 568384}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "a0e969a291a3a3de", "name": "stdout", "source": "a0e969a291a3a3de.txt", "type": "text/plain", "size": 15408}], "parameters": [], "attachmentStep": false, "stepsCount": 7, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450802491, "stop": 1754450802491, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450802491, "stop": 1754450803886, "duration": 1395}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "模块方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_take_a_photo"}, {"name": "subSuite", "value": "TestEllaTakePhoto"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_take_a_photo"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "577708211108941c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754400926509, "stop": 1754400955674, "duration": 29165}}], "categories": [], "tags": ["smoke"]}, "source": "c8517a0d9fe0ff9f.json", "parameterValues": []}