{"uid": "c85c9199aaaea251", "name": "测试turn off driving mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_turn_off_driving_mode.TestEllaTurnOffDrivingMode#test_turn_off_driving_mode", "historyId": "984a0fd313bba0ca2f20f0bbff732eb8", "time": {"start": 1754455379237, "stop": 1754455392992, "duration": 13755}, "description": "验证turn off driving mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证turn off driving mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455367446, "stop": 1754455379235, "duration": 11789}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455379236, "stop": 1754455379236, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证turn off driving mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: turn off driving mode", "time": {"start": 1754455379238, "stop": 1754455392798, "duration": 13560}, "status": "passed", "steps": [{"name": "执行命令: turn off driving mode", "time": {"start": 1754455379238, "stop": 1754455392598, "duration": 13360}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455392598, "stop": 1754455392797, "duration": 199}, "status": "passed", "steps": [], "attachments": [{"uid": "d4681e79bbc43d85", "name": "测试总结", "source": "d4681e79bbc43d85.txt", "type": "text/plain", "size": 235}, {"uid": "a047443e81e62c94", "name": "test_completed", "source": "a047443e81e62c94.png", "type": "image/png", "size": 488700}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455392798, "stop": 1754455392798, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455392798, "stop": 1754455392992, "duration": 194}, "status": "passed", "steps": [], "attachments": [{"uid": "95310ef2f502a20", "name": "测试总结", "source": "95310ef2f502a20.txt", "type": "text/plain", "size": 235}, {"uid": "944e7e305e5e615f", "name": "test_completed", "source": "944e7e305e5e615f.png", "type": "image/png", "size": 488864}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "afd12f11fd58fed4", "name": "stdout", "source": "afd12f11fd58fed4.txt", "type": "text/plain", "size": 11330}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455392994, "stop": 1754455392994, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455392995, "stop": 1754455394334, "duration": 1339}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_off_driving_mode"}, {"name": "subSuite", "value": "TestEllaTurnOffDrivingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_off_driving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "a64c108c7b429dc8", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405422601, "stop": 1754405433840, "duration": 11239}}], "categories": [], "tags": ["smoke"]}, "source": "c85c9199aaaea251.json", "parameterValues": []}