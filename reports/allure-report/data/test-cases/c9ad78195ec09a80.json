{"uid": "c9ad78195ec09a80", "name": "测试play political news", "fullName": "testcases.test_ella.dialogue.test_play_political_news.TestEllaOpenPlayPoliticalNews#test_play_political_news", "historyId": "1bf9bd9c91ab7da6f818ff587cfff7da", "time": {"start": 1754448392398, "stop": 1754448422888, "duration": 30490}, "description": "测试play political news指令", "descriptionHtml": "<p>测试play political news指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448379749, "stop": 1754448392397, "duration": 12648}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448392397, "stop": 1754448392397, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play political news指令", "status": "passed", "steps": [{"name": "执行命令: play political news", "time": {"start": 1754448392398, "stop": 1754448422694, "duration": 30296}, "status": "passed", "steps": [{"name": "执行命令: play political news", "time": {"start": 1754448392398, "stop": 1754448422427, "duration": 30029}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448422427, "stop": 1754448422694, "duration": 267}, "status": "passed", "steps": [], "attachments": [{"uid": "bfd05efe37bcdd1c", "name": "测试总结", "source": "bfd05efe37bcdd1c.txt", "type": "text/plain", "size": 1652}, {"uid": "58fa7e9d766c42be", "name": "test_completed", "source": "58fa7e9d766c42be.png", "type": "image/png", "size": 841240}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448422694, "stop": 1754448422695, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448422695, "stop": 1754448422887, "duration": 192}, "status": "passed", "steps": [], "attachments": [{"uid": "f2917b245da0ce54", "name": "测试总结", "source": "f2917b245da0ce54.txt", "type": "text/plain", "size": 1652}, {"uid": "c199d2f3f3c043ea", "name": "test_completed", "source": "c199d2f3f3c043ea.png", "type": "image/png", "size": 840084}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "1946943144b4e86f", "name": "stdout", "source": "1946943144b4e86f.txt", "type": "text/plain", "size": 18391}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448422888, "stop": 1754448422888, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448422889, "stop": 1754448424290, "duration": 1401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_political_news"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_political_news"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "9cfb216f65c6bc92", "status": "passed", "time": {"start": 1754398654100, "stop": 1754398669905, "duration": 15805}}], "categories": [], "tags": ["smoke"]}, "source": "c9ad78195ec09a80.json", "parameterValues": []}