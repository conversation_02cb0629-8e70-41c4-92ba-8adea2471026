{"uid": "ca1a3155aaeee8a9", "name": "测试close ella能正常执行", "fullName": "testcases.test_ella.component_coupling.test_close_ella.TestEllaCloseElla#test_close_ella", "historyId": "54b47105d42d2a9f18eec071fba40c73", "time": {"start": 1754396835533, "stop": 1754396865403, "duration": 29870}, "description": "close ella", "descriptionHtml": "<p>close ella</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754396822706, "stop": 1754396835531, "duration": 12825}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754396835531, "stop": 1754396835531, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "close ella", "status": "passed", "steps": [{"name": "执行命令: close ella", "time": {"start": 1754396835533, "stop": 1754396864968, "duration": 29435}, "status": "passed", "steps": [{"name": "执行命令: close ella", "time": {"start": 1754396835533, "stop": 1754396864626, "duration": 29093}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754396864626, "stop": 1754396864962, "duration": 336}, "status": "passed", "steps": [], "attachments": [{"uid": "9a617aa1b68202f7", "name": "测试总结", "source": "9a617aa1b68202f7.txt", "type": "text/plain", "size": 655}, {"uid": "2f86c68e41600fbb", "name": "test_completed", "source": "2f86c68e41600fbb.png", "type": "image/png", "size": 486853}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证已打开", "time": {"start": 1754396864968, "stop": 1754396864968, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754396864968, "stop": 1754396865402, "duration": 434}, "status": "passed", "steps": [], "attachments": [{"uid": "39a4a9de4e21ae81", "name": "测试总结", "source": "39a4a9de4e21ae81.txt", "type": "text/plain", "size": 655}, {"uid": "2a5ece4648069709", "name": "test_completed", "source": "2a5ece4648069709.png", "type": "image/png", "size": 487021}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "c899275dc55a6ae4", "name": "stdout", "source": "c899275dc55a6ae4.txt", "type": "text/plain", "size": 18914}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754396865407, "stop": 1754396865407, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754396865409, "stop": 1754396866723, "duration": 1314}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_ella"}, {"name": "subSuite", "value": "TestEllaClose<PERSON>lla"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_ella"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "ca1a3155aaeee8a9.json", "parameterValues": []}