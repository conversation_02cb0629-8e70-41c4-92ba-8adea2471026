{"uid": "ca70ed4533183a70", "name": "测试how is the weather today能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_is_the_weather_today.TestEllaHowIsWeatherToday#test_how_is_the_weather_today", "historyId": "3004e41c81a7ebd857f79d043aaf59df", "time": {"start": 1754447984022, "stop": 1754448004637, "duration": 20615}, "description": "how is the weather today", "descriptionHtml": "<p>how is the weather today</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447971650, "stop": 1754447984021, "duration": 12371}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447984021, "stop": 1754447984021, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "how is the weather today", "status": "passed", "steps": [{"name": "执行命令: how is the weather today", "time": {"start": 1754447984022, "stop": 1754448004452, "duration": 20430}, "status": "passed", "steps": [{"name": "执行命令: how is the weather today", "time": {"start": 1754447984022, "stop": 1754448004274, "duration": 20252}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448004274, "stop": 1754448004452, "duration": 178}, "status": "passed", "steps": [], "attachments": [{"uid": "58517fed97546a64", "name": "测试总结", "source": "58517fed97546a64.txt", "type": "text/plain", "size": 264}, {"uid": "179d99f97f7bdd41", "name": "test_completed", "source": "179d99f97f7bdd41.png", "type": "image/png", "size": 569685}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448004452, "stop": 1754448004453, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448004453, "stop": 1754448004637, "duration": 184}, "status": "passed", "steps": [], "attachments": [{"uid": "15ae9b0eeb9a341c", "name": "测试总结", "source": "15ae9b0eeb9a341c.txt", "type": "text/plain", "size": 264}, {"uid": "c617cf6745baf614", "name": "test_completed", "source": "c617cf6745baf614.png", "type": "image/png", "size": 568993}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "28e2948177b3dc7b", "name": "stdout", "source": "28e2948177b3dc7b.txt", "type": "text/plain", "size": 12393}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448004637, "stop": 1754448004637, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448004638, "stop": 1754448006014, "duration": 1376}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_is_the_weather_today"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_is_the_weather_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "9c64e1e3896872ea", "status": "passed", "time": {"start": 1754398256807, "stop": 1754398276706, "duration": 19899}}], "categories": [], "tags": ["smoke"]}, "source": "ca70ed4533183a70.json", "parameterValues": []}