{"uid": "cab4b6ad694cf9cc", "name": "测试open contact命令 - 简洁版本", "fullName": "testcases.test_ella.dialogue.test_open_app.TestEllaCommandConcise#test_open_app", "historyId": "f5346ff0fa4cb76e4b6ceea6116693ee", "time": {"start": 1754448332869, "stop": 1754448346643, "duration": 13774}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448320390, "stop": 1754448332869, "duration": 12479}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448332869, "stop": 1754448332869, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "passed", "steps": [{"name": "执行命令: open app", "time": {"start": 1754448332870, "stop": 1754448346447, "duration": 13577}, "status": "passed", "steps": [{"name": "执行命令: open app", "time": {"start": 1754448332870, "stop": 1754448346237, "duration": 13367}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448346237, "stop": 1754448346446, "duration": 209}, "status": "passed", "steps": [], "attachments": [{"uid": "1c56a8577186384f", "name": "测试总结", "source": "1c56a8577186384f.txt", "type": "text/plain", "size": 162}, {"uid": "44b57cbbb3d158ae", "name": "test_completed", "source": "44b57cbbb3d158ae.png", "type": "image/png", "size": 527833}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含Done", "time": {"start": 1754448346447, "stop": 1754448346448, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448346448, "stop": 1754448346643, "duration": 195}, "status": "passed", "steps": [], "attachments": [{"uid": "722b3101f9e8ced0", "name": "测试总结", "source": "722b3101f9e8ced0.txt", "type": "text/plain", "size": 162}, {"uid": "9b72659f4039eeb", "name": "test_completed", "source": "9b72659f4039eeb.png", "type": "image/png", "size": 528210}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "e13beff1fcb84a89", "name": "stdout", "source": "e13beff1fcb84a89.txt", "type": "text/plain", "size": 11211}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448346644, "stop": 1754448346644, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448346645, "stop": 1754448348034, "duration": 1389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_open_app"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_open_app"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "bf5eddc2520444f8", "status": "passed", "time": {"start": 1754398597132, "stop": 1754398609851, "duration": 12719}}], "categories": [], "tags": ["smoke"]}, "source": "cab4b6ad694cf9cc.json", "parameterValues": []}