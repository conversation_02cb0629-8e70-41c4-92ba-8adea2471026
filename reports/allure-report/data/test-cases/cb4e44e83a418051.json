{"uid": "cb4e44e83a418051", "name": "测试display the route go company", "fullName": "testcases.test_ella.component_coupling.test_display_the_route_go_company.TestEllaOpenMaps#test_display_the_route_go_company", "historyId": "f4d12b1367b35df96178a58e48fe8f5e", "time": {"start": 1754397041032, "stop": 1754397054977, "duration": 13945}, "description": "测试display the route go company指令", "descriptionHtml": "<p>测试display the route go company指令</p>\n", "status": "failed", "statusMessage": "AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', 'No company address set up yet.', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.component_coupling.test_display_the_route_go_company.TestEllaOpenMaps object at 0x00000240FEFE2210>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000240FF0B6190>\n\n    @allure.title(\"测试display the route go company\")\n    @allure.description(\"测试display the route go company指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_display_the_route_go_company(self, ella_app):\n        \"\"\"测试display the route go company命令\"\"\"\n        command = \"display the route go company\"\n        app_name = 'maps'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     expected_text = ['done']\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', 'No company address set up yet.', '', '']'\nE           assert False\n\ntestcases\\test_ella\\component_coupling\\test_display_the_route_go_company.py:34: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397027869, "stop": 1754397041031, "duration": 13162}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397041032, "stop": 1754397041032, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试display the route go company指令", "status": "failed", "statusMessage": "AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', 'No company address set up yet.', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.component_coupling.test_display_the_route_go_company.TestEllaOpenMaps object at 0x00000240FEFE2210>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000240FF0B6190>\n\n    @allure.title(\"测试display the route go company\")\n    @allure.description(\"测试display the route go company指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_display_the_route_go_company(self, ella_app):\n        \"\"\"测试display the route go company命令\"\"\"\n        command = \"display the route go company\"\n        app_name = 'maps'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     expected_text = ['done']\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', 'No company address set up yet.', '', '']'\nE           assert False\n\ntestcases\\test_ella\\component_coupling\\test_display_the_route_go_company.py:34: AssertionError", "steps": [{"name": "执行命令: display the route go company", "time": {"start": 1754397041032, "stop": 1754397054976, "duration": 13944}, "status": "passed", "steps": [{"name": "执行命令: display the route go company", "time": {"start": 1754397041032, "stop": 1754397054637, "duration": 13605}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397054637, "stop": 1754397054971, "duration": 334}, "status": "passed", "steps": [], "attachments": [{"uid": "50ad87a09477d74a", "name": "测试总结", "source": "50ad87a09477d74a.txt", "type": "text/plain", "size": 210}, {"uid": "bea6764d1d43eac2", "name": "test_completed", "source": "bea6764d1d43eac2.png", "type": "image/png", "size": 551585}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证maps已打开", "time": {"start": 1754397054976, "stop": 1754397054976, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', 'No company address set up yet.', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\component_coupling\\test_display_the_route_go_company.py\", line 34, in test_display_the_route_go_company\n    assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "9ab0d7ec50716444", "name": "stdout", "source": "9ab0d7ec50716444.txt", "type": "text/plain", "size": 11547}], "parameters": [], "attachmentStep": false, "stepsCount": 4, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397055001, "stop": 1754397055307, "duration": 306}, "status": "passed", "steps": [], "attachments": [{"uid": "ad85cc7dc325463d", "name": "失败截图-TestEllaOpenMaps", "source": "ad85cc7dc325463d.png", "type": "image/png", "size": 551788}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754397055311, "stop": 1754397056595, "duration": 1284}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_display_the_route_go_company"}, {"name": "subSuite", "value": "TestEllaOpenMaps"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_display_the_route_go_company"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "cb4e44e83a418051.json", "parameterValues": []}