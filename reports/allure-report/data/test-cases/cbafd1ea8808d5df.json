{"uid": "cbafd1ea8808d5df", "name": "测试open calculator", "fullName": "testcases.test_ella.open_app.test_open_calculator.TestEllaOpenCalculator#test_open_calculator", "historyId": "fead54f5f2ccb14942215ef4a8f481bf", "time": {"start": 1754399487911, "stop": 1754399503355, "duration": 15444}, "description": "测试open calculator指令", "descriptionHtml": "<p>测试open calculator指令</p>\n", "status": "failed", "statusMessage": "AssertionError: calculator: 初始=None, 最终=None, 响应='['open calculator', 'Done!', '', '', '[com.transsion.calculator页面] 未获取到文本内容']'\nassert None", "statusTrace": "self = <test_open_calculator.TestEllaOpenCalculator object at 0x00000240FF0B4A50>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002408150B550>\n\n    @allure.title(\"测试open calculator\")\n    @allure.description(\"测试open calculator指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_calculator(self, ella_app):\n        \"\"\"测试open calculator命令\"\"\"\n        command = \"open calculator\"\n        expected_text = ['Done']\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证calculator已打开\"):\n>           assert final_status, f\"calculator: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: calculator: 初始=None, 最终=None, 响应='['open calculator', 'Done!', '', '', '[com.transsion.calculator页面] 未获取到文本内容']'\nE           assert None\n\ntestcases\\test_ella\\open_app\\test_open_calculator.py:33: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399474973, "stop": 1754399487910, "duration": 12937}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399487911, "stop": 1754399487911, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试open calculator指令", "status": "failed", "statusMessage": "AssertionError: calculator: 初始=None, 最终=None, 响应='['open calculator', 'Done!', '', '', '[com.transsion.calculator页面] 未获取到文本内容']'\nassert None", "statusTrace": "self = <test_open_calculator.TestEllaOpenCalculator object at 0x00000240FF0B4A50>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002408150B550>\n\n    @allure.title(\"测试open calculator\")\n    @allure.description(\"测试open calculator指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_calculator(self, ella_app):\n        \"\"\"测试open calculator命令\"\"\"\n        command = \"open calculator\"\n        expected_text = ['Done']\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证calculator已打开\"):\n>           assert final_status, f\"calculator: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: calculator: 初始=None, 最终=None, 响应='['open calculator', 'Done!', '', '', '[com.transsion.calculator页面] 未获取到文本内容']'\nE           assert None\n\ntestcases\\test_ella\\open_app\\test_open_calculator.py:33: AssertionError", "steps": [{"name": "执行命令: open calculator", "time": {"start": 1754399487911, "stop": 1754399503351, "duration": 15440}, "status": "passed", "steps": [{"name": "执行命令: open calculator", "time": {"start": 1754399487911, "stop": 1754399503089, "duration": 15178}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399503089, "stop": 1754399503350, "duration": 261}, "status": "passed", "steps": [], "attachments": [{"uid": "fee8d5f9603dd3b4", "name": "测试总结", "source": "fee8d5f9603dd3b4.txt", "type": "text/plain", "size": 218}, {"uid": "6d77cd62fe1e9d16", "name": "test_completed", "source": "6d77cd62fe1e9d16.png", "type": "image/png", "size": 518011}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399503351, "stop": 1754399503354, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证calculator已打开", "time": {"start": 1754399503354, "stop": 1754399503354, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: calculator: 初始=None, 最终=None, 响应='['open calculator', 'Done!', '', '', '[com.transsion.calculator页面] 未获取到文本内容']'\nassert None\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\open_app\\test_open_calculator.py\", line 33, in test_open_calculator\n    assert final_status, f\"calculator: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "1e1dd53bd4f58a2a", "name": "stdout", "source": "1e1dd53bd4f58a2a.txt", "type": "text/plain", "size": 14480}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399503365, "stop": 1754399503639, "duration": 274}, "status": "passed", "steps": [], "attachments": [{"uid": "88ccf45041e9d480", "name": "失败截图-TestEllaOpenCalculator", "source": "88ccf45041e9d480.png", "type": "image/png", "size": 518011}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754399503641, "stop": 1754399504876, "duration": 1235}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_calculator"}, {"name": "subSuite", "value": "TestEllaOpenCalculator"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_calculator"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "cbafd1ea8808d5df.json", "parameterValues": []}