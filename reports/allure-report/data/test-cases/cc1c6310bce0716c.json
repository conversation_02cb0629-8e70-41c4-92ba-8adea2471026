{"uid": "cc1c6310bce0716c", "name": "测试jump to battery usage返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_battery_usage.TestEllaJumpBatteryUsage#test_jump_to_battery_usage", "historyId": "4276e587385154206726240ad06acd24", "time": {"start": 1754453231758, "stop": 1754453246777, "duration": 15019}, "description": "验证jump to battery usage指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to battery usage指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453219186, "stop": 1754453231756, "duration": 12570}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453231756, "stop": 1754453231756, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证jump to battery usage指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: jump to battery usage", "time": {"start": 1754453231758, "stop": 1754453246612, "duration": 14854}, "status": "passed", "steps": [{"name": "执行命令: jump to battery usage", "time": {"start": 1754453231758, "stop": 1754453246415, "duration": 14657}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453246415, "stop": 1754453246612, "duration": 197}, "status": "passed", "steps": [], "attachments": [{"uid": "4b65002396463f32", "name": "测试总结", "source": "4b65002396463f32.txt", "type": "text/plain", "size": 229}, {"uid": "a4fea6c3e0b7d848", "name": "test_completed", "source": "a4fea6c3e0b7d848.png", "type": "image/png", "size": 494781}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453246612, "stop": 1754453246613, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453246613, "stop": 1754453246777, "duration": 164}, "status": "passed", "steps": [], "attachments": [{"uid": "dc5a39b3986684e5", "name": "测试总结", "source": "dc5a39b3986684e5.txt", "type": "text/plain", "size": 229}, {"uid": "79dbab9b4117545d", "name": "test_completed", "source": "79dbab9b4117545d.png", "type": "image/png", "size": 495255}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "c5c813e59a2b8125", "name": "stdout", "source": "c5c813e59a2b8125.txt", "type": "text/plain", "size": 11838}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453246778, "stop": 1754453246778, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453246779, "stop": 1754453248175, "duration": 1396}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_battery_usage"}, {"name": "subSuite", "value": "TestEllaJumpBatteryUsage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_battery_usage"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "d731e6c34bccd94c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403248455, "stop": 1754403260041, "duration": 11586}}], "categories": [], "tags": ["smoke"]}, "source": "cc1c6310bce0716c.json", "parameterValues": []}