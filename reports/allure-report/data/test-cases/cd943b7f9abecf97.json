{"uid": "cd943b7f9abecf97", "name": "测试turn off wifi能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_off_wifi.TestEllaTurnOffWifi#test_turn_off_wifi", "historyId": "7d4e24549f0551613cc723b633b9d3c8", "time": {"start": 1754450949206, "stop": 1754450963531, "duration": 14325}, "description": "turn off wifi", "descriptionHtml": "<p>turn off wifi</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450936730, "stop": 1754450949204, "duration": 12474}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450949204, "stop": 1754450949204, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "turn off wifi", "status": "passed", "steps": [{"name": "执行命令: turn off wifi", "time": {"start": 1754450949206, "stop": 1754450963353, "duration": 14147}, "status": "passed", "steps": [{"name": "执行命令: turn off wifi", "time": {"start": 1754450949206, "stop": 1754450963135, "duration": 13929}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450963135, "stop": 1754450963353, "duration": 218}, "status": "passed", "steps": [], "attachments": [{"uid": "26dddc15b2a3f263", "name": "测试总结", "source": "26dddc15b2a3f263.txt", "type": "text/plain", "size": 178}, {"uid": "5150e645b30a8bf6", "name": "test_completed", "source": "5150e645b30a8bf6.png", "type": "image/png", "size": 484083}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450963353, "stop": 1754450963354, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754450963354, "stop": 1754450963354, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450963354, "stop": 1754450963531, "duration": 177}, "status": "passed", "steps": [], "attachments": [{"uid": "a6b38fa73a8d10f1", "name": "测试总结", "source": "a6b38fa73a8d10f1.txt", "type": "text/plain", "size": 178}, {"uid": "1f8b2d42375dc2f2", "name": "test_completed", "source": "1f8b2d42375dc2f2.png", "type": "image/png", "size": 483279}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "b6d577292ba1d3ff", "name": "stdout", "source": "b6d577292ba1d3ff.txt", "type": "text/plain", "size": 11723}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450963532, "stop": 1754450963532, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450963533, "stop": 1754450964889, "duration": 1356}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_off_wifi"}, {"name": "subSuite", "value": "TestEllaTurnOffWifi"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_off_wifi"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "cc02ed079b866cfb", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Wi-<PERSON> is turned off now']\nassert False", "time": {"start": 1754401106177, "stop": 1754401118040, "duration": 11863}}], "categories": [], "tags": ["smoke"]}, "source": "cd943b7f9abecf97.json", "parameterValues": []}