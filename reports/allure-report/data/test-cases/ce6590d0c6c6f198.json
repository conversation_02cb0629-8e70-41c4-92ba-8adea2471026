{"uid": "ce6590d0c6c6f198", "name": "测试play jay chou's music", "fullName": "testcases.test_ella.component_coupling.test_play_jay_chou_s_music.TestEllaOpenMusic#test_play_jay_chou_s_music", "historyId": "b4e75f584d82368436f820de28f92cfd", "time": {"start": 1754397470669, "stop": 1754397492320, "duration": 21651}, "description": "测试play jay chou's music指令", "descriptionHtml": "<p>测试play jay chou's music指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397457670, "stop": 1754397470668, "duration": 12998}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397470668, "stop": 1754397470668, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play jay chou's music指令", "status": "passed", "steps": [{"name": "执行命令: play jay chou's music", "time": {"start": 1754397470669, "stop": 1754397492036, "duration": 21367}, "status": "passed", "steps": [{"name": "执行命令: play jay chou's music", "time": {"start": 1754397470669, "stop": 1754397491730, "duration": 21061}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397491730, "stop": 1754397492035, "duration": 305}, "status": "passed", "steps": [], "attachments": [{"uid": "558246ea888c5293", "name": "测试总结", "source": "558246ea888c5293.txt", "type": "text/plain", "size": 506}, {"uid": "c09a66a2d3ce31d9", "name": "test_completed", "source": "c09a66a2d3ce31d9.png", "type": "image/png", "size": 530018}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754397492036, "stop": 1754397492039, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证music已打开", "time": {"start": 1754397492039, "stop": 1754397492039, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397492039, "stop": 1754397492319, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "43357c74bb9a15a3", "name": "测试总结", "source": "43357c74bb9a15a3.txt", "type": "text/plain", "size": 506}, {"uid": "696ac8fd97d973e2", "name": "test_completed", "source": "696ac8fd97d973e2.png", "type": "image/png", "size": 530418}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "f3eaf4018f67d92", "name": "stdout", "source": "f3eaf4018f67d92.txt", "type": "text/plain", "size": 15151}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397492323, "stop": 1754397492323, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397492325, "stop": 1754397493583, "duration": 1258}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_jay_chou_s_music"}, {"name": "subSuite", "value": "TestEllaOpenMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_jay_chou_s_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "ce6590d0c6c6f198.json", "parameterValues": []}