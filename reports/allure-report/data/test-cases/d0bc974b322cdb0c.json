{"uid": "d0bc974b322cdb0c", "name": "测试What's the weather like in Shanghai today能正常执行", "fullName": "testcases.test_ella.component_coupling.test_what_s_the_weather_like_in_shanghai_today.TestEllaWhatSWeatherLikeShanghaiToday#test_what_s_the_weather_like_in_shanghai_today", "historyId": "53ec4c77118606257c016fc2f7b22065", "time": {"start": 1754397807292, "stop": 1754397827627, "duration": 20335}, "description": "What's the weather like in Shanghai today", "descriptionHtml": "<p>What's the weather like in Shanghai today</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397794211, "stop": 1754397807292, "duration": 13081}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397807292, "stop": 1754397807292, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "What's the weather like in Shanghai today", "status": "passed", "steps": [{"name": "执行命令: What's the weather like in Shanghai today", "time": {"start": 1754397807292, "stop": 1754397827343, "duration": 20051}, "status": "passed", "steps": [{"name": "执行命令: What's the weather like in Shanghai today", "time": {"start": 1754397807292, "stop": 1754397827081, "duration": 19789}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397827081, "stop": 1754397827342, "duration": 261}, "status": "passed", "steps": [], "attachments": [{"uid": "59c627e71b550939", "name": "测试总结", "source": "59c627e71b550939.txt", "type": "text/plain", "size": 274}, {"uid": "6123823f324c4ef8", "name": "test_completed", "source": "6123823f324c4ef8.png", "type": "image/png", "size": 515378}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754397827343, "stop": 1754397827351, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397827351, "stop": 1754397827626, "duration": 275}, "status": "passed", "steps": [], "attachments": [{"uid": "118b0cc23b6ab640", "name": "测试总结", "source": "118b0cc23b6ab640.txt", "type": "text/plain", "size": 274}, {"uid": "a792067ce3cd03f0", "name": "test_completed", "source": "a792067ce3cd03f0.png", "type": "image/png", "size": 515808}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "5a3c56fe3eeeaf30", "name": "stdout", "source": "5a3c56fe3eeeaf30.txt", "type": "text/plain", "size": 12069}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397827629, "stop": 1754397827629, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397827631, "stop": 1754397828871, "duration": 1240}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_what_s_the_weather_like_in_shanghai_today"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherLikeShanghaiToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_what_s_the_weather_like_in_shanghai_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "d0bc974b322cdb0c.json", "parameterValues": []}