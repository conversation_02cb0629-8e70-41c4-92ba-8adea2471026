{"uid": "d0f25b9e4d6dc09d", "name": "测试say hello能正常执行", "fullName": "testcases.test_ella.dialogue.test_say_hello.TestEllaSayHello#test_say_hello", "historyId": "f416bca94fc67372d77ac2dd1f3e4517", "time": {"start": 1754448436849, "stop": 1754448451674, "duration": 14825}, "description": "say hello", "descriptionHtml": "<p>say hello</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448424304, "stop": 1754448436848, "duration": 12544}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448436848, "stop": 1754448436848, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "say hello", "status": "passed", "steps": [{"name": "执行命令: say hello", "time": {"start": 1754448436849, "stop": 1754448451504, "duration": 14655}, "status": "passed", "steps": [{"name": "执行命令: say hello", "time": {"start": 1754448436849, "stop": 1754448451276, "duration": 14427}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448451276, "stop": 1754448451504, "duration": 228}, "status": "passed", "steps": [], "attachments": [{"uid": "f071ef0767e6cef4", "name": "测试总结", "source": "f071ef0767e6cef4.txt", "type": "text/plain", "size": 485}, {"uid": "5fdf5f779bb5e6eb", "name": "test_completed", "source": "5fdf5f779bb5e6eb.png", "type": "image/png", "size": 596272}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448451504, "stop": 1754448451505, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448451505, "stop": 1754448451674, "duration": 169}, "status": "passed", "steps": [], "attachments": [{"uid": "761c536c06847093", "name": "测试总结", "source": "761c536c06847093.txt", "type": "text/plain", "size": 485}, {"uid": "a0b3c5996f4ef583", "name": "test_completed", "source": "a0b3c5996f4ef583.png", "type": "image/png", "size": 595898}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "a321419b2a95a263", "name": "stdout", "source": "a321419b2a95a263.txt", "type": "text/plain", "size": 13146}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448451675, "stop": 1754448451675, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448451676, "stop": 1754448453054, "duration": 1378}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_say_hello"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_say_hello"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "2fdc22d6076ea746", "status": "passed", "time": {"start": 1754398683994, "stop": 1754398697666, "duration": 13672}}], "categories": [], "tags": ["smoke"]}, "source": "d0f25b9e4d6dc09d.json", "parameterValues": []}