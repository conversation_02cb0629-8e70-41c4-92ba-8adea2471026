{"uid": "d24e31670a5f18b1", "name": "测试close phonemaster能正常执行", "fullName": "testcases.test_ella.component_coupling.test_close_phonemaster.TestEllaClosePhonemaster#test_close_phonemaster", "historyId": "bc062eca91b16841cac5c9865921b5c1", "time": {"start": 1754396925746, "stop": 1754396938822, "duration": 13076}, "description": "close phonemaster", "descriptionHtml": "<p>close phonemaster</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754396912269, "stop": 1754396925745, "duration": 13476}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754396925746, "stop": 1754396925746, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "close phonemaster", "status": "passed", "steps": [{"name": "执行命令: close phonemaster", "time": {"start": 1754396925746, "stop": 1754396938491, "duration": 12745}, "status": "passed", "steps": [{"name": "执行命令: close phonemaster", "time": {"start": 1754396925746, "stop": 1754396938162, "duration": 12416}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754396938162, "stop": 1754396938489, "duration": 327}, "status": "passed", "steps": [], "attachments": [{"uid": "f0acaa315cc120d6", "name": "测试总结", "source": "f0acaa315cc120d6.txt", "type": "text/plain", "size": 158}, {"uid": "b7ca5a644321443", "name": "test_completed", "source": "b7ca5a644321443.png", "type": "image/png", "size": 527779}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754396938491, "stop": 1754396938495, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754396938496, "stop": 1754396938821, "duration": 325}, "status": "passed", "steps": [], "attachments": [{"uid": "65addf1d4cb9c6bc", "name": "测试总结", "source": "65addf1d4cb9c6bc.txt", "type": "text/plain", "size": 158}, {"uid": "39731d39a587ff8", "name": "test_completed", "source": "39731d39a587ff8.png", "type": "image/png", "size": 526933}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "6936c2036e91ce1", "name": "stdout", "source": "6936c2036e91ce1.txt", "type": "text/plain", "size": 11080}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754396938823, "stop": 1754396938823, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754396938824, "stop": 1754396940073, "duration": 1249}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_phonemaster"}, {"name": "subSuite", "value": "TestEllaClosePhonemaster"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_phonemaster"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "d24e31670a5f18b1.json", "parameterValues": []}