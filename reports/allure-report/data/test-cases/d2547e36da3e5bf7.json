{"uid": "d2547e36da3e5bf7", "name": "测试where is the carlcare service outlet能正常执行", "fullName": "testcases.test_ella.system_coupling.test_where_is_the_carlcare_service_outlet.TestEllaWhereIsCarlcareServiceOutlet#test_where_is_the_carlcare_service_outlet", "historyId": "4f84a6588e41dde581e4eef4fccd6344", "time": {"start": 1754451268361, "stop": 1754451284343, "duration": 15982}, "description": "where is the carlcare service outlet", "descriptionHtml": "<p>where is the carlcare service outlet</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451255800, "stop": 1754451268359, "duration": 12559}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451268359, "stop": 1754451268359, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "where is the carlcare service outlet", "status": "passed", "steps": [{"name": "执行命令: where is the carlcare service outlet", "time": {"start": 1754451268361, "stop": 1754451284148, "duration": 15787}, "status": "passed", "steps": [{"name": "执行命令: where is the carlcare service outlet", "time": {"start": 1754451268361, "stop": 1754451283941, "duration": 15580}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451283941, "stop": 1754451284148, "duration": 207}, "status": "passed", "steps": [], "attachments": [{"uid": "8b0b5929b28c51eb", "name": "测试总结", "source": "8b0b5929b28c51eb.txt", "type": "text/plain", "size": 265}, {"uid": "7d9341940fa2bd05", "name": "test_completed", "source": "7d9341940fa2bd05.png", "type": "image/png", "size": 462073}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451284148, "stop": 1754451284149, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754451284149, "stop": 1754451284149, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451284149, "stop": 1754451284342, "duration": 193}, "status": "passed", "steps": [], "attachments": [{"uid": "c7ce4b6173a5c791", "name": "测试总结", "source": "c7ce4b6173a5c791.txt", "type": "text/plain", "size": 265}, {"uid": "2621324269b8e508", "name": "test_completed", "source": "2621324269b8e508.png", "type": "image/png", "size": 462079}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "abfa8ae30dcd23ce", "name": "stdout", "source": "abfa8ae30dcd23ce.txt", "type": "text/plain", "size": 14640}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451284343, "stop": 1754451284343, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451284344, "stop": 1754451285713, "duration": 1369}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_where_is_the_carlcare_service_outlet"}, {"name": "subSuite", "value": "TestEllaWhereIsCarlcareServiceOutlet"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_where_is_the_carlcare_service_outlet"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "7eec2b03f82f0c85", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754401423573, "stop": 1754401438589, "duration": 15016}}], "categories": [], "tags": ["smoke"]}, "source": "d2547e36da3e5bf7.json", "parameterValues": []}