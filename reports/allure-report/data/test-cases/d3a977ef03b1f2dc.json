{"uid": "d3a977ef03b1f2dc", "name": "测试disable network enhancement返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_network_enhancement.TestEllaDisableNetworkEnhancement#test_disable_network_enhancement", "historyId": "3d685d9ca6a0d7795be3c96921595318", "time": {"start": 1754452357295, "stop": 1754452371303, "duration": 14008}, "description": "验证disable network enhancement指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable network enhancement指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452343912, "stop": 1754452357295, "duration": 13383}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452357295, "stop": 1754452357295, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证disable network enhancement指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable network enhancement", "time": {"start": 1754452357296, "stop": 1754452371094, "duration": 13798}, "status": "passed", "steps": [{"name": "执行命令: disable network enhancement", "time": {"start": 1754452357296, "stop": 1754452370898, "duration": 13602}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452370898, "stop": 1754452371094, "duration": 196}, "status": "passed", "steps": [], "attachments": [{"uid": "c8038c00f816e1a8", "name": "测试总结", "source": "c8038c00f816e1a8.txt", "type": "text/plain", "size": 248}, {"uid": "f30b6d1a6b124607", "name": "test_completed", "source": "f30b6d1a6b124607.png", "type": "image/png", "size": 503536}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452371094, "stop": 1754452371096, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452371096, "stop": 1754452371302, "duration": 206}, "status": "passed", "steps": [], "attachments": [{"uid": "b17db72a4ea4586c", "name": "测试总结", "source": "b17db72a4ea4586c.txt", "type": "text/plain", "size": 248}, {"uid": "67566aa4057167e0", "name": "test_completed", "source": "67566aa4057167e0.png", "type": "image/png", "size": 504331}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "4e77ce0ebb50f683", "name": "stdout", "source": "4e77ce0ebb50f683.txt", "type": "text/plain", "size": 11401}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452371304, "stop": 1754452371304, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452371305, "stop": 1754452372773, "duration": 1468}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_network_enhancement"}, {"name": "subSuite", "value": "TestEllaDisableNetworkEnhancement"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_network_enhancement"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "3adbb40b5329d22f", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402393253, "stop": 1754402404124, "duration": 10871}}], "categories": [], "tags": ["smoke"]}, "source": "d3a977ef03b1f2dc.json", "parameterValues": []}