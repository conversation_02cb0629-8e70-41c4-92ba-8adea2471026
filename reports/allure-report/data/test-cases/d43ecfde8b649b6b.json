{"uid": "d43ecfde8b649b6b", "name": "测试play afro strut", "fullName": "testcases.test_ella.component_coupling.test_play_afro_strut.TestEllaOpenPlayAfroStrut#test_play_afro_strut", "historyId": "ffb0a39af30beaa699329479ec564117", "time": {"start": 1754397434366, "stop": 1754397456408, "duration": 22042}, "description": "测试play afro strut指令", "descriptionHtml": "<p>测试play afro strut指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397421130, "stop": 1754397434365, "duration": 13235}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397434365, "stop": 1754397434365, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play afro strut指令", "status": "passed", "steps": [{"name": "执行命令: play afro strut", "time": {"start": 1754397434366, "stop": 1754397456146, "duration": 21780}, "status": "passed", "steps": [{"name": "执行命令: play afro strut", "time": {"start": 1754397434366, "stop": 1754397455891, "duration": 21525}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397455891, "stop": 1754397456145, "duration": 254}, "status": "passed", "steps": [], "attachments": [{"uid": "f9b08a5a1a0595ac", "name": "测试总结", "source": "f9b08a5a1a0595ac.txt", "type": "text/plain", "size": 158}, {"uid": "b8ee6fafef69f7d3", "name": "test_completed", "source": "b8ee6fafef69f7d3.png", "type": "image/png", "size": 514976}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754397456146, "stop": 1754397456149, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证play afro strut已打开", "time": {"start": 1754397456149, "stop": 1754397456149, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397456149, "stop": 1754397456407, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "27662de8077e7caa", "name": "测试总结", "source": "27662de8077e7caa.txt", "type": "text/plain", "size": 158}, {"uid": "25d9a8478a78acdd", "name": "test_completed", "source": "25d9a8478a78acdd.png", "type": "image/png", "size": 515371}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "117a9676d31f1dae", "name": "stdout", "source": "117a9676d31f1dae.txt", "type": "text/plain", "size": 13489}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397456410, "stop": 1754397456410, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397456413, "stop": 1754397457660, "duration": 1247}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_afro_strut"}, {"name": "subSuite", "value": "TestEllaOpenPlayAfroStrut"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_afro_strut"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "d43ecfde8b649b6b.json", "parameterValues": []}