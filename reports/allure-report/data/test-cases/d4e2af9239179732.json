{"uid": "d4e2af9239179732", "name": "测试parking space能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_parking_space.TestEllaParkingSpace#test_parking_space", "historyId": "217ee9f7b3be9f625903076716d45106", "time": {"start": 1754453682401, "stop": 1754453695912, "duration": 13511}, "description": "parking space", "descriptionHtml": "<p>parking space</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453670396, "stop": 1754453682399, "duration": 12003}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453682400, "stop": 1754453682400, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "parking space", "status": "passed", "steps": [{"name": "执行命令: parking space", "time": {"start": 1754453682401, "stop": 1754453695727, "duration": 13326}, "status": "passed", "steps": [{"name": "执行命令: parking space", "time": {"start": 1754453682401, "stop": 1754453695538, "duration": 13137}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453695539, "stop": 1754453695727, "duration": 188}, "status": "passed", "steps": [], "attachments": [{"uid": "e091fa151e0e177d", "name": "测试总结", "source": "e091fa151e0e177d.txt", "type": "text/plain", "size": 199}, {"uid": "4d706308d31ea0e8", "name": "test_completed", "source": "4d706308d31ea0e8.png", "type": "image/png", "size": 458746}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754453695727, "stop": 1754453695728, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453695728, "stop": 1754453695912, "duration": 184}, "status": "passed", "steps": [], "attachments": [{"uid": "1c76d68ca50e95b8", "name": "测试总结", "source": "1c76d68ca50e95b8.txt", "type": "text/plain", "size": 199}, {"uid": "e65e2703265822f7", "name": "test_completed", "source": "e65e2703265822f7.png", "type": "image/png", "size": 458381}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "48ac1b282d83f5d9", "name": "stdout", "source": "48ac1b282d83f5d9.txt", "type": "text/plain", "size": 11232}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453695913, "stop": 1754453695913, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453695914, "stop": 1754453697303, "duration": 1389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "ella技能"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_parking_space"}, {"name": "subSuite", "value": "TestEllaParkingSpace"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_parking_space"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "ec27c6ea931f67eb", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Do you need to open parking space or add a new one?']\nassert False", "time": {"start": 1754403699092, "stop": 1754403709862, "duration": 10770}}], "categories": [], "tags": ["smoke"]}, "source": "d4e2af9239179732.json", "parameterValues": []}