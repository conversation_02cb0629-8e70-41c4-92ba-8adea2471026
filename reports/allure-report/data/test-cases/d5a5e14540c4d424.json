{"uid": "d5a5e14540c4d424", "name": "测试resume music能正常执行", "fullName": "testcases.test_ella.component_coupling.test_resume_music.TestEllaResumeMusic#test_resume_music", "historyId": "44b646d68146a0c48da2623a58b17f6f", "time": {"start": 1754447374047, "stop": 1754447387656, "duration": 13609}, "description": "resume music", "descriptionHtml": "<p>resume music</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447361409, "stop": 1754447374046, "duration": 12637}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447374046, "stop": 1754447374046, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "resume music", "status": "passed", "steps": [{"name": "执行命令: resume music", "time": {"start": 1754447374047, "stop": 1754447387433, "duration": 13386}, "status": "passed", "steps": [{"name": "执行命令: resume music", "time": {"start": 1754447374047, "stop": 1754447387228, "duration": 13181}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447387228, "stop": 1754447387433, "duration": 205}, "status": "passed", "steps": [], "attachments": [{"uid": "29e36671149767fa", "name": "测试总结", "source": "29e36671149767fa.txt", "type": "text/plain", "size": 181}, {"uid": "fdd07fa61f7b779b", "name": "test_completed", "source": "fdd07fa61f7b779b.png", "type": "image/png", "size": 557553}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447387433, "stop": 1754447387435, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447387435, "stop": 1754447387655, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "c9aa2fd7e9efbf67", "name": "测试总结", "source": "c9aa2fd7e9efbf67.txt", "type": "text/plain", "size": 181}, {"uid": "28f9de9b1073f90a", "name": "test_completed", "source": "28f9de9b1073f90a.png", "type": "image/png", "size": 557063}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "2cf7016af5091021", "name": "stdout", "source": "2cf7016af5091021.txt", "type": "text/plain", "size": 11422}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447387656, "stop": 1754447387656, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447387658, "stop": 1754447389096, "duration": 1438}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_resume_music"}, {"name": "subSuite", "value": "TestEllaResumeMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_resume_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "746d5df8d9bd2b3b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['No music playing in the background']\nassert False", "time": {"start": 1754397664595, "stop": 1754397677178, "duration": 12583}}], "categories": [], "tags": ["smoke"]}, "source": "d5a5e14540c4d424.json", "parameterValues": []}