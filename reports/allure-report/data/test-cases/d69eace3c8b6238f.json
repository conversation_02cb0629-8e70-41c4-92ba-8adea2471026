{"uid": "d69eace3c8b6238f", "name": "测试find a restaurant near me能正常执行", "fullName": "testcases.test_ella.third_coupling.test_find_a_restaurant_near_me.TestEllaFindRestaurantNearMe#test_find_a_restaurant_near_me", "historyId": "918c52f1eb9803594ff76c724b43d5f8", "time": {"start": 1754401538293, "stop": 1754401562244, "duration": 23951}, "description": "find a restaurant near me", "descriptionHtml": "<p>find a restaurant near me</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754401525293, "stop": 1754401538292, "duration": 12999}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754401538292, "stop": 1754401538292, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "find a restaurant near me", "status": "passed", "steps": [{"name": "执行命令: find a restaurant near me", "time": {"start": 1754401538293, "stop": 1754401561947, "duration": 23654}, "status": "passed", "steps": [{"name": "执行命令: find a restaurant near me", "time": {"start": 1754401538293, "stop": 1754401561645, "duration": 23352}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754401561645, "stop": 1754401561946, "duration": 301}, "status": "passed", "steps": [], "attachments": [{"uid": "db9213a2640d2dbc", "name": "测试总结", "source": "db9213a2640d2dbc.txt", "type": "text/plain", "size": 607}, {"uid": "c7a29cedfb22fbf8", "name": "test_completed", "source": "c7a29cedfb22fbf8.png", "type": "image/png", "size": 503246}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证已打开", "time": {"start": 1754401561947, "stop": 1754401561947, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754401561947, "stop": 1754401562243, "duration": 296}, "status": "passed", "steps": [], "attachments": [{"uid": "ef8cfcbcd5924de4", "name": "测试总结", "source": "ef8cfcbcd5924de4.txt", "type": "text/plain", "size": 607}, {"uid": "15c1a3d95487b7e8", "name": "test_completed", "source": "15c1a3d95487b7e8.png", "type": "image/png", "size": 503246}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "686cbaa7e18f42c5", "name": "stdout", "source": "686cbaa7e18f42c5.txt", "type": "text/plain", "size": 16921}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754401562245, "stop": 1754401562245, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754401562247, "stop": 1754401563520, "duration": 1273}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_find_a_restaurant_near_me"}, {"name": "subSuite", "value": "TestEllaFindRestaurantNearMe"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_find_a_restaurant_near_me"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "d69eace3c8b6238f.json", "parameterValues": []}