{"uid": "d7d0df09dd27c956", "name": "测试introduce yourself能正常执行", "fullName": "testcases.test_ella.dialogue.test_introduce_yourself.TestEllaIntroduceYourself#test_introduce_yourself", "historyId": "a19924fb0a564cf26596907610c0f678", "time": {"start": 1754448267716, "stop": 1754448282495, "duration": 14779}, "description": "introduce yourself", "descriptionHtml": "<p>introduce yourself</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448255022, "stop": 1754448267714, "duration": 12692}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448267714, "stop": 1754448267714, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "introduce yourself", "status": "passed", "steps": [{"name": "执行命令: introduce yourself", "time": {"start": 1754448267716, "stop": 1754448282318, "duration": 14602}, "status": "passed", "steps": [{"name": "执行命令: introduce yourself", "time": {"start": 1754448267716, "stop": 1754448282117, "duration": 14401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448282117, "stop": 1754448282317, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "405c9a8129b3b61b", "name": "测试总结", "source": "405c9a8129b3b61b.txt", "type": "text/plain", "size": 626}, {"uid": "be6226dcda8a84b1", "name": "test_completed", "source": "be6226dcda8a84b1.png", "type": "image/png", "size": 675284}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448282318, "stop": 1754448282319, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448282319, "stop": 1754448282495, "duration": 176}, "status": "passed", "steps": [], "attachments": [{"uid": "48eebff501aa7a62", "name": "测试总结", "source": "48eebff501aa7a62.txt", "type": "text/plain", "size": 626}, {"uid": "5d39056172af2ae3", "name": "test_completed", "source": "5d39056172af2ae3.png", "type": "image/png", "size": 674778}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "68eed3e78c82a7a6", "name": "stdout", "source": "68eed3e78c82a7a6.txt", "type": "text/plain", "size": 13880}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448282496, "stop": 1754448282496, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448282497, "stop": 1754448283913, "duration": 1416}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_introduce_yourself"}, {"name": "subSuite", "value": "TestEllaIntroduceYourself"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_introduce_yourself"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "597023f7a3056d7e", "status": "passed", "time": {"start": 1754398533070, "stop": 1754398546898, "duration": 13828}}], "categories": [], "tags": ["smoke"]}, "source": "d7d0df09dd27c956.json", "parameterValues": []}