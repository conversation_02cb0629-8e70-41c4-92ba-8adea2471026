{"uid": "d7da5dcb44eb19de", "name": "测试enable all ai magic box features返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_all_ai_magic_box_features.TestEllaEnableAllAiMagicBoxFeatures#test_enable_all_ai_magic_box_features", "historyId": "********************************", "time": {"start": 1754452585080, "stop": 1754452598459, "duration": 13379}, "description": "验证enable all ai magic box features指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable all ai magic box features指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452572300, "stop": 1754452585079, "duration": 12779}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452585080, "stop": 1754452585080, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证enable all ai magic box features指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable all ai magic box features", "time": {"start": 1754452585080, "stop": 1754452598273, "duration": 13193}, "status": "passed", "steps": [{"name": "执行命令: enable all ai magic box features", "time": {"start": 1754452585080, "stop": 1754452598093, "duration": 13013}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452598093, "stop": 1754452598273, "duration": 180}, "status": "passed", "steps": [], "attachments": [{"uid": "cb534e2a4ae7ab4f", "name": "测试总结", "source": "cb534e2a4ae7ab4f.txt", "type": "text/plain", "size": 250}, {"uid": "b2c17df71b0a6ed2", "name": "test_completed", "source": "b2c17df71b0a6ed2.png", "type": "image/png", "size": 503357}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452598273, "stop": 1754452598274, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452598274, "stop": 1754452598458, "duration": 184}, "status": "passed", "steps": [], "attachments": [{"uid": "51466bf0db33ddb0", "name": "测试总结", "source": "51466bf0db33ddb0.txt", "type": "text/plain", "size": 250}, {"uid": "bc48624470fbbb43", "name": "test_completed", "source": "bc48624470fbbb43.png", "type": "image/png", "size": 503856}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "72c46e5edacfdadc", "name": "stdout", "source": "72c46e5edacfdadc.txt", "type": "text/plain", "size": 11426}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452598459, "stop": 1754452598459, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452598460, "stop": 1754452599827, "duration": 1367}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_all_ai_magic_box_features"}, {"name": "subSuite", "value": "TestEllaEnableAllAiMagicBoxFeatures"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_all_ai_magic_box_features"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "11fcdf27778f568e", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402608966, "stop": 1754402620044, "duration": 11078}}], "categories": [], "tags": ["smoke"]}, "source": "d7da5dcb44eb19de.json", "parameterValues": []}