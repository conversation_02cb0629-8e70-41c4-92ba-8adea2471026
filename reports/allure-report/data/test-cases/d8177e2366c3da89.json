{"uid": "d8177e2366c3da89", "name": "测试delete the 8 o'clock alarm", "fullName": "testcases.test_ella.component_coupling.test_delete_the_8_o_clock_alarm.TestEllaOpenClock#test_delete_the_8_o_clock_alarm", "historyId": "8c62567d8b8a27f77124afc90fa44336", "time": {"start": 1754446698704, "stop": 1754446716817, "duration": 18113}, "description": "测试delete the 8 o'clock alarm指令", "descriptionHtml": "<p>测试delete the 8 o'clock alarm指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754446686500, "stop": 1754446698702, "duration": 12202}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754446698703, "stop": 1754446698703, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试delete the 8 o'clock alarm指令", "status": "passed", "steps": [{"name": "执行命令: delete the 8 o'clock alarm", "time": {"start": 1754446698704, "stop": 1754446716623, "duration": 17919}, "status": "passed", "steps": [{"name": "执行命令: delete the 8 o'clock alarm", "time": {"start": 1754446698704, "stop": 1754446716407, "duration": 17703}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446716407, "stop": 1754446716622, "duration": 215}, "status": "passed", "steps": [], "attachments": [{"uid": "a2947e53dff47202", "name": "测试总结", "source": "a2947e53dff47202.txt", "type": "text/plain", "size": 214}, {"uid": "da5ed80d6b0c4a1b", "name": "test_completed", "source": "da5ed80d6b0c4a1b.png", "type": "image/png", "size": 557053}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754446716623, "stop": 1754446716624, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446716624, "stop": 1754446716817, "duration": 193}, "status": "passed", "steps": [], "attachments": [{"uid": "d3a7162f738d7fc1", "name": "测试总结", "source": "d3a7162f738d7fc1.txt", "type": "text/plain", "size": 214}, {"uid": "54a4cf4d4ed18527", "name": "test_completed", "source": "54a4cf4d4ed18527.png", "type": "image/png", "size": 557053}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "4bd2d6759b85d8de", "name": "stdout", "source": "4bd2d6759b85d8de.txt", "type": "text/plain", "size": 11745}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754446716818, "stop": 1754446716818, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754446716821, "stop": 1754446718176, "duration": 1355}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_delete_the_8_o_clock_alarm"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_delete_the_8_o_clock_alarm"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "a6004a58bef8d00f", "status": "passed", "time": {"start": 1754397009121, "stop": 1754397026564, "duration": 17443}}], "categories": [], "tags": ["smoke"]}, "source": "d8177e2366c3da89.json", "parameterValues": []}