{"uid": "d8d80c1a8a7aa2ba", "name": "测试can you give me a coin能正常执行", "fullName": "testcases.test_ella.dialogue.test_can_you_give_me_a_coin.TestEllaCanYouGiveMeCoin#test_can_you_give_me_a_coin", "historyId": "78164cec6ab8359be9416229a4882ef9", "time": {"start": 1754447660529, "stop": 1754447677746, "duration": 17217}, "description": "can you give me a coin", "descriptionHtml": "<p>can you give me a coin</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447647068, "stop": 1754447660528, "duration": 13460}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447660528, "stop": 1754447660528, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "can you give me a coin", "status": "passed", "steps": [{"name": "执行命令: can you give me a coin", "time": {"start": 1754447660530, "stop": 1754447677269, "duration": 16739}, "status": "passed", "steps": [{"name": "执行命令: can you give me a coin", "time": {"start": 1754447660530, "stop": 1754447676873, "duration": 16343}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447676873, "stop": 1754447677269, "duration": 396}, "status": "passed", "steps": [], "attachments": [{"uid": "a5fb24567c343707", "name": "测试总结", "source": "a5fb24567c343707.txt", "type": "text/plain", "size": 193}, {"uid": "f54ad28b0f12d915", "name": "test_completed", "source": "f54ad28b0f12d915.png", "type": "image/png", "size": 625423}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447677269, "stop": 1754447677275, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447677275, "stop": 1754447677745, "duration": 470}, "status": "passed", "steps": [], "attachments": [{"uid": "c913ee230147a813", "name": "测试总结", "source": "c913ee230147a813.txt", "type": "text/plain", "size": 193}, {"uid": "e872f5576747ef22", "name": "test_completed", "source": "e872f5576747ef22.png", "type": "image/png", "size": 625447}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "d2d5475692f34f3a", "name": "stdout", "source": "d2d5475692f34f3a.txt", "type": "text/plain", "size": 11489}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447677747, "stop": 1754447677747, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447677750, "stop": 1754447679140, "duration": 1390}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_can_you_give_me_a_coin"}, {"name": "subSuite", "value": "TestEllaCanYouGiveMeCoin"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_can_you_give_me_a_coin"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "a375fd272737defc", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"All right, here's the money\"]\nassert False", "time": {"start": 1754397934145, "stop": 1754397947527, "duration": 13382}}], "categories": [], "tags": ["smoke"]}, "source": "d8d80c1a8a7aa2ba.json", "parameterValues": []}