{"uid": "d9283c555a0dfffd", "name": "测试close phonemaster能正常执行", "fullName": "testcases.test_ella.component_coupling.test_close_phonemaster.TestEllaClosePhonemaster#test_close_phonemaster", "historyId": "bc062eca91b16841cac5c9865921b5c1", "time": {"start": 1754446615376, "stop": 1754446629166, "duration": 13790}, "description": "close phonemaster", "descriptionHtml": "<p>close phonemaster</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754446602908, "stop": 1754446615374, "duration": 12466}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754446615375, "stop": 1754446615375, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "close phonemaster", "status": "passed", "steps": [{"name": "执行命令: close phonemaster", "time": {"start": 1754446615376, "stop": 1754446628969, "duration": 13593}, "status": "passed", "steps": [{"name": "执行命令: close phonemaster", "time": {"start": 1754446615376, "stop": 1754446628749, "duration": 13373}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446628749, "stop": 1754446628969, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "9b477ce2acfb107b", "name": "测试总结", "source": "9b477ce2acfb107b.txt", "type": "text/plain", "size": 158}, {"uid": "aba39a42bf1ec77b", "name": "test_completed", "source": "aba39a42bf1ec77b.png", "type": "image/png", "size": 553675}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754446628969, "stop": 1754446628970, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754446628970, "stop": 1754446629165, "duration": 195}, "status": "passed", "steps": [], "attachments": [{"uid": "f83df098ef334f0", "name": "测试总结", "source": "f83df098ef334f0.txt", "type": "text/plain", "size": 158}, {"uid": "ad58a50b7e459087", "name": "test_completed", "source": "ad58a50b7e459087.png", "type": "image/png", "size": 553372}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "326995c1270e9a85", "name": "stdout", "source": "326995c1270e9a85.txt", "type": "text/plain", "size": 11080}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754446629166, "stop": 1754446629166, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754446629167, "stop": 1754446630486, "duration": 1319}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_phonemaster"}, {"name": "subSuite", "value": "TestEllaClosePhonemaster"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_phonemaster"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "d24e31670a5f18b1", "status": "passed", "time": {"start": 1754396925746, "stop": 1754396938822, "duration": 13076}}], "categories": [], "tags": ["smoke"]}, "source": "d9283c555a0dfffd.json", "parameterValues": []}