{"uid": "d930c37cd1055316", "name": "测试turn on driving mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_turn_on_driving_mode.TestEllaTurnDrivingMode#test_turn_on_driving_mode", "historyId": "d8a3659601151a79f3b71ba4e47cafee", "time": {"start": 1754455434254, "stop": 1754455448097, "duration": 13843}, "description": "验证turn on driving mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证turn on driving mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455422160, "stop": 1754455434253, "duration": 12093}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455434254, "stop": 1754455434254, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证turn on driving mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: turn on driving mode", "time": {"start": 1754455434254, "stop": 1754455447907, "duration": 13653}, "status": "passed", "steps": [{"name": "执行命令: turn on driving mode", "time": {"start": 1754455434254, "stop": 1754455447685, "duration": 13431}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455447685, "stop": 1754455447907, "duration": 222}, "status": "passed", "steps": [], "attachments": [{"uid": "1493f14e6c9e50fa", "name": "测试总结", "source": "1493f14e6c9e50fa.txt", "type": "text/plain", "size": 233}, {"uid": "bbf774f4c53b3044", "name": "test_completed", "source": "bbf774f4c53b3044.png", "type": "image/png", "size": 480957}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455447907, "stop": 1754455447909, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455447909, "stop": 1754455448097, "duration": 188}, "status": "passed", "steps": [], "attachments": [{"uid": "41043b7e5f5498dc", "name": "测试总结", "source": "41043b7e5f5498dc.txt", "type": "text/plain", "size": 233}, {"uid": "b64a9aef877bd450", "name": "test_completed", "source": "b64a9aef877bd450.png", "type": "image/png", "size": 481193}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "e23de669640a005", "name": "stdout", "source": "e23de669640a005.txt", "type": "text/plain", "size": 11315}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754455448098, "stop": 1754455449523, "duration": 1425}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754455448098, "stop": 1754455448098, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_on_driving_mode"}, {"name": "subSuite", "value": "TestEllaTurnDrivingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_on_driving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "3b10cf576dc64f7b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405474408, "stop": 1754405490084, "duration": 15676}}], "categories": [], "tags": ["smoke"]}, "source": "d930c37cd1055316.json", "parameterValues": []}