{"uid": "d97ce04890c6efba", "name": "测试switch to equilibrium mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_switch_to_equilibrium_mode.TestEllaSwitchEquilibriumMode#test_switch_to_equilibrium_mode", "historyId": "8b6d374ba70006ef7591c8e0bf72bb00", "time": {"start": 1754455241895, "stop": 1754455255566, "duration": 13671}, "description": "验证switch to equilibrium mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证switch to equilibrium mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455230193, "stop": 1754455241894, "duration": 11701}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455241894, "stop": 1754455241894, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证switch to equilibrium mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: switch to equilibrium mode", "time": {"start": 1754455241896, "stop": 1754455255372, "duration": 13476}, "status": "passed", "steps": [{"name": "执行命令: switch to equilibrium mode", "time": {"start": 1754455241896, "stop": 1754455255179, "duration": 13283}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455255179, "stop": 1754455255371, "duration": 192}, "status": "passed", "steps": [], "attachments": [{"uid": "c61edab8703b38dd", "name": "测试总结", "source": "c61edab8703b38dd.txt", "type": "text/plain", "size": 245}, {"uid": "4aef6a639a4c9703", "name": "test_completed", "source": "4aef6a639a4c9703.png", "type": "image/png", "size": 514537}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455255372, "stop": 1754455255373, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455255373, "stop": 1754455255565, "duration": 192}, "status": "passed", "steps": [], "attachments": [{"uid": "6996e7ac12d97448", "name": "测试总结", "source": "6996e7ac12d97448.txt", "type": "text/plain", "size": 245}, {"uid": "111d8eb45a6b50f2", "name": "test_completed", "source": "111d8eb45a6b50f2.png", "type": "image/png", "size": 514093}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "814011322f82c7b0", "name": "stdout", "source": "814011322f82c7b0.txt", "type": "text/plain", "size": 11381}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455255566, "stop": 1754455255566, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455255567, "stop": 1754455256880, "duration": 1313}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switch_to_equilibrium_mode"}, {"name": "subSuite", "value": "TestEllaSwitchEquilibriumMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switch_to_equilibrium_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "cb6143fe08ac5795", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405281915, "stop": 1754405297809, "duration": 15894}}], "categories": [], "tags": ["smoke"]}, "source": "d97ce04890c6efba.json", "parameterValues": []}