{"uid": "d9d41f3b82b40b56", "name": "测试What languages do you support能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_languages_do_you_support.TestEllaWhatLanguagesDoYouSupport#test_what_languages_do_you_support", "historyId": "0c44c94f08feed70addcec44e96bda5a", "time": {"start": 1754448947539, "stop": 1754448961326, "duration": 13787}, "description": "What languages do you support", "descriptionHtml": "<p>What languages do you support</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448934787, "stop": 1754448947538, "duration": 12751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448947538, "stop": 1754448947538, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "What languages do you support", "status": "passed", "steps": [{"name": "执行命令: What languages do you support", "time": {"start": 1754448947539, "stop": 1754448961123, "duration": 13584}, "status": "passed", "steps": [{"name": "执行命令: What languages do you support", "time": {"start": 1754448947539, "stop": 1754448960928, "duration": 13389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448960928, "stop": 1754448961123, "duration": 195}, "status": "passed", "steps": [], "attachments": [{"uid": "82e972cc04f7a152", "name": "测试总结", "source": "82e972cc04f7a152.txt", "type": "text/plain", "size": 322}, {"uid": "dba58ce69eb6f8cc", "name": "test_completed", "source": "dba58ce69eb6f8cc.png", "type": "image/png", "size": 627007}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448961123, "stop": 1754448961124, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448961124, "stop": 1754448961325, "duration": 201}, "status": "passed", "steps": [], "attachments": [{"uid": "faeeccf8863ab695", "name": "测试总结", "source": "faeeccf8863ab695.txt", "type": "text/plain", "size": 322}, {"uid": "8312c1dd746fefd2", "name": "test_completed", "source": "8312c1dd746fefd2.png", "type": "image/png", "size": 627352}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "371decfb0ab8209e", "name": "stdout", "source": "371decfb0ab8209e.txt", "type": "text/plain", "size": 11766}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448961326, "stop": 1754448961326, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448961327, "stop": 1754448962730, "duration": 1403}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_languages_do_you_support"}, {"name": "subSuite", "value": "TestEllaWhatLanguagesDoYouSupport"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_languages_do_you_support"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "2bebe751895151ae", "status": "passed", "time": {"start": 1754399141966, "stop": 1754399155267, "duration": 13301}}], "categories": [], "tags": ["smoke"]}, "source": "d9d41f3b82b40b56.json", "parameterValues": []}