{"uid": "dac25dfd4e1508f2", "name": "测试pause fm能正常执行", "fullName": "testcases.test_ella.component_coupling.test_pause_fm.TestEllaPauseFm#test_pause_fm", "historyId": "861aa58f9a3d0d9c9861d88316e784c5", "time": {"start": 1754397377552, "stop": 1754397391767, "duration": 14215}, "description": "pause fm", "descriptionHtml": "<p>pause fm</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397364427, "stop": 1754397377551, "duration": 13124}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397377551, "stop": 1754397377552, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "pause fm", "status": "passed", "steps": [{"name": "执行命令: pause fm", "time": {"start": 1754397377552, "stop": 1754397391457, "duration": 13905}, "status": "passed", "steps": [{"name": "执行命令: pause fm", "time": {"start": 1754397377552, "stop": 1754397391185, "duration": 13633}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397391185, "stop": 1754397391456, "duration": 271}, "status": "passed", "steps": [], "attachments": [{"uid": "5bae6ed845d8678a", "name": "测试总结", "source": "5bae6ed845d8678a.txt", "type": "text/plain", "size": 196}, {"uid": "6ca0d0dff9f619ba", "name": "test_completed", "source": "6ca0d0dff9f619ba.png", "type": "image/png", "size": 622283}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754397391457, "stop": 1754397391461, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397391461, "stop": 1754397391766, "duration": 305}, "status": "passed", "steps": [], "attachments": [{"uid": "edcaf57f3a803099", "name": "测试总结", "source": "edcaf57f3a803099.txt", "type": "text/plain", "size": 196}, {"uid": "c511048021043a7d", "name": "test_completed", "source": "c511048021043a7d.png", "type": "image/png", "size": 621663}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "8b32e6c6fdd21f32", "name": "stdout", "source": "8b32e6c6fdd21f32.txt", "type": "text/plain", "size": 11205}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397391767, "stop": 1754397391767, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397391769, "stop": 1754397393102, "duration": 1333}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_pause_fm"}, {"name": "subSuite", "value": "TestEllaPauseFm"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_pause_fm"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "dac25dfd4e1508f2.json", "parameterValues": []}