{"uid": "dadc4cc89b23040e", "name": "测试play video", "fullName": "testcases.test_ella.unsupported_commands.test_play_video.TestEllaOpenPlayPoliticalNews#test_play_political_news", "historyId": "d13fb0644c88e807e0b804b1986ee770", "time": {"start": 1754453847145, "stop": 1754453862367, "duration": 15222}, "description": "测试play video指令", "descriptionHtml": "<p>测试play video指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453834673, "stop": 1754453847143, "duration": 12470}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453847144, "stop": 1754453847144, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play video指令", "status": "passed", "steps": [{"name": "执行命令: play video", "time": {"start": 1754453847145, "stop": 1754453862177, "duration": 15032}, "status": "passed", "steps": [{"name": "执行命令: play video", "time": {"start": 1754453847145, "stop": 1754453861979, "duration": 14834}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453861979, "stop": 1754453862177, "duration": 198}, "status": "passed", "steps": [], "attachments": [{"uid": "8e53fe45a0e16a3f", "name": "测试总结", "source": "8e53fe45a0e16a3f.txt", "type": "text/plain", "size": 199}, {"uid": "b0f4f6a7b8591b4d", "name": "test_completed", "source": "b0f4f6a7b8591b4d.png", "type": "image/png", "size": 406339}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754453862177, "stop": 1754453862178, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证youtube已打开", "time": {"start": 1754453862178, "stop": 1754453862178, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453862178, "stop": 1754453862367, "duration": 189}, "status": "passed", "steps": [], "attachments": [{"uid": "a3739f37ad60ed0b", "name": "测试总结", "source": "a3739f37ad60ed0b.txt", "type": "text/plain", "size": 199}, {"uid": "55c07844415173a9", "name": "test_completed", "source": "55c07844415173a9.png", "type": "image/png", "size": 406353}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "dcccf3fe23751b30", "name": "stdout", "source": "dcccf3fe23751b30.txt", "type": "text/plain", "size": 14472}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453862369, "stop": 1754453862369, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453862370, "stop": 1754453863789, "duration": 1419}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_video"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_video"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "ec3a7ca0e904fb11", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754403861515, "stop": 1754403876356, "duration": 14841}}], "categories": [], "tags": ["smoke"]}, "source": "dadc4cc89b23040e.json", "parameterValues": []}