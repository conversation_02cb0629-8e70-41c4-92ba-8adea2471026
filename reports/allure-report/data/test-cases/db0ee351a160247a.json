{"uid": "db0ee351a160247a", "name": "测试pause fm能正常执行", "fullName": "testcases.test_ella.component_coupling.test_pause_fm.TestEllaPauseFm#test_pause_fm", "historyId": "861aa58f9a3d0d9c9861d88316e784c5", "time": {"start": 1754447070116, "stop": 1754447083848, "duration": 13732}, "description": "pause fm", "descriptionHtml": "<p>pause fm</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447057545, "stop": 1754447070115, "duration": 12570}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447070115, "stop": 1754447070115, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "pause fm", "status": "passed", "steps": [{"name": "执行命令: pause fm", "time": {"start": 1754447070117, "stop": 1754447083636, "duration": 13519}, "status": "passed", "steps": [{"name": "执行命令: pause fm", "time": {"start": 1754447070117, "stop": 1754447083453, "duration": 13336}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447083453, "stop": 1754447083636, "duration": 183}, "status": "passed", "steps": [], "attachments": [{"uid": "1957262bc0fcebe3", "name": "测试总结", "source": "1957262bc0fcebe3.txt", "type": "text/plain", "size": 196}, {"uid": "e33984b340cae5ef", "name": "test_completed", "source": "e33984b340cae5ef.png", "type": "image/png", "size": 603024}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447083636, "stop": 1754447083637, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447083637, "stop": 1754447083847, "duration": 210}, "status": "passed", "steps": [], "attachments": [{"uid": "1bee2db51123844b", "name": "测试总结", "source": "1bee2db51123844b.txt", "type": "text/plain", "size": 196}, {"uid": "5fd6821d4956d3e8", "name": "test_completed", "source": "5fd6821d4956d3e8.png", "type": "image/png", "size": 602500}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "728a83b74d1ba4d1", "name": "stdout", "source": "728a83b74d1ba4d1.txt", "type": "text/plain", "size": 11205}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447083848, "stop": 1754447083848, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447083850, "stop": 1754447085202, "duration": 1352}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_pause_fm"}, {"name": "subSuite", "value": "TestEllaPauseFm"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_pause_fm"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "dac25dfd4e1508f2", "status": "passed", "time": {"start": 1754397377552, "stop": 1754397391767, "duration": 14215}}], "categories": [], "tags": ["smoke"]}, "source": "db0ee351a160247a.json", "parameterValues": []}