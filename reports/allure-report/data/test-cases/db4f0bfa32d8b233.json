{"uid": "db4f0bfa32d8b233", "name": "测试set folding screen zone返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_folding_screen_zone.TestEllaSetFoldingScreenZone#test_set_folding_screen_zone", "historyId": "c04d9357fdaf44e6ee27f8a97ece6c5d", "time": {"start": 1754454551661, "stop": 1754454565552, "duration": 13891}, "description": "验证set folding screen zone指令返回预期的不支持响应", "descriptionHtml": "<p>验证set folding screen zone指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454538948, "stop": 1754454551661, "duration": 12713}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454551661, "stop": 1754454551661, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set folding screen zone指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set folding screen zone", "time": {"start": 1754454551661, "stop": 1754454565345, "duration": 13684}, "status": "passed", "steps": [{"name": "执行命令: set folding screen zone", "time": {"start": 1754454551661, "stop": 1754454565168, "duration": 13507}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454565168, "stop": 1754454565344, "duration": 176}, "status": "passed", "steps": [], "attachments": [{"uid": "d810b8bc749b6682", "name": "测试总结", "source": "d810b8bc749b6682.txt", "type": "text/plain", "size": 239}, {"uid": "6d2e366905ad0a1a", "name": "test_completed", "source": "6d2e366905ad0a1a.png", "type": "image/png", "size": 505089}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454565345, "stop": 1754454565346, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454565346, "stop": 1754454565552, "duration": 206}, "status": "passed", "steps": [], "attachments": [{"uid": "67cd931c45a2ade3", "name": "测试总结", "source": "67cd931c45a2ade3.txt", "type": "text/plain", "size": 239}, {"uid": "233c8aabec27e6f", "name": "test_completed", "source": "233c8aabec27e6f.png", "type": "image/png", "size": 505476}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "95b0648141570b07", "name": "stdout", "source": "95b0648141570b07.txt", "type": "text/plain", "size": 11352}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454565552, "stop": 1754454565552, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454565555, "stop": 1754454566954, "duration": 1399}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_folding_screen_zone"}, {"name": "subSuite", "value": "TestEllaSetFoldingScreenZone"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_folding_screen_zone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "45cdb921cad6925b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404553433, "stop": 1754404564515, "duration": 11082}}], "categories": [], "tags": ["smoke"]}, "source": "db4f0bfa32d8b233.json", "parameterValues": []}