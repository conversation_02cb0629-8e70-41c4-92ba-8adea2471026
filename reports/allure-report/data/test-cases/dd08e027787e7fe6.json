{"uid": "dd08e027787e7fe6", "name": "测试turn on show battery percentage返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_turn_on_show_battery_percentage.TestEllaTurnShowBatteryPercentage#test_turn_on_show_battery_percentage", "historyId": "e82a80866bdbe9a7e1ac367f20c977b5", "time": {"start": 1754455489902, "stop": 1754455504894, "duration": 14992}, "description": "验证turn on show battery percentage指令返回预期的不支持响应", "descriptionHtml": "<p>验证turn on show battery percentage指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455477492, "stop": 1754455489901, "duration": 12409}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455489901, "stop": 1754455489901, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证turn on show battery percentage指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: turn on show battery percentage", "time": {"start": 1754455489902, "stop": 1754455504717, "duration": 14815}, "status": "passed", "steps": [{"name": "执行命令: turn on show battery percentage", "time": {"start": 1754455489902, "stop": 1754455504528, "duration": 14626}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455504528, "stop": 1754455504717, "duration": 189}, "status": "passed", "steps": [], "attachments": [{"uid": "fe3a5e45f4b57f61", "name": "测试总结", "source": "fe3a5e45f4b57f61.txt", "type": "text/plain", "size": 254}, {"uid": "ffcfc4e39d50bfa", "name": "test_completed", "source": "ffcfc4e39d50bfa.png", "type": "image/png", "size": 506345}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455504717, "stop": 1754455504718, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455504718, "stop": 1754455504894, "duration": 176}, "status": "passed", "steps": [], "attachments": [{"uid": "8e1f2cb641077807", "name": "测试总结", "source": "8e1f2cb641077807.txt", "type": "text/plain", "size": 254}, {"uid": "43424dcddc42029c", "name": "test_completed", "source": "43424dcddc42029c.png", "type": "image/png", "size": 506394}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "44eae19ef3fb1f08", "name": "stdout", "source": "44eae19ef3fb1f08.txt", "type": "text/plain", "size": 11961}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455504894, "stop": 1754455504894, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455504897, "stop": 1754455506235, "duration": 1338}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_on_show_battery_percentage"}, {"name": "subSuite", "value": "TestEllaTurnShowBatteryPercentage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_on_show_battery_percentage"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "ed8356a04fc7c716", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405535637, "stop": 1754405547072, "duration": 11435}}], "categories": [], "tags": ["smoke"]}, "source": "dd08e027787e7fe6.json", "parameterValues": []}