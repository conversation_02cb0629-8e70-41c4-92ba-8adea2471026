{"uid": "ddc18dfac4b48d27", "name": "测试download qq能正常执行", "fullName": "testcases.test_ella.third_coupling.test_download_qq.TestEllaDownloadQq#test_download_qq", "historyId": "b89775573784e6ef95769309baebeae4", "time": {"start": 1754451355833, "stop": 1754451372839, "duration": 17006}, "description": "download qq", "descriptionHtml": "<p>download qq</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451343231, "stop": 1754451355832, "duration": 12601}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451355832, "stop": 1754451355832, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "download qq", "status": "passed", "steps": [{"name": "执行命令: download qq", "time": {"start": 1754451355833, "stop": 1754451372670, "duration": 16837}, "status": "passed", "steps": [{"name": "执行命令: download qq", "time": {"start": 1754451355833, "stop": 1754451372475, "duration": 16642}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451372475, "stop": 1754451372670, "duration": 195}, "status": "passed", "steps": [], "attachments": [{"uid": "38d05ec4ea18168c", "name": "测试总结", "source": "38d05ec4ea18168c.txt", "type": "text/plain", "size": 298}, {"uid": "7aafc7bc8172b20b", "name": "test_completed", "source": "7aafc7bc8172b20b.png", "type": "image/png", "size": 441811}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451372670, "stop": 1754451372671, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754451372671, "stop": 1754451372671, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451372671, "stop": 1754451372839, "duration": 168}, "status": "passed", "steps": [], "attachments": [{"uid": "1f4df1d79f64dbce", "name": "测试总结", "source": "1f4df1d79f64dbce.txt", "type": "text/plain", "size": 298}, {"uid": "e51478461be4deac", "name": "test_completed", "source": "e51478461be4deac.png", "type": "image/png", "size": 442136}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "a68360fd5484b16", "name": "stdout", "source": "a68360fd5484b16.txt", "type": "text/plain", "size": 14498}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451372840, "stop": 1754451372840, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451372841, "stop": 1754451374223, "duration": 1382}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_download_qq"}, {"name": "subSuite", "value": "TestEllaDownloadQq"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_download_qq"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "196cd951200f71ac", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Redirecting to the Play Store to search for QQ']\nassert False", "time": {"start": 1754401507682, "stop": 1754401523567, "duration": 15885}}], "categories": [], "tags": ["smoke"]}, "source": "ddc18dfac4b48d27.json", "parameterValues": []}