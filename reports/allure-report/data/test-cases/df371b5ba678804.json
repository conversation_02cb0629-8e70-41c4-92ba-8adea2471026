{"uid": "df371b5ba678804", "name": "测试navigate to shanghai disneyland能正常执行", "fullName": "testcases.test_ella.third_coupling.test_navigate_to_shanghai_disneyland.TestEllaNavigateShanghaiDisneyland#test_navigate_to_shanghai_disneyland", "historyId": "039e454ca4c329751543f1bfbb5e008e", "time": {"start": 1754451492189, "stop": 1754451512550, "duration": 20361}, "description": "navigate to shanghai disneyland", "descriptionHtml": "<p>navigate to shanghai disneyland</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451479688, "stop": 1754451492187, "duration": 12499}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451492187, "stop": 1754451492187, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "navigate to shanghai disneyland", "status": "passed", "steps": [{"name": "执行命令: navigate to shanghai disneyland", "time": {"start": 1754451492189, "stop": 1754451512354, "duration": 20165}, "status": "passed", "steps": [{"name": "执行命令: navigate to shanghai disneyland", "time": {"start": 1754451492189, "stop": 1754451512170, "duration": 19981}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451512170, "stop": 1754451512354, "duration": 184}, "status": "passed", "steps": [], "attachments": [{"uid": "9d27040b325784b4", "name": "测试总结", "source": "9d27040b325784b4.txt", "type": "text/plain", "size": 293}, {"uid": "e46b5eb41a68de6e", "name": "test_completed", "source": "e46b5eb41a68de6e.png", "type": "image/png", "size": 444228}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451512354, "stop": 1754451512356, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754451512356, "stop": 1754451512356, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451512356, "stop": 1754451512549, "duration": 193}, "status": "passed", "steps": [], "attachments": [{"uid": "77d329704e187fed", "name": "测试总结", "source": "77d329704e187fed.txt", "type": "text/plain", "size": 293}, {"uid": "52f505c0e1317310", "name": "test_completed", "source": "52f505c0e1317310.png", "type": "image/png", "size": 444228}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "182554fd3a576eaf", "name": "stdout", "source": "182554fd3a576eaf.txt", "type": "text/plain", "size": 16749}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451512550, "stop": 1754451512550, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451512551, "stop": 1754451513898, "duration": 1347}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigate_to_shanghai_disneyland"}, {"name": "subSuite", "value": "TestEllaNavigateShanghaiDisneyland"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigate_to_shanghai_disneyland"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "743aa2515bc2c753", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754401643543, "stop": 1754401663291, "duration": 19748}}], "categories": [], "tags": ["smoke"]}, "source": "df371b5ba678804.json", "parameterValues": []}