{"uid": "e068de8b2837d67", "name": "stop  screen recording能正常执行", "fullName": "testcases.test_ella.system_coupling.test_start_screen_recording.TestEllaStartScreenRecording#test_stop_screen_recording", "historyId": "cda905ef365af8bbcc5fba28f6bde9ea", "time": {"start": 1754450325189, "stop": 1754450343632, "duration": 18443}, "description": "stop  screen recording", "descriptionHtml": "<p>stop  screen recording</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450312046, "stop": 1754450325187, "duration": 13141}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450325187, "stop": 1754450325187, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "stop  screen recording", "status": "passed", "steps": [{"name": "执行命令: stop screen recording", "time": {"start": 1754450325189, "stop": 1754450343447, "duration": 18258}, "status": "passed", "steps": [{"name": "执行命令: stop screen recording", "time": {"start": 1754450325189, "stop": 1754450343255, "duration": 18066}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450343255, "stop": 1754450343447, "duration": 192}, "status": "passed", "steps": [], "attachments": [{"uid": "7ded6adfdd5c8e0d", "name": "测试总结", "source": "7ded6adfdd5c8e0d.txt", "type": "text/plain", "size": 191}, {"uid": "ff3e6d40cbe4b9c", "name": "test_completed", "source": "ff3e6d40cbe4b9c.png", "type": "image/png", "size": 592456}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450343447, "stop": 1754450343449, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754450343449, "stop": 1754450343449, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450343449, "stop": 1754450343632, "duration": 183}, "status": "passed", "steps": [], "attachments": [{"uid": "cc5b3403c9b79206", "name": "测试总结", "source": "cc5b3403c9b79206.txt", "type": "text/plain", "size": 191}, {"uid": "e263c448fe6fa27b", "name": "test_completed", "source": "e263c448fe6fa27b.png", "type": "image/png", "size": 592456}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "b64460ec2202be71", "name": "stdout", "source": "b64460ec2202be71.txt", "type": "text/plain", "size": 11835}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450343632, "stop": 1754450343632, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450343633, "stop": 1754450345048, "duration": 1415}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_start_screen_recording"}, {"name": "subSuite", "value": "TestEllaStartScreenRecording"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_start_screen_recording"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "745c1794a29194a9", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording finished']\nassert False", "time": {"start": 1754400494936, "stop": 1754400511085, "duration": 16149}}], "categories": [], "tags": ["smoke"]}, "source": "e068de8b2837d67.json", "parameterValues": []}