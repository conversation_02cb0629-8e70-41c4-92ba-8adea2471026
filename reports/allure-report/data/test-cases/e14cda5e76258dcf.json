{"uid": "e14cda5e76258dcf", "name": "测试play news", "fullName": "testcases.test_ella.dialogue.test_play_news.TestEllaOpenPlayNews#test_play_news", "historyId": "fee3033814a8b17ff8c8abe6bbcdc839", "time": {"start": 1754448360692, "stop": 1754448378327, "duration": 17635}, "description": "测试play news指令", "descriptionHtml": "<p>测试play news指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448348041, "stop": 1754448360690, "duration": 12649}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448360691, "stop": 1754448360691, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play news指令", "status": "passed", "steps": [{"name": "执行命令: play news", "time": {"start": 1754448360692, "stop": 1754448378091, "duration": 17399}, "status": "passed", "steps": [{"name": "执行命令: play news", "time": {"start": 1754448360692, "stop": 1754448377896, "duration": 17204}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448377896, "stop": 1754448378091, "duration": 195}, "status": "passed", "steps": [], "attachments": [{"uid": "d6a7da2c3bae05f1", "name": "测试总结", "source": "d6a7da2c3bae05f1.txt", "type": "text/plain", "size": 239}, {"uid": "f2254840b17dfb3c", "name": "test_completed", "source": "f2254840b17dfb3c.png", "type": "image/png", "size": 837444}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448378091, "stop": 1754448378092, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448378092, "stop": 1754448378327, "duration": 235}, "status": "passed", "steps": [], "attachments": [{"uid": "d10de60092cf3fce", "name": "测试总结", "source": "d10de60092cf3fce.txt", "type": "text/plain", "size": 239}, {"uid": "8c2d18fa0a4ae3c", "name": "test_completed", "source": "8c2d18fa0a4ae3c.png", "type": "image/png", "size": 837444}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "cfdfdc627dd8beb", "name": "stdout", "source": "cfdfdc627dd8beb.txt", "type": "text/plain", "size": 12702}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448378328, "stop": 1754448378328, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448378329, "stop": 1754448379743, "duration": 1414}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_news"}, {"name": "subSuite", "value": "TestEllaOpenPlayNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_news"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "4b3374b2c47a62f0", "status": "passed", "time": {"start": 1754398624023, "stop": 1754398639789, "duration": 15766}}], "categories": [], "tags": ["smoke"]}, "source": "e14cda5e76258dcf.json", "parameterValues": []}