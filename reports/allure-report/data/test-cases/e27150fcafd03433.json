{"uid": "e27150fcafd03433", "name": "测试close performance mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_close_performance_mode.TestEllaClosePerformanceMode#test_close_performance_mode", "historyId": "57c053de6acd628d4b4cd1230b702a40", "time": {"start": 1754452028631, "stop": 1754452028631, "duration": 0}, "description": "验证close performance mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证close performance mode指令返回预期的不支持响应</p>\n", "status": "failed", "statusMessage": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B6BA090>.wait_for_page_load", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_close_performance_mode.TestEllaClosePerformanceMode object at 0x000001E3895463D0>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 在启动应用前清除所有运行中的进程\n            self.clear_all_running_processes()\n    \n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n>           assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\nE           AssertionError: Ella页面加载失败\nE           assert False\nE            +  where False = wait_for_page_load(timeout=15)\nE            +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B6BA090>.wait_for_page_load\n\ntestcases\\test_ella\\base_ella_test.py:208: AssertionError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <testcases.test_ella.unsupported_commands.test_close_performance_mode.TestEllaClosePerformanceMode object at 0x000001E3895463D0>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 在启动应用前清除所有运行中的进程\n            self.clear_all_running_processes()\n    \n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n            assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\n    \n            log.info(\"✅ Ella应用启动成功\")\n            yield ella_page\n    \n        except Exception as e:\n            log.error(f\"❌ Ella应用启动异常: {e}\")\n>           pytest.fail(f\"Ella应用启动异常: {e}\")\nE           Failed: Ella应用启动异常: Ella页面加载失败\nE           assert False\nE            +  where False = wait_for_page_load(timeout=15)\nE            +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B6BA090>.wait_for_page_load\n\ntestcases\\test_ella\\base_ella_test.py:215: Failed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452028632, "stop": 1754452063574, "duration": 34942}, "status": "failed", "statusMessage": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B6BA090>.wait_for_page_load\n", "statusTrace": "  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 38, in run_old_style_hookwrapper\n    res = yield\n          ^^^^^\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 139, in _multicall\n    teardown.throw(exception)\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 53, in run_old_style_hookwrapper\n    return result.get_result()\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_result.py\", line 103, in get_result\n    raise exc.with_traceback(tb)\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 38, in run_old_style_hookwrapper\n    res = yield\n          ^^^^^\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 139, in _multicall\n    teardown.throw(exception)\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\_pytest\\setuponly.py\", line 36, in pytest_fixture_setup\n    return (yield)\n            ^^^^^\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 121, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 1195, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 922, in call_fixture_func\n    fixture_result = next(generator)\n                     ^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 215, in ella_app\n    pytest.fail(f\"Ella应用启动异常: {e}\")\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\_pytest\\outcomes.py\", line 177, in fail\n    raise Failed(msg=reason, pytrace=pytrace)\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452357295, "stop": 1754452357295, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证close performance mode指令返回预期的不支持响应", "status": "failed", "statusMessage": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B6BA090>.wait_for_page_load", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_close_performance_mode.TestEllaClosePerformanceMode object at 0x000001E3895463D0>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 在启动应用前清除所有运行中的进程\n            self.clear_all_running_processes()\n    \n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n>           assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\nE           AssertionError: Ella页面加载失败\nE           assert False\nE            +  where False = wait_for_page_load(timeout=15)\nE            +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B6BA090>.wait_for_page_load\n\ntestcases\\test_ella\\base_ella_test.py:208: AssertionError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <testcases.test_ella.unsupported_commands.test_close_performance_mode.TestEllaClosePerformanceMode object at 0x000001E3895463D0>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 在启动应用前清除所有运行中的进程\n            self.clear_all_running_processes()\n    \n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n            assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\n    \n            log.info(\"✅ Ella应用启动成功\")\n            yield ella_page\n    \n        except Exception as e:\n            log.error(f\"❌ Ella应用启动异常: {e}\")\n>           pytest.fail(f\"Ella应用启动异常: {e}\")\nE           Failed: Ella应用启动异常: Ella页面加载失败\nE           assert False\nE            +  where False = wait_for_page_load(timeout=15)\nE            +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B6BA090>.wait_for_page_load\n\ntestcases\\test_ella\\base_ella_test.py:215: Failed", "steps": [], "attachments": [{"uid": "6c2a361be8201913", "name": "stdout", "source": "6c2a361be8201913.txt", "type": "text/plain", "size": 2876}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 1}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452371304, "stop": 1754452371304, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_close_performance_mode"}, {"name": "subSuite", "value": "TestEllaClosePerformanceMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_close_performance_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "b189078eca379002", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402129908, "stop": 1754402141119, "duration": 11211}}], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "e27150fcafd03433.json", "parameterValues": []}