{"uid": "e2f40efc3b65c3eb", "name": "测试long screenshot能正常执行", "fullName": "testcases.test_ella.system_coupling.test_long_screenshot.TestEllaLongScreenshot#test_long_screenshot", "historyId": "6f7052acfdd45e34e5dded44ad87416e", "time": {"start": 1754399945669, "stop": 1754399960605, "duration": 14936}, "description": "long screenshot", "descriptionHtml": "<p>long screenshot</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399932516, "stop": 1754399945667, "duration": 13151}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399945667, "stop": 1754399945667, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "long screenshot", "status": "passed", "steps": [{"name": "执行命令: long screenshot", "time": {"start": 1754399945669, "stop": 1754399960325, "duration": 14656}, "status": "passed", "steps": [{"name": "执行命令: long screenshot", "time": {"start": 1754399945669, "stop": 1754399960074, "duration": 14405}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399960074, "stop": 1754399960324, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "f51d5209b8799d7a", "name": "测试总结", "source": "f51d5209b8799d7a.txt", "type": "text/plain", "size": 469}, {"uid": "e98245b5c0ae5907", "name": "test_completed", "source": "e98245b5c0ae5907.png", "type": "image/png", "size": 562202}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证文件存在", "time": {"start": 1754399960325, "stop": 1754399960325, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399960325, "stop": 1754399960605, "duration": 280}, "status": "passed", "steps": [], "attachments": [{"uid": "8490392d91329f02", "name": "测试总结", "source": "8490392d91329f02.txt", "type": "text/plain", "size": 469}, {"uid": "3656df975fee45d3", "name": "test_completed", "source": "3656df975fee45d3.png", "type": "image/png", "size": 561601}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "6f0559837d0f1e94", "name": "stdout", "source": "6f0559837d0f1e94.txt", "type": "text/plain", "size": 13352}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399960607, "stop": 1754399960607, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399960608, "stop": 1754399961924, "duration": 1316}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_long_screenshot"}, {"name": "subSuite", "value": "TestEllaLongScreenshot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_long_screenshot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "e2f40efc3b65c3eb.json", "parameterValues": []}