{"uid": "e4f52714d79f9e15", "name": "测试open dialer能正常执行", "fullName": "testcases.test_ella.component_coupling.test_open_dialer.TestEllaCommandConcise#test_open_dialer", "historyId": "936ae2bf6db744b69d4acf28b22f7646", "time": {"start": 1754397246702, "stop": 1754397270687, "duration": 23985}, "description": "open dialer", "descriptionHtml": "<p>open dialer</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397233156, "stop": 1754397246702, "duration": 13546}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397246702, "stop": 1754397246702, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "open dialer", "status": "passed", "steps": [{"name": "执行命令: open dialer", "time": {"start": 1754397246702, "stop": 1754397270379, "duration": 23677}, "status": "passed", "steps": [{"name": "执行命令: open dialer", "time": {"start": 1754397246702, "stop": 1754397270046, "duration": 23344}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397270046, "stop": 1754397270377, "duration": 331}, "status": "passed", "steps": [], "attachments": [{"uid": "6c11eddaa3a66ea7", "name": "测试总结", "source": "6c11eddaa3a66ea7.txt", "type": "text/plain", "size": 206}, {"uid": "603e0529d63fda0e", "name": "test_completed", "source": "603e0529d63fda0e.png", "type": "image/png", "size": 597529}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1754397270379, "stop": 1754397270383, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397270383, "stop": 1754397270687, "duration": 304}, "status": "passed", "steps": [], "attachments": [{"uid": "c5c28bd8022bdb7f", "name": "测试总结", "source": "c5c28bd8022bdb7f.txt", "type": "text/plain", "size": 206}, {"uid": "7082ebe0ac877c7", "name": "test_completed", "source": "7082ebe0ac877c7.png", "type": "image/png", "size": 597886}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "80664663309b9671", "name": "stdout", "source": "80664663309b9671.txt", "type": "text/plain", "size": 14363}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397270689, "stop": 1754397270689, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397270691, "stop": 1754397271989, "duration": 1298}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_dialer"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_dialer"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "e4f52714d79f9e15.json", "parameterValues": []}