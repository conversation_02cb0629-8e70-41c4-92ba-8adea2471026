{"uid": "e4feab036331402e", "name": "测试open font family settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_open_font_family_settings.TestEllaOpenSettings#test_open_font_family_settings", "historyId": "fd79b0d35f1f4639521f70b269d3aadc", "time": {"start": 1754453540495, "stop": 1754453556552, "duration": 16057}, "description": "验证open font family settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证open font family settings指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453528460, "stop": 1754453540494, "duration": 12034}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453540494, "stop": 1754453540494, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证open font family settings指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: open font family settings", "time": {"start": 1754453540495, "stop": 1754453556370, "duration": 15875}, "status": "passed", "steps": [{"name": "执行命令: open font family settings", "time": {"start": 1754453540495, "stop": 1754453556190, "duration": 15695}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453556190, "stop": 1754453556370, "duration": 180}, "status": "passed", "steps": [], "attachments": [{"uid": "b016b1f03d85a23c", "name": "测试总结", "source": "b016b1f03d85a23c.txt", "type": "text/plain", "size": 232}, {"uid": "294930037c0fd8ef", "name": "test_completed", "source": "294930037c0fd8ef.png", "type": "image/png", "size": 484226}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453556370, "stop": 1754453556371, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453556371, "stop": 1754453556552, "duration": 181}, "status": "passed", "steps": [], "attachments": [{"uid": "61c88458d1ab9af5", "name": "测试总结", "source": "61c88458d1ab9af5.txt", "type": "text/plain", "size": 232}, {"uid": "de817a5a67fa3f91", "name": "test_completed", "source": "de817a5a67fa3f91.png", "type": "image/png", "size": 484235}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "fca38402422485d5", "name": "stdout", "source": "fca38402422485d5.txt", "type": "text/plain", "size": 11829}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453556552, "stop": 1754453556552, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453556553, "stop": 1754453557970, "duration": 1417}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_font_family_settings"}, {"name": "subSuite", "value": "TestEllaOpenSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_font_family_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "8bcb19201db38cee", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403562055, "stop": 1754403578855, "duration": 16800}}], "categories": [], "tags": ["smoke"]}, "source": "e4feab036331402e.json", "parameterValues": []}