{"uid": "e5968f58913b35a0", "name": "测试what is apec?能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_is_apec.TestEllaWhatIsApec#test_what_is_apec", "historyId": "0c4bd81bf0dbac094265e3ac47550bbd", "time": {"start": 1754448917614, "stop": 1754448933320, "duration": 15706}, "description": "what is apec?", "descriptionHtml": "<p>what is apec?</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448905037, "stop": 1754448917613, "duration": 12576}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448917613, "stop": 1754448917613, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "what is apec?", "status": "passed", "steps": [{"name": "执行命令: what is apec?", "time": {"start": 1754448917615, "stop": 1754448933116, "duration": 15501}, "status": "passed", "steps": [{"name": "执行命令: what is apec?", "time": {"start": 1754448917615, "stop": 1754448932933, "duration": 15318}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448932933, "stop": 1754448933116, "duration": 183}, "status": "passed", "steps": [], "attachments": [{"uid": "dbdb46234be0da03", "name": "测试总结", "source": "dbdb46234be0da03.txt", "type": "text/plain", "size": 733}, {"uid": "19107ed1b9dd4c5", "name": "test_completed", "source": "19107ed1b9dd4c5.png", "type": "image/png", "size": 673662}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448933116, "stop": 1754448933117, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448933117, "stop": 1754448933320, "duration": 203}, "status": "passed", "steps": [], "attachments": [{"uid": "63bdb98e1f785eea", "name": "测试总结", "source": "63bdb98e1f785eea.txt", "type": "text/plain", "size": 733}, {"uid": "ca567e2279dd4207", "name": "test_completed", "source": "ca567e2279dd4207.png", "type": "image/png", "size": 673528}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "9801cdf82827b458", "name": "stdout", "source": "9801cdf82827b458.txt", "type": "text/plain", "size": 14145}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448933321, "stop": 1754448933321, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448933322, "stop": 1754448934776, "duration": 1454}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_is_apec"}, {"name": "subSuite", "value": "TestEllaWhatIsApec"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_is_apec"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "38124d92f03aa953", "status": "passed", "time": {"start": 1754399112436, "stop": 1754399127749, "duration": 15313}}], "categories": [], "tags": ["smoke"]}, "source": "e5968f58913b35a0.json", "parameterValues": []}