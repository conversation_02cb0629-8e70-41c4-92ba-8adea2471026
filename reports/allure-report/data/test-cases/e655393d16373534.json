{"uid": "e655393d16373534", "name": "测试change your language to chinese能正常执行", "fullName": "testcases.test_ella.system_coupling.test_change_your_language_to_chinese.TestEllaChangeYourLanguageChinese#test_change_your_language_to_chinese", "historyId": "9b6faa79e3fe09fed639b1092082745b", "time": {"start": 1754449379042, "stop": 1754449391214, "duration": 12172}, "description": "change your language to chinese", "descriptionHtml": "<p>change your language to chinese</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449366435, "stop": 1754449379041, "duration": 12606}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449379041, "stop": 1754449379041, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "change your language to chinese", "status": "passed", "steps": [{"name": "执行命令: change your language to chinese", "time": {"start": 1754449379042, "stop": 1754449391036, "duration": 11994}, "status": "passed", "steps": [{"name": "执行命令: change your language to chinese", "time": {"start": 1754449379042, "stop": 1754449390851, "duration": 11809}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449390851, "stop": 1754449391036, "duration": 185}, "status": "passed", "steps": [], "attachments": [{"uid": "a18b11ce5f1eb08b", "name": "测试总结", "source": "a18b11ce5f1eb08b.txt", "type": "text/plain", "size": 285}, {"uid": "97a7e303f3e2da11", "name": "test_completed", "source": "97a7e303f3e2da11.png", "type": "image/png", "size": 594937}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "记录测试结果", "time": {"start": 1754449391036, "stop": 1754449391214, "duration": 178}, "status": "passed", "steps": [], "attachments": [{"uid": "10ddbab64fd2f9d", "name": "测试总结", "source": "10ddbab64fd2f9d.txt", "type": "text/plain", "size": 285}, {"uid": "b575c11f7759f613", "name": "test_completed", "source": "b575c11f7759f613.png", "type": "image/png", "size": 594940}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "b30d9de76d25106d", "name": "stdout", "source": "b30d9de76d25106d.txt", "type": "text/plain", "size": 11141}], "parameters": [], "attachmentStep": false, "stepsCount": 4, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449391214, "stop": 1754449391214, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449391216, "stop": 1754449392640, "duration": 1424}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_change_your_language_to_chinese"}, {"name": "subSuite", "value": "TestEllaChangeYourLanguageChinese"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_change_your_language_to_chinese"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "1f38cfd1433da4f", "status": "passed", "time": {"start": 1754399576019, "stop": 1754399587350, "duration": 11331}}], "categories": [], "tags": ["smoke"]}, "source": "e655393d16373534.json", "parameterValues": []}