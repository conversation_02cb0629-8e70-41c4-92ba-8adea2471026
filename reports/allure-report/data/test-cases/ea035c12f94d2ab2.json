{"uid": "ea035c12f94d2ab2", "name": "测试help me write an email能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_write_an_email.TestEllaHelpMeWriteAnEmail#test_help_me_write_an_email", "historyId": "70c9bd8c4aab57e96eb06acb93ca2223", "time": {"start": 1754452946349, "stop": 1754452961574, "duration": 15225}, "description": "help me write an email", "descriptionHtml": "<p>help me write an email</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452934207, "stop": 1754452946347, "duration": 12140}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452946347, "stop": 1754452946347, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "help me write an email", "status": "passed", "steps": [{"name": "执行命令: help me write an email", "time": {"start": 1754452946349, "stop": 1754452961393, "duration": 15044}, "status": "passed", "steps": [{"name": "执行命令: help me write an email", "time": {"start": 1754452946349, "stop": 1754452961172, "duration": 14823}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452961172, "stop": 1754452961393, "duration": 221}, "status": "passed", "steps": [], "attachments": [{"uid": "f2678032221f6299", "name": "测试总结", "source": "f2678032221f6299.txt", "type": "text/plain", "size": 647}, {"uid": "9e57bdce1e2d50e5", "name": "test_completed", "source": "9e57bdce1e2d50e5.png", "type": "image/png", "size": 554590}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754452961393, "stop": 1754452961394, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452961394, "stop": 1754452961573, "duration": 179}, "status": "passed", "steps": [], "attachments": [{"uid": "25af747d4513917", "name": "测试总结", "source": "25af747d4513917.txt", "type": "text/plain", "size": 647}, {"uid": "117e8d4c944e1ccf", "name": "test_completed", "source": "117e8d4c944e1ccf.png", "type": "image/png", "size": 553438}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "b6092e4e78ca3657", "name": "stdout", "source": "b6092e4e78ca3657.txt", "type": "text/plain", "size": 13827}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452961575, "stop": 1754452961575, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452961575, "stop": 1754452962926, "duration": 1351}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_write_an_email"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnEmail"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_write_an_email"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "2873ce24215c8fc8", "status": "passed", "time": {"start": 1754402960040, "stop": 1754402973051, "duration": 13011}}], "categories": [], "tags": ["smoke"]}, "source": "ea035c12f94d2ab2.json", "parameterValues": []}