{"uid": "ea6730154255a6b7", "name": "测试summarize content on this page能正常执行", "fullName": "testcases.test_ella.dialogue.test_summarize_content_on_this_page.TestEllaSummarizeContentThisPage#test_summarize_content_on_this_page", "historyId": "3e1e4da6344de7cdf40fa1d59c43dcc3", "time": {"start": 1754398910901, "stop": 1754398923728, "duration": 12827}, "description": "summarize content on this page", "descriptionHtml": "<p>summarize content on this page</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398897678, "stop": 1754398910900, "duration": 13222}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398910901, "stop": 1754398910901, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "summarize content on this page", "status": "passed", "steps": [{"name": "执行命令: summarize content on this page", "time": {"start": 1754398910901, "stop": 1754398923411, "duration": 12510}, "status": "passed", "steps": [{"name": "执行命令: summarize content on this page", "time": {"start": 1754398910901, "stop": 1754398923143, "duration": 12242}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398923143, "stop": 1754398923410, "duration": 267}, "status": "passed", "steps": [], "attachments": [{"uid": "816a7a79f0d90136", "name": "测试总结", "source": "816a7a79f0d90136.txt", "type": "text/plain", "size": 277}, {"uid": "46d18c296be743a8", "name": "test_completed", "source": "46d18c296be743a8.png", "type": "image/png", "size": 570898}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398923411, "stop": 1754398923420, "duration": 9}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398923420, "stop": 1754398923726, "duration": 306}, "status": "passed", "steps": [], "attachments": [{"uid": "ea60cb431ce3b51b", "name": "测试总结", "source": "ea60cb431ce3b51b.txt", "type": "text/plain", "size": 277}, {"uid": "405ccc773d5e643", "name": "test_completed", "source": "405ccc773d5e643.png", "type": "image/png", "size": 571510}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "487d9eb9e88b0f42", "name": "stdout", "source": "487d9eb9e88b0f42.txt", "type": "text/plain", "size": 11510}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398923729, "stop": 1754398923729, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398923731, "stop": 1754398925019, "duration": 1288}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_summarize_content_on_this_page"}, {"name": "subSuite", "value": "TestEllaSummarizeContentThisPage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_summarize_content_on_this_page"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "ea6730154255a6b7.json", "parameterValues": []}