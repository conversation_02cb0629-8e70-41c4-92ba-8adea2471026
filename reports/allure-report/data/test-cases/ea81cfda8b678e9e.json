{"uid": "ea81cfda8b678e9e", "name": "测试navigation to the first address in the image能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image.TestEllaNavigationFirstAddressImage#test_navigation_to_the_first_address_in_the_image", "historyId": "9c84b087eb7d9fde94ed5bb5370b275b", "time": {"start": 1754403534392, "stop": 1754403547438, "duration": 13046}, "description": "navigation to the first address in the image", "descriptionHtml": "<p>navigation to the first address in the image</p>\n", "status": "broken", "statusMessage": "ValueError: too many values to unpack (expected 3)", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image.TestEllaNavigationFirstAddressImage object at 0x00000240FF2C9010>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000024081A40CD0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_navigation_to_the_first_address_in_the_image(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n>           initial_status, final_status, response_text = self.simple_command_test(\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\nE           ValueError: too many values to unpack (expected 3)\n\ntestcases\\test_ella\\unsupported_commands\\test_navigation_to_the_first_address_in_the_image.py:26: ValueError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754403521463, "stop": 1754403534391, "duration": 12928}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754403534391, "stop": 1754403534391, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "navigation to the first address in the image", "status": "broken", "statusMessage": "ValueError: too many values to unpack (expected 3)", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image.TestEllaNavigationFirstAddressImage object at 0x00000240FF2C9010>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000024081A40CD0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_navigation_to_the_first_address_in_the_image(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n>           initial_status, final_status, response_text = self.simple_command_test(\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\nE           ValueError: too many values to unpack (expected 3)\n\ntestcases\\test_ella\\unsupported_commands\\test_navigation_to_the_first_address_in_the_image.py:26: ValueError", "steps": [{"name": "执行命令: navigation to the first address in the image", "time": {"start": 1754403534392, "stop": 1754403547436, "duration": 13044}, "status": "broken", "statusMessage": "ValueError: too many values to unpack (expected 3)\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_navigation_to_the_first_address_in_the_image.py\", line 26, in test_navigation_to_the_first_address_in_the_image\n    initial_status, final_status, response_text = self.simple_command_test(\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "steps": [{"name": "执行命令: navigation to the first address in the image", "time": {"start": 1754403534392, "stop": 1754403547184, "duration": 12792}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754403547184, "stop": 1754403547434, "duration": 250}, "status": "passed", "steps": [], "attachments": [{"uid": "964179dd1ad466bb", "name": "测试总结", "source": "964179dd1ad466bb.txt", "type": "text/plain", "size": 654}, {"uid": "ede053af0d833d0f", "name": "test_completed", "source": "ede053af0d833d0f.png", "type": "image/png", "size": 612265}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 2}], "attachments": [{"uid": "d4c95adc25b51ae", "name": "stdout", "source": "d4c95adc25b51ae.txt", "type": "text/plain", "size": 14136}], "parameters": [], "attachmentStep": false, "stepsCount": 3, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754403547451, "stop": 1754403547746, "duration": 295}, "status": "passed", "steps": [], "attachments": [{"uid": "f6ad9b3dd3b88f83", "name": "失败截图-TestEllaNavigationFirstAddressImage", "source": "f6ad9b3dd3b88f83.png", "type": "image/png", "size": 611935}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754403547748, "stop": 1754403549014, "duration": 1266}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_navigation_to_the_first_address_in_the_image"}, {"name": "subSuite", "value": "TestEllaNavigationFirstAddressImage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "ea81cfda8b678e9e.json", "parameterValues": []}