{"uid": "eb1da234ac691e7c", "name": "测试download app能正常执行", "fullName": "testcases.test_ella.third_coupling.test_download_app.TestEllaDownloadApp#test_download_app", "historyId": "578e52c6d5e868d5464682b454971c51", "time": {"start": 1754451298290, "stop": 1754451312878, "duration": 14588}, "description": "download app", "descriptionHtml": "<p>download app</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451285719, "stop": 1754451298288, "duration": 12569}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451298288, "stop": 1754451298288, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "download app", "status": "passed", "steps": [{"name": "执行命令: download app", "time": {"start": 1754451298290, "stop": 1754451312692, "duration": 14402}, "status": "passed", "steps": [{"name": "执行命令: download app", "time": {"start": 1754451298290, "stop": 1754451312527, "duration": 14237}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451312527, "stop": 1754451312692, "duration": 165}, "status": "passed", "steps": [], "attachments": [{"uid": "5ccfc44a15fac8df", "name": "测试总结", "source": "5ccfc44a15fac8df.txt", "type": "text/plain", "size": 176}, {"uid": "1637abd973de3673", "name": "test_completed", "source": "1637abd973de3673.png", "type": "image/png", "size": 372226}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含在期望中", "time": {"start": 1754451312692, "stop": 1754451312694, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451312694, "stop": 1754451312877, "duration": 183}, "status": "passed", "steps": [], "attachments": [{"uid": "d62a708e630d9a8e", "name": "测试总结", "source": "d62a708e630d9a8e.txt", "type": "text/plain", "size": 176}, {"uid": "e9715f00fdf52095", "name": "test_completed", "source": "e9715f00fdf52095.png", "type": "image/png", "size": 373321}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "8e15dd389dedb3da", "name": "stdout", "source": "8e15dd389dedb3da.txt", "type": "text/plain", "size": 11797}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451312878, "stop": 1754451312878, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451312879, "stop": 1754451314267, "duration": 1388}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_download_app"}, {"name": "subSuite", "value": "TestEllaDownloadApp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_download_app"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "6af1de0ba659e3b1", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Which app']\nassert False", "time": {"start": 1754401453214, "stop": 1754401465583, "duration": 12369}}], "categories": [], "tags": ["smoke"]}, "source": "eb1da234ac691e7c.json", "parameterValues": []}