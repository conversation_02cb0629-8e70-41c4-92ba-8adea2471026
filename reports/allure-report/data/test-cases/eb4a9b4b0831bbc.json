{"uid": "eb4a9b4b0831bbc", "name": "测试help me write an thanks email能正常执行", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_write_an_thanks_email.TestEllaHelpMeWriteAnThanksEmail#test_help_me_write_an_thanks_email", "historyId": "306cbf11cdbcb045eb3c3c716515b1d6", "time": {"start": 1754452974940, "stop": 1754452989831, "duration": 14891}, "description": "help me write an thanks email", "descriptionHtml": "<p>help me write an thanks email</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452962929, "stop": 1754452974939, "duration": 12010}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452974939, "stop": 1754452974939, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "help me write an thanks email", "status": "passed", "steps": [{"name": "执行命令: help me write an thanks email", "time": {"start": 1754452974940, "stop": 1754452989584, "duration": 14644}, "status": "passed", "steps": [{"name": "执行命令: help me write an thanks email", "time": {"start": 1754452974940, "stop": 1754452989387, "duration": 14447}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452989387, "stop": 1754452989583, "duration": 196}, "status": "passed", "steps": [], "attachments": [{"uid": "c46f667079280f44", "name": "测试总结", "source": "c46f667079280f44.txt", "type": "text/plain", "size": 1158}, {"uid": "25952ed54e229cf3", "name": "test_completed", "source": "25952ed54e229cf3.png", "type": "image/png", "size": 829406}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754452989584, "stop": 1754452989585, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452989585, "stop": 1754452989831, "duration": 246}, "status": "passed", "steps": [], "attachments": [{"uid": "3321faa7ff3c727f", "name": "测试总结", "source": "3321faa7ff3c727f.txt", "type": "text/plain", "size": 1158}, {"uid": "6bb5695a5234c478", "name": "test_completed", "source": "6bb5695a5234c478.png", "type": "image/png", "size": 829216}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "e23433ae38ec1b5", "name": "stdout", "source": "e23433ae38ec1b5.txt", "type": "text/plain", "size": 15907}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452989832, "stop": 1754452989832, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452989833, "stop": 1754452991162, "duration": 1329}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "Ella技能"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_write_an_thanks_email"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnThanksEmail"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_write_an_thanks_email"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "18bae186eb61c6cc", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['thank you']\nassert False", "time": {"start": 1754402987211, "stop": 1754403000102, "duration": 12891}}], "categories": [], "tags": ["smoke"]}, "source": "eb4a9b4b0831bbc.json", "parameterValues": []}