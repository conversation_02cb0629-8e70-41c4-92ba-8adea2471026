{"uid": "ecd122ba3ea3ea0a", "name": "测试set an alarm at 8 am", "fullName": "testcases.test_ella.component_coupling.test_set_an_alarm_at_8_am.TestEllaOpenClock#test_set_an_alarm_at_8_am", "historyId": "de0ed312f350c708e7a00bb74aeaac0f", "time": {"start": 1754397691760, "stop": 1754397706148, "duration": 14388}, "description": "测试set an alarm at 8 am指令", "descriptionHtml": "<p>测试set an alarm at 8 am指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754397678806, "stop": 1754397691759, "duration": 12953}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754397691759, "stop": 1754397691759, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试set an alarm at 8 am指令", "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "time": {"start": 1754397691760, "stop": 1754397705882, "duration": 14122}, "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "time": {"start": 1754397691760, "stop": 1754397705638, "duration": 13878}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397705638, "stop": 1754397705881, "duration": 243}, "status": "passed", "steps": [], "attachments": [{"uid": "6352f0eafb6854db", "name": "测试总结", "source": "6352f0eafb6854db.txt", "type": "text/plain", "size": 197}, {"uid": "3d293c6a033e369b", "name": "test_completed", "source": "3d293c6a033e369b.png", "type": "image/png", "size": 433695}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754397705882, "stop": 1754397705889, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754397705889, "stop": 1754397706147, "duration": 258}, "status": "passed", "steps": [], "attachments": [{"uid": "17a6671341c46e98", "name": "测试总结", "source": "17a6671341c46e98.txt", "type": "text/plain", "size": 197}, {"uid": "a406ef75eb551fae", "name": "test_completed", "source": "a406ef75eb551fae.png", "type": "image/png", "size": 433440}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "5164d6911a9ad47f", "name": "stdout", "source": "5164d6911a9ad47f.txt", "type": "text/plain", "size": 11960}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754397706149, "stop": 1754397706149, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754397706152, "stop": 1754397707405, "duration": 1253}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_set_an_alarm_at_8_am"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_set_an_alarm_at_8_am"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "ecd122ba3ea3ea0a.json", "parameterValues": []}