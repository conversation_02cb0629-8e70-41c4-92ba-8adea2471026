{"uid": "ee418c4c9a2793b3", "name": "测试download basketball返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_download_basketball.TestEllaDownloadBasketball#test_download_basketball", "historyId": "b3a1d4f5c4ea6e3e176798cf3deda55b", "time": {"start": 1754452498881, "stop": 1754452514424, "duration": 15543}, "description": "验证download basketball指令返回预期的不支持响应", "descriptionHtml": "<p>验证download basketball指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452486360, "stop": 1754452498880, "duration": 12520}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452498880, "stop": 1754452498880, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证download basketball指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: download basketball", "time": {"start": 1754452498881, "stop": 1754452514237, "duration": 15356}, "status": "passed", "steps": [{"name": "执行命令: download basketball", "time": {"start": 1754452498881, "stop": 1754452514022, "duration": 15141}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452514022, "stop": 1754452514237, "duration": 215}, "status": "passed", "steps": [], "attachments": [{"uid": "26bb86d440394c1b", "name": "测试总结", "source": "26bb86d440394c1b.txt", "type": "text/plain", "size": 229}, {"uid": "fff7409da3e34fbd", "name": "test_completed", "source": "fff7409da3e34fbd.png", "type": "image/png", "size": 427198}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452514237, "stop": 1754452514238, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452514238, "stop": 1754452514424, "duration": 186}, "status": "passed", "steps": [], "attachments": [{"uid": "dae56b8ff04a37bb", "name": "测试总结", "source": "dae56b8ff04a37bb.txt", "type": "text/plain", "size": 229}, {"uid": "e6882eb7d6e38ebd", "name": "test_completed", "source": "e6882eb7d6e38ebd.png", "type": "image/png", "size": 428451}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3ecc75b52cbbe701", "name": "stdout", "source": "3ecc75b52cbbe701.txt", "type": "text/plain", "size": 11812}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "ella_app::0", "time": {"start": 1754452514425, "stop": 1754452515817, "duration": 1392}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure::0", "time": {"start": 1754452514425, "stop": 1754452514425, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_download_basketball"}, {"name": "subSuite", "value": "TestEllaDownloadBasketball"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_download_basketball"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "c3316fab4b6571cf", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402530444, "stop": 1754402543014, "duration": 12570}}], "categories": [], "tags": ["smoke"]}, "source": "ee418c4c9a2793b3.json", "parameterValues": []}