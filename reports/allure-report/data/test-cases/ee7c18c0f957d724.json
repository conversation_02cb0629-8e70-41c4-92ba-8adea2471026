{"uid": "ee7c18c0f957d724", "name": "测试set flip case feature返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_flip_case_feature.TestEllaSetFlipCaseFeature#test_set_flip_case_feature", "historyId": "3a27fef360a79f638f96f0461df262da", "time": {"start": 1754454496546, "stop": 1754454509946, "duration": 13400}, "description": "验证set flip case feature指令返回预期的不支持响应", "descriptionHtml": "<p>验证set flip case feature指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454484018, "stop": 1754454496545, "duration": 12527}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454496545, "stop": 1754454496545, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set flip case feature指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set flip case feature", "time": {"start": 1754454496547, "stop": 1754454509753, "duration": 13206}, "status": "passed", "steps": [{"name": "执行命令: set flip case feature", "time": {"start": 1754454496547, "stop": 1754454509568, "duration": 13021}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454509568, "stop": 1754454509753, "duration": 185}, "status": "passed", "steps": [], "attachments": [{"uid": "e3ca1cc36d53a051", "name": "测试总结", "source": "e3ca1cc36d53a051.txt", "type": "text/plain", "size": 233}, {"uid": "b3962ca612de096f", "name": "test_completed", "source": "b3962ca612de096f.png", "type": "image/png", "size": 491204}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454509753, "stop": 1754454509754, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454509754, "stop": 1754454509945, "duration": 191}, "status": "passed", "steps": [], "attachments": [{"uid": "d16d78192a486ab1", "name": "测试总结", "source": "d16d78192a486ab1.txt", "type": "text/plain", "size": 233}, {"uid": "747bf8c056371f2d", "name": "test_completed", "source": "747bf8c056371f2d.png", "type": "image/png", "size": 491527}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "6a480bd9e73838d9", "name": "stdout", "source": "6a480bd9e73838d9.txt", "type": "text/plain", "size": 11324}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454509946, "stop": 1754454509946, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454509947, "stop": 1754454511385, "duration": 1438}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_flip_case_feature"}, {"name": "subSuite", "value": "TestEllaSetFlipCaseFeature"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_flip_case_feature"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "2df5df1d22a2c47a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404497358, "stop": 1754404508427, "duration": 11069}}], "categories": [], "tags": ["smoke"]}, "source": "ee7c18c0f957d724.json", "parameterValues": []}