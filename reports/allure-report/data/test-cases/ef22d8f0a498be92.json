{"uid": "ef22d8f0a498be92", "name": "测试what time is it now能正常执行", "fullName": "testcases.test_ella.dialogue.test_what_time_is_it_now.TestEllaWhatTimeIsItNow#test_what_time_is_it_now", "historyId": "519d11d818a361bc75d5af94c6a68b28", "time": {"start": 1754399291821, "stop": 1754399304794, "duration": 12973}, "description": "what time is it now", "descriptionHtml": "<p>what time is it now</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754399278928, "stop": 1754399291821, "duration": 12893}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754399291821, "stop": 1754399291821, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "what time is it now", "status": "passed", "steps": [{"name": "执行命令: what time is it now", "time": {"start": 1754399291822, "stop": 1754399304498, "duration": 12676}, "status": "passed", "steps": [{"name": "执行命令: what time is it now", "time": {"start": 1754399291822, "stop": 1754399304181, "duration": 12359}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399304181, "stop": 1754399304496, "duration": 315}, "status": "passed", "steps": [], "attachments": [{"uid": "51a80c07f93cb8bc", "name": "测试总结", "source": "51a80c07f93cb8bc.txt", "type": "text/plain", "size": 175}, {"uid": "86bd906ed19b91f2", "name": "test_completed", "source": "86bd906ed19b91f2.png", "type": "image/png", "size": 577090}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754399304498, "stop": 1754399304503, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754399304503, "stop": 1754399304793, "duration": 290}, "status": "passed", "steps": [], "attachments": [{"uid": "685963e60205fba7", "name": "测试总结", "source": "685963e60205fba7.txt", "type": "text/plain", "size": 175}, {"uid": "4a3d3266010dae5", "name": "test_completed", "source": "4a3d3266010dae5.png", "type": "image/png", "size": 576822}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "7dbc7f4490a3f4d7", "name": "stdout", "source": "7dbc7f4490a3f4d7.txt", "type": "text/plain", "size": 11139}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754399304798, "stop": 1754399304798, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754399304801, "stop": 1754399306052, "duration": 1251}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_time_is_it_now"}, {"name": "subSuite", "value": "TestEllaWhatTimeIsItNow"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_time_is_it_now"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "ef22d8f0a498be92.json", "parameterValues": []}