{"uid": "ef7a049e326a2e58", "name": "测试redial", "fullName": "testcases.test_ella.unsupported_commands.test_redial.TestEllaOpenPlayPoliticalNews#test_play_political_news", "historyId": "4b05cca85015ddfc6c9bfc65934f9bcc", "time": {"start": 1754453935890, "stop": 1754453958417, "duration": 22527}, "description": "测试redial指令", "descriptionHtml": "<p>测试redial指令</p>\n", "status": "failed", "statusMessage": "AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', 'No call record', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_redial.TestEllaOpenPlayPoliticalNews object at 0x000001E389602DD0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B6D0790>\n\n    @allure.title(\"测试redial\")\n    @allure.description(\"测试redial指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_play_political_news(self, ella_app):\n        \"\"\"测试redial命令\"\"\"\n        command = \"redial\"\n        app_name = 'contacts'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = []\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', 'No call record', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_redial.py:34: AssertionError", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453923048, "stop": 1754453935889, "duration": 12841}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453935889, "stop": 1754453935889, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试redial指令", "status": "failed", "statusMessage": "AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', 'No call record', '', '']'\nassert False", "statusTrace": "self = <testcases.test_ella.unsupported_commands.test_redial.TestEllaOpenPlayPoliticalNews object at 0x000001E389602DD0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B6D0790>\n\n    @allure.title(\"测试redial\")\n    @allure.description(\"测试redial指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_play_political_news(self, ella_app):\n        \"\"\"测试redial命令\"\"\"\n        command = \"redial\"\n        app_name = 'contacts'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = []\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', 'No call record', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_redial.py:34: AssertionError", "steps": [{"name": "执行命令: redial", "time": {"start": 1754453935890, "stop": 1754453958416, "duration": 22526}, "status": "passed", "steps": [{"name": "执行命令: redial", "time": {"start": 1754453935890, "stop": 1754453958216, "duration": 22326}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453958216, "stop": 1754453958416, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "e23fe92b439ac211", "name": "测试总结", "source": "e23fe92b439ac211.txt", "type": "text/plain", "size": 150}, {"uid": "4f8f4fdd170ac3f3", "name": "test_completed", "source": "4f8f4fdd170ac3f3.png", "type": "image/png", "size": 423625}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754453958416, "stop": 1754453958417, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证contacts已打开", "time": {"start": 1754453958417, "stop": 1754453958417, "duration": 0}, "status": "failed", "statusMessage": "AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', 'No call record', '', '']'\nassert False\n", "statusTrace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_redial.py\", line 34, in test_play_political_news\n    assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 0}], "attachments": [{"uid": "d0b880e4fb31adf9", "name": "stdout", "source": "d0b880e4fb31adf9.txt", "type": "text/plain", "size": 11697}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": true, "attachmentsCount": 3}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453958421, "stop": 1754453958583, "duration": 162}, "status": "passed", "steps": [], "attachments": [{"uid": "f6daae264f983b59", "name": "失败截图-TestEllaOpenPlayPoliticalNews", "source": "f6daae264f983b59.png", "type": "image/png", "size": 423125}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "ella_app::0", "time": {"start": 1754453958584, "stop": 1754453960024, "duration": 1440}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_redial"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_redial"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "bee0110354c87879", "status": "failed", "statusDetails": "AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', '没有通话记录', '', '']'\nassert False", "time": {"start": 1754403948451, "stop": 1754403970128, "duration": 21677}}], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": ["smoke"]}, "source": "ef7a049e326a2e58.json", "parameterValues": []}