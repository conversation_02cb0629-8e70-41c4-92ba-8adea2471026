{"uid": "f137a3f44d2dcec2", "name": "测试give me some money能正常执行", "fullName": "testcases.test_ella.dialogue.test_give_me_some_money.TestEllaGiveMeSomeMoney#test_give_me_some_money", "historyId": "2060dd1cfd03194548c0456a10798266", "time": {"start": 1754447836770, "stop": 1754447853314, "duration": 16544}, "description": "give me some money", "descriptionHtml": "<p>give me some money</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447824105, "stop": 1754447836769, "duration": 12664}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447836769, "stop": 1754447836769, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "give me some money", "status": "passed", "steps": [{"name": "执行命令: give me some money", "time": {"start": 1754447836770, "stop": 1754447853112, "duration": 16342}, "status": "passed", "steps": [{"name": "执行命令: give me some money", "time": {"start": 1754447836770, "stop": 1754447852914, "duration": 16144}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447852914, "stop": 1754447853111, "duration": 197}, "status": "passed", "steps": [], "attachments": [{"uid": "8658b9c2601512e6", "name": "测试总结", "source": "8658b9c2601512e6.txt", "type": "text/plain", "size": 185}, {"uid": "79a36d601fb9c0ed", "name": "test_completed", "source": "79a36d601fb9c0ed.png", "type": "image/png", "size": 566082}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447853112, "stop": 1754447853113, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447853113, "stop": 1754447853313, "duration": 200}, "status": "passed", "steps": [], "attachments": [{"uid": "dd7f74b81b305182", "name": "测试总结", "source": "dd7f74b81b305182.txt", "type": "text/plain", "size": 185}, {"uid": "b9db0682135fd712", "name": "test_completed", "source": "b9db0682135fd712.png", "type": "image/png", "size": 566076}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "2cb860234a6da46b", "name": "stdout", "source": "2cb860234a6da46b.txt", "type": "text/plain", "size": 11451}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447853314, "stop": 1754447853314, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447853315, "stop": 1754447854703, "duration": 1388}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_give_me_some_money"}, {"name": "subSuite", "value": "TestEllaGiveMeSomeMoney"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_give_me_some_money"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "3798453d42ddef7a", "status": "passed", "time": {"start": 1754398101857, "stop": 1754398116736, "duration": 14879}}], "categories": [], "tags": ["smoke"]}, "source": "f137a3f44d2dcec2.json", "parameterValues": []}