{"uid": "f2f0addf6616c69", "name": "测试pause music能正常执行", "fullName": "testcases.test_ella.component_coupling.test_pause_music.TestEllaPauseMusic#test_pause_music", "historyId": "464c8ea2a15f7ff86b8e0a347a821945", "time": {"start": 1754447097680, "stop": 1754447111256, "duration": 13576}, "description": "pause music", "descriptionHtml": "<p>pause music</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754447085206, "stop": 1754447097679, "duration": 12473}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754447097680, "stop": 1754447097680, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "pause music", "status": "passed", "steps": [{"name": "执行命令: pause music", "time": {"start": 1754447097680, "stop": 1754447111071, "duration": 13391}, "status": "passed", "steps": [{"name": "执行命令: pause music", "time": {"start": 1754447097680, "stop": 1754447110857, "duration": 13177}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447110857, "stop": 1754447111071, "duration": 214}, "status": "passed", "steps": [], "attachments": [{"uid": "f9a114a5131c3452", "name": "测试总结", "source": "f9a114a5131c3452.txt", "type": "text/plain", "size": 179}, {"uid": "5759c5ca27bc141b", "name": "test_completed", "source": "5759c5ca27bc141b.png", "type": "image/png", "size": 580675}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754447111071, "stop": 1754447111072, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754447111072, "stop": 1754447111255, "duration": 183}, "status": "passed", "steps": [], "attachments": [{"uid": "7d3d0f7011d10635", "name": "测试总结", "source": "7d3d0f7011d10635.txt", "type": "text/plain", "size": 179}, {"uid": "18972274876cbed5", "name": "test_completed", "source": "18972274876cbed5.png", "type": "image/png", "size": 580038}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "70d970fb26c19b9d", "name": "stdout", "source": "70d970fb26c19b9d.txt", "type": "text/plain", "size": 11411}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754447111256, "stop": 1754447111256, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754447111257, "stop": 1754447112621, "duration": 1364}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_pause_music"}, {"name": "subSuite", "value": "TestEllaPauseMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_pause_music"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "3209a336e36bcb0f", "status": "passed", "time": {"start": 1754397406124, "stop": 1754397419872, "duration": 13748}}], "categories": [], "tags": ["smoke"]}, "source": "f2f0addf6616c69.json", "parameterValues": []}