{"uid": "f42c4c84962569a", "name": "测试set parallel windows返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_parallel_windows.TestEllaSetParallelWindows#test_set_parallel_windows", "historyId": "eb142151b4b5ba4125a1a866dc2b58ef", "time": {"start": 1754454747179, "stop": 1754454761313, "duration": 14134}, "description": "验证set parallel windows指令返回预期的不支持响应", "descriptionHtml": "<p>验证set parallel windows指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454734437, "stop": 1754454747178, "duration": 12741}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454747178, "stop": 1754454747178, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set parallel windows指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set parallel windows", "time": {"start": 1754454747179, "stop": 1754454761122, "duration": 13943}, "status": "passed", "steps": [{"name": "执行命令: set parallel windows", "time": {"start": 1754454747179, "stop": 1754454760933, "duration": 13754}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454760933, "stop": 1754454761122, "duration": 189}, "status": "passed", "steps": [], "attachments": [{"uid": "b7681a73fce57698", "name": "测试总结", "source": "b7681a73fce57698.txt", "type": "text/plain", "size": 230}, {"uid": "a84999f31868f525", "name": "test_completed", "source": "a84999f31868f525.png", "type": "image/png", "size": 491918}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454761122, "stop": 1754454761124, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454761124, "stop": 1754454761313, "duration": 189}, "status": "passed", "steps": [], "attachments": [{"uid": "b037af739a818710", "name": "测试总结", "source": "b037af739a818710.txt", "type": "text/plain", "size": 230}, {"uid": "55e0e521083b73ec", "name": "test_completed", "source": "55e0e521083b73ec.png", "type": "image/png", "size": 490688}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "60e2bcdbdb7d52a5", "name": "stdout", "source": "60e2bcdbdb7d52a5.txt", "type": "text/plain", "size": 11312}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454761314, "stop": 1754454761314, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454761315, "stop": 1754454762623, "duration": 1308}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_parallel_windows"}, {"name": "subSuite", "value": "TestEllaSetParallelWindows"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_parallel_windows"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "f32acb276b2d0dad", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404758128, "stop": 1754404769200, "duration": 11072}}], "categories": [], "tags": ["smoke"]}, "source": "f42c4c84962569a.json", "parameterValues": []}