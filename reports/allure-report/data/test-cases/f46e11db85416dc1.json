{"uid": "f46e11db85416dc1", "name": "测试turn on the screen record能正常执行", "fullName": "testcases.test_ella.system_coupling.test_stop_recording.TestEllaTurnScreenRecord#test_turn_on_the_screen_record", "historyId": "ecfbca0f5d1122fac0e0543e38291ce2", "time": {"start": 1754450357435, "stop": 1754450375374, "duration": 17939}, "description": "turn on the screen record", "descriptionHtml": "<p>turn on the screen record</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450345052, "stop": 1754450357434, "duration": 12382}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450357434, "stop": 1754450357434, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "turn on the screen record", "status": "passed", "steps": [{"name": "执行命令: turn on the screen record", "time": {"start": 1754450357436, "stop": 1754450375188, "duration": 17752}, "status": "passed", "steps": [{"name": "执行命令: turn on the screen record", "time": {"start": 1754450357436, "stop": 1754450375000, "duration": 17564}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450375000, "stop": 1754450375187, "duration": 187}, "status": "passed", "steps": [], "attachments": [{"uid": "8bf27b03551bca97", "name": "测试总结", "source": "8bf27b03551bca97.txt", "type": "text/plain", "size": 198}, {"uid": "104f46a63165b394", "name": "test_completed", "source": "104f46a63165b394.png", "type": "image/png", "size": 649001}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450375188, "stop": 1754450375188, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754450375188, "stop": 1754450375188, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450375188, "stop": 1754450375372, "duration": 184}, "status": "passed", "steps": [], "attachments": [{"uid": "d94b2259c7594833", "name": "测试总结", "source": "d94b2259c7594833.txt", "type": "text/plain", "size": 198}, {"uid": "5b1df5d3e448d9a3", "name": "test_completed", "source": "5b1df5d3e448d9a3.png", "type": "image/png", "size": 649057}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "2ad6dbf1237fa9d", "name": "stdout", "source": "2ad6dbf1237fa9d.txt", "type": "text/plain", "size": 11730}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450375374, "stop": 1754450375374, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450375375, "stop": 1754450376845, "duration": 1470}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_stop_recording"}, {"name": "subSuite", "value": "TestEllaTurnScreenRecord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_stop_recording"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "75a0e9720c6254c6", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording started']\nassert False", "time": {"start": 1754400525767, "stop": 1754400542955, "duration": 17188}}], "categories": [], "tags": ["smoke"]}, "source": "f46e11db85416dc1.json", "parameterValues": []}