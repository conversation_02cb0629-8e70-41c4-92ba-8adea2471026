{"uid": "f5d2d3a4bb66a858", "name": "测试play video by youtube", "fullName": "testcases.test_ella.unsupported_commands.test_play_video_by_youtube.TestEllaOpenPlayPoliticalNews#test_play_political_news", "historyId": "209a33b821beee1291ce6952c4af7243", "time": {"start": 1754453876674, "stop": 1754453892534, "duration": 15860}, "description": "测试play video by youtube指令", "descriptionHtml": "<p>测试play video by youtube指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453863792, "stop": 1754453876672, "duration": 12880}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453876672, "stop": 1754453876673, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试play video by youtube指令", "status": "passed", "steps": [{"name": "执行命令: play video by youtube", "time": {"start": 1754453876674, "stop": 1754453892313, "duration": 15639}, "status": "passed", "steps": [{"name": "执行命令: play video by youtube", "time": {"start": 1754453876674, "stop": 1754453892083, "duration": 15409}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453892083, "stop": 1754453892313, "duration": 230}, "status": "passed", "steps": [], "attachments": [{"uid": "720e9e5d759d0a", "name": "测试总结", "source": "720e9e5d759d0a.txt", "type": "text/plain", "size": 221}, {"uid": "97843ed38f9f38f2", "name": "test_completed", "source": "97843ed38f9f38f2.png", "type": "image/png", "size": 424486}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754453892313, "stop": 1754453892314, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证youtube已打开", "time": {"start": 1754453892314, "stop": 1754453892314, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453892314, "stop": 1754453892534, "duration": 220}, "status": "passed", "steps": [], "attachments": [{"uid": "82c532a229747de7", "name": "测试总结", "source": "82c532a229747de7.txt", "type": "text/plain", "size": 221}, {"uid": "de07de4a932adf70", "name": "test_completed", "source": "de07de4a932adf70.png", "type": "image/png", "size": 423541}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "193f1c4b86e8f7d4", "name": "stdout", "source": "193f1c4b86e8f7d4.txt", "type": "text/plain", "size": 14580}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453892535, "stop": 1754453892535, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453892536, "stop": 1754453893915, "duration": 1379}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_play_video_by_youtube"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_play_video_by_youtube"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "5d6d2f27c1282d41", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754403890973, "stop": 1754403906319, "duration": 15346}}], "categories": [], "tags": ["smoke"]}, "source": "f5d2d3a4bb66a858.json", "parameterValues": []}