{"uid": "f5d600558ea29705", "name": "测试driving mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_driving_mode.TestEllaDrivingMode#test_driving_mode", "historyId": "3215de286c6ddd59d6e52a44f2a9967d", "time": {"start": 1754452528064, "stop": 1754452542007, "duration": 13943}, "description": "验证driving mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证driving mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452515824, "stop": 1754452528063, "duration": 12239}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452528063, "stop": 1754452528063, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证driving mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: driving mode", "time": {"start": 1754452528064, "stop": 1754452541833, "duration": 13769}, "status": "passed", "steps": [{"name": "执行命令: driving mode", "time": {"start": 1754452528064, "stop": 1754452541637, "duration": 13573}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452541637, "stop": 1754452541833, "duration": 196}, "status": "passed", "steps": [], "attachments": [{"uid": "96fc6b199da5300c", "name": "测试总结", "source": "96fc6b199da5300c.txt", "type": "text/plain", "size": 214}, {"uid": "9983f21f84adedf1", "name": "test_completed", "source": "9983f21f84adedf1.png", "type": "image/png", "size": 488597}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452541833, "stop": 1754452541834, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452541834, "stop": 1754452542006, "duration": 172}, "status": "passed", "steps": [], "attachments": [{"uid": "6057d3452d8917e4", "name": "测试总结", "source": "6057d3452d8917e4.txt", "type": "text/plain", "size": 214}, {"uid": "fab96caded2eddb", "name": "test_completed", "source": "fab96caded2eddb.png", "type": "image/png", "size": 488603}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "721cbf502dd8f9d1", "name": "stdout", "source": "721cbf502dd8f9d1.txt", "type": "text/plain", "size": 11235}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452542007, "stop": 1754452542007, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452542008, "stop": 1754452543338, "duration": 1330}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_driving_mode"}, {"name": "subSuite", "value": "TestEllaDrivingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_driving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "1b4e03bbf1fbfe63", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402557388, "stop": 1754402568688, "duration": 11300}}], "categories": [], "tags": ["smoke"]}, "source": "f5d600558ea29705.json", "parameterValues": []}