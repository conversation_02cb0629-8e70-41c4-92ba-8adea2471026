{"uid": "f60123fd513b8334", "name": "测试global gdp trends能正常执行", "fullName": "testcases.test_ella.dialogue.test_global_gdp_trends.TestEllaGlobalGdpTrends#test_global_gdp_trends", "historyId": "dee08db8cb0f1293bf864f56326992d5", "time": {"start": 1754398131113, "stop": 1754398158728, "duration": 27615}, "description": "global gdp trends", "descriptionHtml": "<p>global gdp trends</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754396776203, "stop": 1754396776404, "duration": 201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754398118024, "stop": 1754398131111, "duration": 13087}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754398131111, "stop": 1754398131111, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "global gdp trends", "status": "passed", "steps": [{"name": "执行命令: global gdp trends", "time": {"start": 1754398131113, "stop": 1754398158332, "duration": 27219}, "status": "passed", "steps": [{"name": "执行命令: global gdp trends", "time": {"start": 1754398131113, "stop": 1754398158005, "duration": 26892}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398158005, "stop": 1754398158330, "duration": 325}, "status": "passed", "steps": [], "attachments": [{"uid": "18a398bfc07ccb6", "name": "测试总结", "source": "18a398bfc07ccb6.txt", "type": "text/plain", "size": 982}, {"uid": "f4719ee3da26ced4", "name": "test_completed", "source": "f4719ee3da26ced4.png", "type": "image/png", "size": 746238}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754398158332, "stop": 1754398158337, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754398158337, "stop": 1754398158727, "duration": 390}, "status": "passed", "steps": [], "attachments": [{"uid": "e7010db051272643", "name": "测试总结", "source": "e7010db051272643.txt", "type": "text/plain", "size": 982}, {"uid": "b78ed183a0d9dbfa", "name": "test_completed", "source": "b78ed183a0d9dbfa.png", "type": "image/png", "size": 746238}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "63d062e304d7f0db", "name": "stdout", "source": "63d062e304d7f0db.txt", "type": "text/plain", "size": 15161}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754398158730, "stop": 1754398158730, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754398158732, "stop": 1754398160037, "duration": 1305}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754405665365, "stop": 1754405665369, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_global_gdp_trends"}, {"name": "subSuite", "value": "TestEllaGlobalGdpTrends"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "37684-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_global_gdp_trends"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke"]}, "source": "f60123fd513b8334.json", "parameterValues": []}