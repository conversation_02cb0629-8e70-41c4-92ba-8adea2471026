{"uid": "f674c997733d4a85", "name": "测试set call back with last used sim返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_call_back_with_last_used_sim.TestEllaSetCallBackLastUsedSim#test_set_call_back_with_last_used_sim", "historyId": "d45827de1782723ad9f8cd9d38f067dc", "time": {"start": 1754454265047, "stop": 1754454287795, "duration": 22748}, "description": "验证set call back with last used sim指令返回预期的不支持响应", "descriptionHtml": "<p>验证set call back with last used sim指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754454252403, "stop": 1754454265046, "duration": 12643}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754454265046, "stop": 1754454265046, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set call back with last used sim指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set call back with last used sim", "time": {"start": 1754454265047, "stop": 1754454287624, "duration": 22577}, "status": "passed", "steps": [{"name": "执行命令: set call back with last used sim", "time": {"start": 1754454265047, "stop": 1754454287382, "duration": 22335}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454287382, "stop": 1754454287624, "duration": 242}, "status": "passed", "steps": [], "attachments": [{"uid": "4079bd980b7900b1", "name": "测试总结", "source": "4079bd980b7900b1.txt", "type": "text/plain", "size": 268}, {"uid": "ffac680da8a8b90", "name": "test_completed", "source": "ffac680da8a8b90.png", "type": "image/png", "size": 513935}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754454287624, "stop": 1754454287625, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754454287625, "stop": 1754454287795, "duration": 170}, "status": "passed", "steps": [], "attachments": [{"uid": "88bc53d9100ac2a0", "name": "测试总结", "source": "88bc53d9100ac2a0.txt", "type": "text/plain", "size": 268}, {"uid": "7495108e11a608b9", "name": "test_completed", "source": "7495108e11a608b9.png", "type": "image/png", "size": 512815}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "184ebf7fec07801a", "name": "stdout", "source": "184ebf7fec07801a.txt", "type": "text/plain", "size": 11869}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754454287796, "stop": 1754454287796, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754454287797, "stop": 1754454289210, "duration": 1413}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_call_back_with_last_used_sim"}, {"name": "subSuite", "value": "TestEllaSetCallBackLastUsedSim"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_call_back_with_last_used_sim"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "a5310af2de506bcb", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754404274451, "stop": 1754404294889, "duration": 20438}}], "categories": [], "tags": ["smoke"]}, "source": "f674c997733d4a85.json", "parameterValues": []}