{"uid": "f6dd481761067622", "name": "测试turn on the flashlight能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_the_flashlight.TestEllaTurnFlashlight#test_turn_on_the_flashlight", "historyId": "8c5a5747e91f2cb0412111d5027bb7ec", "time": {"start": 1754451117198, "stop": 1754451133008, "duration": 15810}, "description": "turn on the flashlight", "descriptionHtml": "<p>turn on the flashlight</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451104864, "stop": 1754451117197, "duration": 12333}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451117197, "stop": 1754451117197, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "turn on the flashlight", "status": "passed", "steps": [{"name": "执行命令: turn on the flashlight", "time": {"start": 1754451117198, "stop": 1754451132826, "duration": 15628}, "status": "passed", "steps": [{"name": "执行命令: turn on the flashlight", "time": {"start": 1754451117198, "stop": 1754451132615, "duration": 15417}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451132615, "stop": 1754451132826, "duration": 211}, "status": "passed", "steps": [], "attachments": [{"uid": "ede6a87dec8e3216", "name": "测试总结", "source": "ede6a87dec8e3216.txt", "type": "text/plain", "size": 205}, {"uid": "7293e3acd4e45415", "name": "test_completed", "source": "7293e3acd4e45415.png", "type": "image/png", "size": 400898}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451132826, "stop": 1754451132827, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证应用已打开", "time": {"start": 1754451132827, "stop": 1754451132827, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451132827, "stop": 1754451133008, "duration": 181}, "status": "passed", "steps": [], "attachments": [{"uid": "674dc2885a8a8ee6", "name": "测试总结", "source": "674dc2885a8a8ee6.txt", "type": "text/plain", "size": 205}, {"uid": "eee9f13be65a92b5", "name": "test_completed", "source": "eee9f13be65a92b5.png", "type": "image/png", "size": 400804}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3b97ee51ed4b6aca", "name": "stdout", "source": "3b97ee51ed4b6aca.txt", "type": "text/plain", "size": 11922}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451133009, "stop": 1754451133009, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451133010, "stop": 1754451134384, "duration": 1374}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_the_flashlight"}, {"name": "subSuite", "value": "TestEllaTurnFlashlight"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_the_flashlight"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "b731c6d30a5edcab", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Flashlight is turned on now']\nassert False", "time": {"start": 1754401277802, "stop": 1754401291384, "duration": 13582}}], "categories": [], "tags": ["smoke"]}, "source": "f6dd481761067622.json", "parameterValues": []}