{"uid": "f6f7df8d1ba4a4a1", "name": "测试enable zonetouch master返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_zonetouch_master.TestEllaEnableZonetouchMaster#test_enable_zonetouch_master", "historyId": "7a670647c2336e6a5a5d07824fe89da6", "time": {"start": 1754452849649, "stop": 1754452863794, "duration": 14145}, "description": "验证enable zonetouch master指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable zonetouch master指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754452837648, "stop": 1754452849648, "duration": 12000}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754452849649, "stop": 1754452849649, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证enable zonetouch master指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable zonetouch master", "time": {"start": 1754452849649, "stop": 1754452863599, "duration": 13950}, "status": "passed", "steps": [{"name": "执行命令: enable zonetouch master", "time": {"start": 1754452849649, "stop": 1754452863425, "duration": 13776}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452863425, "stop": 1754452863599, "duration": 174}, "status": "passed", "steps": [], "attachments": [{"uid": "c3c19a187673d29a", "name": "测试总结", "source": "c3c19a187673d29a.txt", "type": "text/plain", "size": 237}, {"uid": "593f3d482bcabc51", "name": "test_completed", "source": "593f3d482bcabc51.png", "type": "image/png", "size": 459344}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754452863599, "stop": 1754452863600, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754452863600, "stop": 1754452863793, "duration": 193}, "status": "passed", "steps": [], "attachments": [{"uid": "b33b3395df4f16e", "name": "测试总结", "source": "b33b3395df4f16e.txt", "type": "text/plain", "size": 237}, {"uid": "ee963756d43c6d5e", "name": "test_completed", "source": "ee963756d43c6d5e.png", "type": "image/png", "size": 458076}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "19495f13129c45c3", "name": "stdout", "source": "19495f13129c45c3.txt", "type": "text/plain", "size": 11348}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754452863794, "stop": 1754452863794, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754452863795, "stop": 1754452865171, "duration": 1376}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_zonetouch_master"}, {"name": "subSuite", "value": "TestEllaEnableZonetouchMaster"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_zonetouch_master"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "274816f91f59cf0", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754402880051, "stop": 1754402891603, "duration": 11552}}], "categories": [], "tags": ["smoke"]}, "source": "f6f7df8d1ba4a4a1.json", "parameterValues": []}