{"uid": "f883a50d936e4a49", "name": "测试open wifi", "fullName": "testcases.test_ella.system_coupling.test_open_wifi.TestEllaOpenWifi#test_open_wifi", "historyId": "99709ca7d9951f6f7049b49ea81d0cd1", "time": {"start": 1754449978649, "stop": 1754449993380, "duration": 14731}, "description": "测试open wifi指令", "descriptionHtml": "<p>测试open wifi指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754449966043, "stop": 1754449978647, "duration": 12604}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754449978647, "stop": 1754449978647, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "测试open wifi指令", "status": "passed", "steps": [{"name": "执行命令: open wifi", "time": {"start": 1754449978649, "stop": 1754449993183, "duration": 14534}, "status": "passed", "steps": [{"name": "执行命令: open wifi", "time": {"start": 1754449978649, "stop": 1754449992988, "duration": 14339}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449992988, "stop": 1754449993183, "duration": 195}, "status": "passed", "steps": [], "attachments": [{"uid": "5fb6dc0880423f77", "name": "测试总结", "source": "5fb6dc0880423f77.txt", "type": "text/plain", "size": 169}, {"uid": "5ca2a9551fb8ceda", "name": "test_completed", "source": "5ca2a9551fb8ceda.png", "type": "image/png", "size": 499640}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754449993183, "stop": 1754449993184, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证wifi已打开", "time": {"start": 1754449993184, "stop": 1754449993184, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754449993184, "stop": 1754449993379, "duration": 195}, "status": "passed", "steps": [], "attachments": [{"uid": "b5bdb95e09d2abab", "name": "测试总结", "source": "b5bdb95e09d2abab.txt", "type": "text/plain", "size": 169}, {"uid": "da2924dc73b3fa2", "name": "test_completed", "source": "da2924dc73b3fa2.png", "type": "image/png", "size": 499488}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3547d79633c7001f", "name": "stdout", "source": "3547d79633c7001f.txt", "type": "text/plain", "size": 11678}], "parameters": [], "attachmentStep": false, "stepsCount": 6, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754449993380, "stop": 1754449993380, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754449993381, "stop": 1754449994748, "duration": 1367}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "打开应用"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_open_wifi"}, {"name": "subSuite", "value": "TestEllaOpenWifi"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_open_wifi"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "916fcb5f12a4c9fe", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Wi-<PERSON> is turned on now']\nassert False", "time": {"start": 1754400153609, "stop": 1754400166131, "duration": 12522}}], "categories": [], "tags": ["smoke"]}, "source": "f883a50d936e4a49.json", "parameterValues": []}