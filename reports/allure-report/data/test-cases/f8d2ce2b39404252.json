{"uid": "f8d2ce2b39404252", "name": "测试how's the weather today?返回正确的不支持响应", "fullName": "testcases.test_ella.dialogue.test_how_s_the_weather_today.TestEllaHowSWeatherToday#test_how_s_the_weather_today", "historyId": "f2f6762c5ec83e110ace25b47e3112d5", "time": {"start": 1754448046663, "stop": 1754448067498, "duration": 20835}, "description": "验证how's the weather today?指令返回预期的不支持响应", "descriptionHtml": "<p>验证how's the weather today?指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448033860, "stop": 1754448046662, "duration": 12802}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448046663, "stop": 1754448046663, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证how's the weather today?指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: how's the weather today?", "time": {"start": 1754448046664, "stop": 1754448067303, "duration": 20639}, "status": "passed", "steps": [{"name": "执行命令: how's the weather today?", "time": {"start": 1754448046664, "stop": 1754448067075, "duration": 20411}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448067075, "stop": 1754448067303, "duration": 228}, "status": "passed", "steps": [], "attachments": [{"uid": "5ddf14d4807a21f2", "name": "测试总结", "source": "5ddf14d4807a21f2.txt", "type": "text/plain", "size": 264}, {"uid": "656b4fe548c1aeab", "name": "test_completed", "source": "656b4fe548c1aeab.png", "type": "image/png", "size": 561659}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754448067303, "stop": 1754448067304, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448067305, "stop": 1754448067497, "duration": 192}, "status": "passed", "steps": [], "attachments": [{"uid": "bb907fff187542d6", "name": "测试总结", "source": "bb907fff187542d6.txt", "type": "text/plain", "size": 264}, {"uid": "9c8a90a7001f88d7", "name": "test_completed", "source": "9c8a90a7001f88d7.png", "type": "image/png", "size": 562380}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "7c47bf4759b4c3ca", "name": "stdout", "source": "7c47bf4759b4c3ca.txt", "type": "text/plain", "size": 11959}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448067498, "stop": 1754448067498, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448067500, "stop": 1754448068978, "duration": 1478}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_s_the_weather_today"}, {"name": "subSuite", "value": "TestEllaHowSWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_s_the_weather_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "8251282b801233c8", "status": "passed", "time": {"start": 1754398318149, "stop": 1754398338348, "duration": 20199}}], "categories": [], "tags": ["smoke"]}, "source": "f8d2ce2b39404252.json", "parameterValues": []}