{"uid": "f9f860dc66e4a117", "name": "测试Switch Magic Voice to Grace能正常执行", "fullName": "testcases.test_ella.system_coupling.test_switch_magic_voice_to_grace.TestEllaSwitchMagicVoiceGrace#test_switch_magic_voice_to_grace", "historyId": "04cef4934fe29e90ca0248af7b395794", "time": {"start": 1754450445439, "stop": 1754450459010, "duration": 13571}, "description": "Switch Magic Voice to Grace", "descriptionHtml": "<p><PERSON>witch Magic Voice to Grace</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754450432790, "stop": 1754450445437, "duration": 12647}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754450445438, "stop": 1754450445438, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "Switch Magic Voice to Grace", "status": "passed", "steps": [{"name": "执行命令: Switch Magic Voice to Grace", "time": {"start": 1754450445439, "stop": 1754450458790, "duration": 13351}, "status": "passed", "steps": [{"name": "执行命令: Switch Magic Voice to Grace", "time": {"start": 1754450445439, "stop": 1754450458571, "duration": 13132}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450458571, "stop": 1754450458790, "duration": 219}, "status": "passed", "steps": [], "attachments": [{"uid": "3b5119966706fe92", "name": "测试总结", "source": "3b5119966706fe92.txt", "type": "text/plain", "size": 268}, {"uid": "62785f26175ba577", "name": "test_completed", "source": "62785f26175ba577.png", "type": "image/png", "size": 579586}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754450458790, "stop": 1754450458792, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754450458792, "stop": 1754450459010, "duration": 218}, "status": "passed", "steps": [], "attachments": [{"uid": "9cd6b09d4137d26b", "name": "测试总结", "source": "9cd6b09d4137d26b.txt", "type": "text/plain", "size": 268}, {"uid": "ec180f9626270980", "name": "test_completed", "source": "ec180f9626270980.png", "type": "image/png", "size": 578872}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "58402e4c12247f00", "name": "stdout", "source": "58402e4c12247f00.txt", "type": "text/plain", "size": 11797}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754450459011, "stop": 1754450459011, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754450459012, "stop": 1754450460436, "duration": 1424}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_magic_voice_to_grace"}, {"name": "subSuite", "value": "TestEllaSwitchMagicVoiceGrace"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_magic_voice_to_grace"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "e62b49bee8a5de1d", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['This voice is currently unavailable', 'Here are some alternatives', 'Please select one you like']\nassert False", "time": {"start": 1754400615179, "stop": 1754400626214, "duration": 11035}}], "categories": [], "tags": ["smoke"]}, "source": "f9f860dc66e4a117.json", "parameterValues": []}