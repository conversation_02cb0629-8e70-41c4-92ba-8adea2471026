{"uid": "fe566ec100eab021", "name": "测试set ultra power saving返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_ultra_power_saving.TestEllaSetUltraPowerSaving#test_set_ultra_power_saving", "historyId": "71c64c122f83e6fd138db517bbda4aef", "time": {"start": 1754455156956, "stop": 1754455171944, "duration": 14988}, "description": "验证set ultra power saving指令返回预期的不支持响应", "descriptionHtml": "<p>验证set ultra power saving指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754455145362, "stop": 1754455156955, "duration": 11593}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754455156956, "stop": 1754455156956, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set ultra power saving指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set ultra power saving", "time": {"start": 1754455156956, "stop": 1754455171753, "duration": 14797}, "status": "passed", "steps": [{"name": "执行命令: set ultra power saving", "time": {"start": 1754455156956, "stop": 1754455171565, "duration": 14609}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455171565, "stop": 1754455171753, "duration": 188}, "status": "passed", "steps": [], "attachments": [{"uid": "f69973baa090a01d", "name": "测试总结", "source": "f69973baa090a01d.txt", "type": "text/plain", "size": 240}, {"uid": "6782e49828a88b05", "name": "test_completed", "source": "6782e49828a88b05.png", "type": "image/png", "size": 490589}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754455171753, "stop": 1754455171754, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754455171754, "stop": 1754455171944, "duration": 190}, "status": "passed", "steps": [], "attachments": [{"uid": "7b6b84c8ae161537", "name": "测试总结", "source": "7b6b84c8ae161537.txt", "type": "text/plain", "size": 240}, {"uid": "88bfc29476b47431", "name": "test_completed", "source": "88bfc29476b47431.png", "type": "image/png", "size": 490593}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "e43d812af47e5ff0", "name": "stdout", "source": "e43d812af47e5ff0.txt", "type": "text/plain", "size": 11880}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754455171945, "stop": 1754455171945, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754455171947, "stop": 1754455173302, "duration": 1355}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_ultra_power_saving"}, {"name": "subSuite", "value": "TestEllaSetUltraPowerSaving"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_ultra_power_saving"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "ff5b7c2d1d514320", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754405190396, "stop": 1754405201735, "duration": 11339}}], "categories": [], "tags": ["smoke"]}, "source": "fe566ec100eab021.json", "parameterValues": []}