{"uid": "fe60a039ac946371", "name": "stop  screen recording能正常执行", "fullName": "testcases.test_ella.system_coupling.test_turn_on_the_screen_record.TestEllaTurnScreenRecord#test_stop_screen_recording", "historyId": "ebd9dc4871a5e78e68997fd53d4e9d06", "time": {"start": 1754451179149, "stop": 1754451198065, "duration": 18916}, "description": "stop  screen recording", "descriptionHtml": "<p>stop  screen recording</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754451166232, "stop": 1754451179148, "duration": 12916}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754451179148, "stop": 1754451179148, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "stop  screen recording", "status": "passed", "steps": [{"name": "执行命令: stop screen recording", "time": {"start": 1754451179149, "stop": 1754451197872, "duration": 18723}, "status": "passed", "steps": [{"name": "执行命令: stop screen recording", "time": {"start": 1754451179149, "stop": 1754451197677, "duration": 18528}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451197677, "stop": 1754451197871, "duration": 194}, "status": "passed", "steps": [], "attachments": [{"uid": "fd7673f89d3704f5", "name": "测试总结", "source": "fd7673f89d3704f5.txt", "type": "text/plain", "size": 191}, {"uid": "e087de77d9fdff35", "name": "test_completed", "source": "e087de77d9fdff35.png", "type": "image/png", "size": 464309}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754451197872, "stop": 1754451197872, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证已打开", "time": {"start": 1754451197872, "stop": 1754451197872, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "验证文件存在", "time": {"start": 1754451197872, "stop": 1754451197872, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754451197872, "stop": 1754451198064, "duration": 192}, "status": "passed", "steps": [], "attachments": [{"uid": "f56be49381c773f9", "name": "测试总结", "source": "f56be49381c773f9.txt", "type": "text/plain", "size": 191}, {"uid": "b8912b34afabaa73", "name": "test_completed", "source": "b8912b34afabaa73.png", "type": "image/png", "size": 464309}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "cb5e7b4fadaa82e5", "name": "stdout", "source": "cb5e7b4fadaa82e5.txt", "type": "text/plain", "size": 12797}], "parameters": [], "attachmentStep": false, "stepsCount": 7, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754451198065, "stop": 1754451198065, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754451198066, "stop": 1754451199470, "duration": 1404}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_turn_on_the_screen_record"}, {"name": "subSuite", "value": "TestEllaTurnScreenRecord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_turn_on_the_screen_record"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "492c4e9d6bef3996", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording finished']\nassert False", "time": {"start": 1754401338276, "stop": 1754401354965, "duration": 16689}}], "categories": [], "tags": ["smoke"]}, "source": "fe60a039ac946371.json", "parameterValues": []}