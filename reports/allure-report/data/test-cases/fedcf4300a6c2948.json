{"uid": "fedcf4300a6c2948", "name": "测试how is the wheather today能正常执行", "fullName": "testcases.test_ella.dialogue.test_how_is_the_wheather_today.TestEllaHowIsWheatherToday#test_how_is_the_wheather_today", "historyId": "f7282303534c1c8599c3343608e6f453", "time": {"start": 1754448018652, "stop": 1754448032473, "duration": 13821}, "description": "how is the wheather today", "descriptionHtml": "<p>how is the wheather today</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754448006022, "stop": 1754448018651, "duration": 12629}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754448018651, "stop": 1754448018651, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "how is the wheather today", "status": "passed", "steps": [{"name": "执行命令: how is the wheather today", "time": {"start": 1754448018652, "stop": 1754448032276, "duration": 13624}, "status": "passed", "steps": [{"name": "执行命令: how is the wheather today", "time": {"start": 1754448018652, "stop": 1754448032092, "duration": 13440}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448032092, "stop": 1754448032276, "duration": 184}, "status": "passed", "steps": [], "attachments": [{"uid": "4fb38f0edec2bc08", "name": "测试总结", "source": "4fb38f0edec2bc08.txt", "type": "text/plain", "size": 264}, {"uid": "1e42c41aa160e055", "name": "test_completed", "source": "1e42c41aa160e055.png", "type": "image/png", "size": 546767}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望内容", "time": {"start": 1754448032276, "stop": 1754448032280, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754448032280, "stop": 1754448032472, "duration": 192}, "status": "passed", "steps": [], "attachments": [{"uid": "255e78973bf23db8", "name": "测试总结", "source": "255e78973bf23db8.txt", "type": "text/plain", "size": 264}, {"uid": "5c68ce5cc56413ca", "name": "test_completed", "source": "5c68ce5cc56413ca.png", "type": "image/png", "size": 546812}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3b1998c2190a0033", "name": "stdout", "source": "3b1998c2190a0033.txt", "type": "text/plain", "size": 11995}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754448032473, "stop": 1754448032473, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754448032474, "stop": 1754448033854, "duration": 1380}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "第三方集成"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_is_the_wheather_today"}, {"name": "subSuite", "value": "TestEllaHowIsWheatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_is_the_wheather_today"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [{"uid": "50ef96927d3bd86f", "status": "passed", "time": {"start": 1754398290921, "stop": 1754398303892, "duration": 12971}}], "categories": [], "tags": ["smoke"]}, "source": "fedcf4300a6c2948.json", "parameterValues": []}