{"uid": "fee8f542d5d430f2", "name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_lock_screen_notification_and_display_settings.TestEllaOpenSettings#test_jump_to_lock_screen_notification_and_display_settings", "historyId": "fca782bf64e9cf595a09003471d4cc31", "time": {"start": 1754453325062, "stop": 1754453348609, "duration": 23547}, "description": "验证jump to lock screen notification and display settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to lock screen notification and display settings指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1754446461770, "stop": 1754446462171, "duration": 401}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1754453312894, "stop": 1754453325061, "duration": 12167}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1754453325061, "stop": 1754453325061, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证jump to lock screen notification and display settings指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: jump to lock screen notification and display settings", "time": {"start": 1754453325062, "stop": 1754453348429, "duration": 23367}, "status": "passed", "steps": [{"name": "执行命令: jump to lock screen notification and display settings", "time": {"start": 1754453325062, "stop": 1754453348220, "duration": 23158}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453348220, "stop": 1754453348429, "duration": 209}, "status": "passed", "steps": [], "attachments": [{"uid": "f028954f4e127454", "name": "测试总结", "source": "f028954f4e127454.txt", "type": "text/plain", "size": 323}, {"uid": "e2d388b9007f4d1c", "name": "test_completed", "source": "e2d388b9007f4d1c.png", "type": "image/png", "size": 551189}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 2, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1754453348429, "stop": 1754453348431, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1754453348431, "stop": 1754453348609, "duration": 178}, "status": "passed", "steps": [], "attachments": [{"uid": "df60d13128a2ece9", "name": "测试总结", "source": "df60d13128a2ece9.txt", "type": "text/plain", "size": 323}, {"uid": "da3628fbbdd85455", "name": "test_completed", "source": "da3628fbbdd85455.png", "type": "image/png", "size": 551055}], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "6157e46defb6e133", "name": "stdout", "source": "6157e46defb6e133.txt", "type": "text/plain", "size": 12085}], "parameters": [], "attachmentStep": false, "stepsCount": 5, "hasContent": true, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1754453348610, "stop": 1754453348610, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1754453348611, "stop": 1754453349935, "duration": 1324}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1754455627767, "stop": 1754455627769, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentStep": false, "stepsCount": 0, "hasContent": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_lock_screen_notification_and_display_settings"}, {"name": "subSuite", "value": "TestEllaOpenSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "64860-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_lock_screen_notification_and_display_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "19588de64ce7a6b5", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754403344699, "stop": 1754403364901, "duration": 20202}}], "categories": [], "tags": ["smoke"]}, "source": "fee8f542d5d430f2.json", "parameterValues": []}