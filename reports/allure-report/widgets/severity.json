[{"uid": "6dbcf1976884cdf8", "name": "测试set my fonts返回正确的不支持响应", "time": {"start": 1753766062593, "stop": 1753766071390, "duration": 8797}, "status": "passed", "severity": "normal"}, {"uid": "b9ad5f0890d7ff95", "name": "测试switch to performance mode返回正确的不支持响应", "time": {"start": 1753766497236, "stop": 1753766505493, "duration": 8257}, "status": "passed", "severity": "normal"}, {"uid": "4566d78d4a8b1e24", "name": "测试reset phone返回正确的不支持响应", "time": {"start": 1753765618958, "stop": 1753765632284, "duration": 13326}, "status": "passed", "severity": "normal"}, {"uid": "f72ab1da7fdcbd71", "name": "测试set phone number返回正确的不支持响应", "time": {"start": 1753766175014, "stop": 1753766188180, "duration": 13166}, "status": "passed", "severity": "normal"}, {"uid": "a934d12461417644", "name": "测试set app notifications返回正确的不支持响应", "time": {"start": 1753765668208, "stop": 1753765677125, "duration": 8917}, "status": "passed", "severity": "normal"}, {"uid": "2d67f42529f4ba11", "name": "测试set font size返回正确的不支持响应", "time": {"start": 1753765967757, "stop": 1753765976842, "duration": 9085}, "status": "passed", "severity": "normal"}, {"uid": "d1f215605f21a61a", "name": "测试set floating windows返回正确的不支持响应", "time": {"start": 1753765923195, "stop": 1753765931855, "duration": 8660}, "status": "passed", "severity": "normal"}, {"uid": "5e296627e553497f", "name": "测试turn off driving mode返回正确的不支持响应", "time": {"start": 1753766586941, "stop": 1753766595841, "duration": 8900}, "status": "passed", "severity": "normal"}, {"uid": "ca8b062a81e447d0", "name": "测试set gesture navigation返回正确的不支持响应", "time": {"start": 1753765990851, "stop": 1753766003692, "duration": 12841}, "status": "passed", "severity": "normal"}, {"uid": "f12e7811277e974e", "name": "测试enable all ai magic box features返回正确的不支持响应", "time": {"start": 1753764959912, "stop": 1753764968912, "duration": 9000}, "status": "passed", "severity": "normal"}, {"uid": "7d7e356db129b7a7", "name": "测试disable all ai magic box features返回正确的不支持响应", "time": {"start": 1753761812917, "stop": 1753761822063, "duration": 9146}, "status": "passed", "severity": "normal"}, {"uid": "c2c1ab04ba6a67df", "name": "测试disable auto pickup返回正确的不支持响应", "time": {"start": 1753761835778, "stop": 1753761844202, "duration": 8424}, "status": "passed", "severity": "normal"}, {"uid": "17d22617af07582d", "name": "测试turn off show battery percentage返回正确的不支持响应", "time": {"start": 1753766609664, "stop": 1753766618172, "duration": 8508}, "status": "passed", "severity": "normal"}, {"uid": "ceced2591885abc6", "name": "测试jump to high brightness mode settings返回正确的不支持响应", "time": {"start": 1753765417843, "stop": 1753765430253, "duration": 12410}, "status": "passed", "severity": "normal"}, {"uid": "771aecbdf39fcdf1", "name": "测试disable unfreeze返回正确的不支持响应", "time": {"start": 1753764845156, "stop": 1753764854156, "duration": 9000}, "status": "passed", "severity": "normal"}, {"uid": "c2492372c4391305", "name": "测试disable hide notifications返回正确的不支持响应", "time": {"start": 1753761907303, "stop": 1753761916192, "duration": 8889}, "status": "passed", "severity": "normal"}, {"uid": "e03fdcb88dd5fa05", "name": "测试enable unfreeze返回正确的不支持响应", "time": {"start": 1753765147303, "stop": 1753765155709, "duration": 8406}, "status": "passed", "severity": "normal"}, {"uid": "2f290e7ec2fa59be", "name": "测试set flex-still mode返回正确的不支持响应", "time": {"start": 1753765878872, "stop": 1753765887490, "duration": 8618}, "status": "passed", "severity": "normal"}, {"uid": "a9ae4477c0b948a6", "name": "测试set screen relay返回正确的不支持响应", "time": {"start": 1753766247545, "stop": 1753766256691, "duration": 9146}, "status": "passed", "severity": "normal"}, {"uid": "afaa736411383736", "name": "测试set color style返回正确的不支持响应", "time": {"start": 1753765744290, "stop": 1753765752878, "duration": 8588}, "status": "passed", "severity": "normal"}, {"uid": "757593af1b0f4165", "name": "测试increase settings for special functions返回正确的不支持响应", "time": {"start": 1753765241506, "stop": 1753765253546, "duration": 12040}, "status": "passed", "severity": "normal"}, {"uid": "ed1dcfed11432025", "name": "测试enable running lock返回正确的不支持响应", "time": {"start": 1753765102958, "stop": 1753765111240, "duration": 8282}, "status": "passed", "severity": "normal"}, {"uid": "865d969ffdc78faa", "name": "测试Enable Call on Hold返回正确的不支持响应", "time": {"start": 1753765026272, "stop": 1753765039213, "duration": 12941}, "status": "passed", "severity": "normal"}, {"uid": "f8e5b04c258276eb", "name": "测试driving mode返回正确的不支持响应", "time": {"start": 1753764915935, "stop": 1753764924701, "duration": 8766}, "status": "passed", "severity": "normal"}, {"uid": "7373dd251d127d09", "name": "测试set lockscreen passwords返回正确的不支持响应", "time": {"start": 1753766040123, "stop": 1753766048747, "duration": 8624}, "status": "passed", "severity": "normal"}, {"uid": "ff43e38cf6639396", "name": "测试set call back with last used sim返回正确的不支持响应", "time": {"start": 1753765717082, "stop": 1753765730096, "duration": 13014}, "status": "passed", "severity": "normal"}, {"uid": "d872f1ccc0009a0b", "name": "测试disable running lock返回正确的不支持响应", "time": {"start": 1753764801669, "stop": 1753764810096, "duration": 8427}, "status": "passed", "severity": "normal"}, {"uid": "1b1585fb19777980", "name": "测试Voice setting page返回正确的不支持响应", "time": {"start": 1753766700113, "stop": 1753766712922, "duration": 12809}, "status": "passed", "severity": "normal"}, {"uid": "888dd68c71948402", "name": "测试download basketball返回正确的不支持响应", "time": {"start": 1753764889004, "stop": 1753764902578, "duration": 13574}, "status": "passed", "severity": "normal"}, {"uid": "4f630659388f04a8", "name": "测试set cover screen apps返回正确的不支持响应", "time": {"start": 1753765789622, "stop": 1753765797895, "duration": 8273}, "status": "passed", "severity": "normal"}, {"uid": "c7a4a9bb9cd3d2d1", "name": "测试set scheduled power on/off and restart返回正确的不支持响应", "time": {"start": 1753766202038, "stop": 1753766211140, "duration": 9102}, "status": "passed", "severity": "normal"}, {"uid": "75008c9c41959d3b", "name": "测试set my themes返回正确的不支持响应", "time": {"start": 1753766085120, "stop": 1753766093577, "duration": 8457}, "status": "passed", "severity": "normal"}, {"uid": "f5a5bd26d1bd1074", "name": "测试close performance mode返回正确的不支持响应", "time": {"start": 1753761746394, "stop": 1753761755312, "duration": 8918}, "status": "passed", "severity": "normal"}, {"uid": "bca06f3ba9fb151a", "name": "测试enable auto pickup返回正确的不支持响应", "time": {"start": 1753764982371, "stop": 1753764991328, "duration": 8957}, "status": "passed", "severity": "normal"}, {"uid": "eacf804ad7a8f2d5", "name": "测试enable accelerate dialogue返回正确的不支持响应", "time": {"start": 1753764937838, "stop": 1753764946850, "duration": 9012}, "status": "passed", "severity": "normal"}, {"uid": "98bb8c35dbf6e67f", "name": "测试set flip case feature返回正确的不支持响应", "time": {"start": 1753765901173, "stop": 1753765909427, "duration": 8254}, "status": "passed", "severity": "normal"}, {"uid": "8383bffd0b084967", "name": "测试close power saving mode返回正确的不支持响应", "time": {"start": 1753761768665, "stop": 1753761777338, "duration": 8673}, "status": "passed", "severity": "normal"}, {"uid": "80224d74cee84492", "name": "测试Enable Call Rejection返回正确的不支持响应", "time": {"start": 1753765053607, "stop": 1753765066572, "duration": 12965}, "status": "passed", "severity": "normal"}, {"uid": "4760d5acdc6b4ef1", "name": "测试enable zonetouch master返回正确的不支持响应", "time": {"start": 1753765170109, "stop": 1753765178909, "duration": 8800}, "status": "passed", "severity": "normal"}, {"uid": "2184d35880254919", "name": "测试disable magic voice changer返回正确的不支持响应", "time": {"start": 1753764758200, "stop": 1753764765984, "duration": 7784}, "status": "passed", "severity": "normal"}, {"uid": "f098baef018951df", "name": "测试set date & time返回正确的不支持响应", "time": {"start": 1753765833804, "stop": 1753765842611, "duration": 8807}, "status": "passed", "severity": "normal"}, {"uid": "33e1b9f38d32945b", "name": "测试set screen timeout返回正确的不支持响应", "time": {"start": 1753766270460, "stop": 1753766279132, "duration": 8672}, "status": "passed", "severity": "normal"}, {"uid": "cb7dced3ed5dce81", "name": "测试disable call rejection返回正确的不支持响应", "time": {"start": 1753761880191, "stop": 1753761893514, "duration": 13323}, "status": "passed", "severity": "normal"}, {"uid": "32fa194272e5fe43", "name": "测试jump to battery and power saving返回正确的不支持响应", "time": {"start": 1753765345752, "stop": 1753765354649, "duration": 8897}, "status": "passed", "severity": "normal"}, {"uid": "f7620d96e89fe416", "name": "测试open font family settings返回正确的不支持响应", "time": {"start": 1753765522695, "stop": 1753765534823, "duration": 12128}, "status": "passed", "severity": "normal"}, {"uid": "246bdbd532071352", "name": "测试turn on high brightness mode返回正确的不支持响应", "time": {"start": 1753766654111, "stop": 1753766662935, "duration": 8824}, "status": "passed", "severity": "normal"}, {"uid": "e3bf02bacdd2495b", "name": "测试disable accelerate dialogue返回正确的不支持响应", "time": {"start": 1753761790803, "stop": 1753761799572, "duration": 8769}, "status": "passed", "severity": "normal"}, {"uid": "144fa92bac6d7d3c", "name": "测试enable touch optimization返回正确的不支持响应", "time": {"start": 1753765125128, "stop": 1753765133836, "duration": 8708}, "status": "passed", "severity": "normal"}, {"uid": "7d3b9bd829e39087", "name": "测试disable network enhancement返回正确的不支持响应", "time": {"start": 1753764779947, "stop": 1753764788591, "duration": 8644}, "status": "passed", "severity": "normal"}, {"uid": "94d5be3d7b73138a", "name": "测试set split-screen apps返回正确的不支持响应", "time": {"start": 1753766405436, "stop": 1753766415096, "duration": 9660}, "status": "passed", "severity": "normal"}, {"uid": "820e6fda2bc7ebdc", "name": "测试enable brightness locking返回正确的不支持响应", "time": {"start": 1753765004516, "stop": 1753765012789, "duration": 8273}, "status": "passed", "severity": "normal"}, {"uid": "3265a62e2f50eb16", "name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "time": {"start": 1753765293770, "stop": 1753765305973, "duration": 12203}, "status": "passed", "severity": "normal"}, {"uid": "895239a3cabc886", "name": "测试open notification ringtone settings返回正确的不支持响应", "time": {"start": 1753765548511, "stop": 1753765560850, "duration": 12339}, "status": "passed", "severity": "normal"}, {"uid": "b790b402ce19d974", "name": "测试set edge mistouch prevention返回正确的不支持响应", "time": {"start": 1753765856158, "stop": 1753765864716, "duration": 8558}, "status": "passed", "severity": "normal"}, {"uid": "1b42bcf3479561c8", "name": "测试set screen refresh rate返回正确的不支持响应", "time": {"start": 1753766224733, "stop": 1753766233136, "duration": 8403}, "status": "passed", "severity": "normal"}, {"uid": "7498ca1bd63270a0", "name": "测试set phantom v pen返回正确的不支持响应", "time": {"start": 1753766152299, "stop": 1753766161117, "duration": 8818}, "status": "passed", "severity": "normal"}, {"uid": "881ccc6117d75d5b", "name": "测试set folding screen zone返回正确的不支持响应", "time": {"start": 1753765945477, "stop": 1753765953957, "duration": 8480}, "status": "passed", "severity": "normal"}, {"uid": "35097e6128df904c", "name": "测试set personal hotspot返回正确的不支持响应", "time": {"start": 1753766129315, "stop": 1753766138465, "duration": 9150}, "status": "passed", "severity": "normal"}, {"uid": "1aef1ccd7dac8693", "name": "测试set compatibility mode返回正确的不支持响应", "time": {"start": 1753765767158, "stop": 1753765775913, "duration": 8755}, "status": "passed", "severity": "normal"}, {"uid": "676b5a85a463de98", "name": "测试check my balance of sim1返回正确的不支持响应", "time": {"start": 1753761700135, "stop": 1753761709886, "duration": 9751}, "status": "passed", "severity": "normal"}, {"uid": "6d09de9067475c54", "name": "测试set battery saver settings返回正确的不支持响应", "time": {"start": 1753765690785, "stop": 1753765703376, "duration": 12591}, "status": "passed", "severity": "normal"}, {"uid": "feaafdb7aa1aa06", "name": "测试set parallel windows返回正确的不支持响应", "time": {"start": 1753766107022, "stop": 1753766115543, "duration": 8521}, "status": "passed", "severity": "normal"}, {"uid": "977a0552709ca7dd", "name": "测试close equilibrium mode返回正确的不支持响应", "time": {"start": 1753761723925, "stop": 1753761733203, "duration": 9278}, "status": "passed", "severity": "normal"}, {"uid": "8808f7aabaffe30e", "name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "time": {"start": 1753765443894, "stop": 1753765456708, "duration": 12814}, "status": "passed", "severity": "normal"}, {"uid": "1991f1f1174d50bb", "name": "测试yandex eats返回正确的不支持响应", "time": {"start": 1753766727520, "stop": 1753766736122, "duration": 8602}, "status": "passed", "severity": "normal"}, {"uid": "55306a20cf18accf", "name": "测试set app auto rotate返回正确的不支持响应", "time": {"start": 1753765645878, "stop": 1753765655018, "duration": 9140}, "status": "passed", "severity": "normal"}, {"uid": "a56d8e321dbdf739", "name": "测试check model information返回正确的不支持响应", "time": {"start": 1753761677631, "stop": 1753761686636, "duration": 9005}, "status": "passed", "severity": "normal"}, {"uid": "26de3ee7bd066adc", "name": "测试jump to adaptive brightness settings返回正确的不支持响应", "time": {"start": 1753765267520, "stop": 1753765280068, "duration": 12548}, "status": "passed", "severity": "normal"}, {"uid": "1d57845f41e6a5f8", "name": "测试set screen to minimum brightness返回正确的不支持响应", "time": {"start": 1753766292819, "stop": 1753766301429, "duration": 8610}, "status": "passed", "severity": "normal"}, {"uid": "2d689da05d191cd8", "name": "测试set smart hub返回正确的不支持响应", "time": {"start": 1753766337954, "stop": 1753766346724, "duration": 8770}, "status": "passed", "severity": "normal"}, {"uid": "8600fc1e69aa8275", "name": "测试order a burger返回正确的不支持响应", "time": {"start": 1753765574537, "stop": 1753765583369, "duration": 8832}, "status": "passed", "severity": "normal"}, {"uid": "dc061218f29f8e09", "name": "测试set sim1 ringtone返回正确的不支持响应", "time": {"start": 1753766315363, "stop": 1753766324154, "duration": 8791}, "status": "passed", "severity": "normal"}, {"uid": "e787218d61e83f91", "name": "测试set timezone返回正确的不支持响应", "time": {"start": 1753766428807, "stop": 1753766437224, "duration": 8417}, "status": "passed", "severity": "normal"}, {"uid": "ab574fd223ac3ccd", "name": "测试disable brightness locking返回正确的不支持响应", "time": {"start": 1753761857701, "stop": 1753761866488, "duration": 8787}, "status": "passed", "severity": "normal"}, {"uid": "2a28697021ec7d75", "name": "测试check mobile data balance of sim2返回正确的不支持响应", "time": {"start": 1753761655231, "stop": 1753761664149, "duration": 8918}, "status": "passed", "severity": "normal"}, {"uid": "9b68c7b52f218f6f", "name": "测试Enable Network Enhancement返回正确的不支持响应", "time": {"start": 1753765080707, "stop": 1753765088965, "duration": 8258}, "status": "passed", "severity": "normal"}, {"uid": "9f6d55c7b99aa986", "name": "测试order a takeaway返回正确的不支持响应", "time": {"start": 1753765597096, "stop": 1753765605451, "duration": 8355}, "status": "passed", "severity": "normal"}, {"uid": "b766f5a20f897743", "name": "测试check battery information返回正确的不支持响应", "time": {"start": 1753761631982, "stop": 1753761641998, "duration": 10016}, "status": "passed", "severity": "normal"}, {"uid": "523aa5be0a30d5f7", "name": "测试set languages返回正确的不支持响应", "time": {"start": 1753766017621, "stop": 1753766026454, "duration": 8833}, "status": "passed", "severity": "normal"}, {"uid": "ce8424deaa12d0d3", "name": "测试switch to equilibrium mode返回正确的不支持响应", "time": {"start": 1753766474322, "stop": 1753766483379, "duration": 9057}, "status": "passed", "severity": "normal"}, {"uid": "c3dca0ed7665226e", "name": "测试set ultra power saving返回正确的不支持响应", "time": {"start": 1753766451006, "stop": 1753766460089, "duration": 9083}, "status": "passed", "severity": "normal"}, {"uid": "c289979a8e3cbf4", "name": "测试set smart panel返回正确的不支持响应", "time": {"start": 1753766360497, "stop": 1753766369063, "duration": 8566}, "status": "passed", "severity": "normal"}, {"uid": "6666371aa4b73f76", "name": "测试turn on show battery percentage返回正确的不支持响应", "time": {"start": 1753766676863, "stop": 1753766685957, "duration": 9094}, "status": "passed", "severity": "normal"}, {"uid": "c145d2c6d0bdd37b", "name": "测试jump to auto rotate screen settings返回正确的不支持响应", "time": {"start": 1753765319585, "stop": 1753765332134, "duration": 12549}, "status": "passed", "severity": "normal"}, {"uid": "6f7ba473d5caa2ff", "name": "测试jump to battery usage返回正确的不支持响应", "time": {"start": 1753765368389, "stop": 1753765377128, "duration": 8739}, "status": "passed", "severity": "normal"}, {"uid": "fc74c441f7f7fac8", "name": "测试set special function返回正确的不支持响应", "time": {"start": 1753766382944, "stop": 1753766391498, "duration": 8554}, "status": "passed", "severity": "normal"}, {"uid": "c8ecf6e0dcefd2e3", "name": "测试how to set screenshots返回正确的不支持响应", "time": {"start": 1753765219280, "stop": 1753765227782, "duration": 8502}, "status": "passed", "severity": "normal"}, {"uid": "fd6f74a8afb4623b", "name": "测试jump to call notifications返回正确的不支持响应", "time": {"start": 1753765390724, "stop": 1753765403631, "duration": 12907}, "status": "passed", "severity": "normal"}, {"uid": "865b70c233569c0b", "name": "测试disable zonetouch master返回正确的不支持响应", "time": {"start": 1753764867517, "stop": 1753764875770, "duration": 8253}, "status": "passed", "severity": "normal"}, {"uid": "a3eadc9fba00cf71", "name": "测试how's the weather today?返回正确的不支持响应", "time": {"start": 1753765192515, "stop": 1753765205101, "duration": 12586}, "status": "failed", "severity": "normal"}, {"uid": "c297d430a8b59844", "name": "测试jump to notifications and status bar settings返回正确的不支持响应", "time": {"start": 1753765470565, "stop": 1753765483040, "duration": 12475}, "status": "passed", "severity": "normal"}, {"uid": "73d90eb159607760", "name": "测试disable touch optimization返回正确的不支持响应", "time": {"start": 1753764823176, "stop": 1753764831729, "duration": 8553}, "status": "passed", "severity": "normal"}, {"uid": "6854011c63695983", "name": "测试turn on driving mode返回正确的不支持响应", "time": {"start": 1753766631818, "stop": 1753766640135, "duration": 8317}, "status": "passed", "severity": "normal"}, {"uid": "9bc73e51a27bea6f", "name": "测试set customized cover screen返回正确的不支持响应", "time": {"start": 1753765811458, "stop": 1753765819846, "duration": 8388}, "status": "passed", "severity": "normal"}, {"uid": "a651e04c6a3ccba0", "name": "测试switching charging speed返回正确的不支持响应", "time": {"start": 1753766542302, "stop": 1753766550938, "duration": 8636}, "status": "passed", "severity": "normal"}, {"uid": "509ae9b5f4ed11e1", "name": "测试switch to power saving mode返回正确的不支持响应", "time": {"start": 1753766519465, "stop": 1753766528222, "duration": 8757}, "status": "passed", "severity": "normal"}, {"uid": "487f42ee1097884f", "name": "测试the second返回正确的不支持响应", "time": {"start": 1753766564387, "stop": 1753766572770, "duration": 8383}, "status": "passed", "severity": "normal"}, {"uid": "b6291b5bdf4cb29d", "name": "测试more settings返回正确的不支持响应", "time": {"start": 1753765496809, "stop": 1753765508975, "duration": 12166}, "status": "passed", "severity": "normal"}]