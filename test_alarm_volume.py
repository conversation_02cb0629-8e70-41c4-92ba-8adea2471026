#!/usr/bin/env python3
"""
测试闹钟音量获取功能
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def test_alarm_volume():
    """测试优化后的闹钟音量获取功能"""
    print("🔊 测试优化后的闹钟音量获取功能")
    print("=" * 60)

    try:
        # 创建系统状态检查器实例
        checker = SystemStatusChecker()

        # 测试新的完整信息获取方法
        print("\n🆕 测试闹钟音量完整信息获取...")
        alarm_info = checker.get_alarm_volume_info()

        if alarm_info['success']:
            print(f"✅ 闹钟音量完整信息:")
            print(f"   📊 音量值: {alarm_info['volume']}")
            print(f"   📈 百分比: {alarm_info['percentage']}%")
            print(f"   📏 最大音量: {alarm_info['max_volume']}")
            print(f"   🔇 是否静音: {alarm_info['is_muted']}")
            print(f"   🔍 检测方法: {alarm_info['detection_method']}")
        else:
            print("❌ 获取闹钟音量完整信息失败")

        # 测试优化后的单独方法
        print("\n📊 测试优化后的单独获取方法...")
        alarm_volume = checker.get_alarm_volume()
        alarm_percentage = checker.get_alarm_volume_percentage()

        print(f"优化后闹钟音量: {alarm_volume}")
        print(f"优化后闹钟音量百分比: {alarm_percentage}%")

        # 对比通用音量获取方法
        print("\n📊 对比通用音量获取方法...")
        general_alarm_volume = checker.get_system_volume("alarm")
        print(f"通用方法获取闹钟音量: {general_alarm_volume}")

        # 结果对比
        if alarm_volume != -1 and general_alarm_volume != -1:
            if alarm_volume == general_alarm_volume:
                print(f"✅ 优化方法与通用方法结果一致: {alarm_volume}")
            else:
                print(f"⚠️ 结果不一致: 优化方法={alarm_volume}, 通用方法={general_alarm_volume}")

        # 性能对比测试
        print("\n⚡ 性能对比测试...")
        import time

        # 测试优化方法性能
        start_time = time.time()
        for _ in range(3):
            checker.get_alarm_volume()
        optimized_time = time.time() - start_time

        # 测试通用方法性能
        start_time = time.time()
        for _ in range(3):
            checker.get_system_volume("alarm")
        general_time = time.time() - start_time

        print(f"优化方法耗时: {optimized_time:.3f}秒 (3次调用)")
        print(f"通用方法耗时: {general_time:.3f}秒 (3次调用)")

        if optimized_time < general_time:
            improvement = ((general_time - optimized_time) / general_time) * 100
            print(f"✅ 优化方法性能提升: {improvement:.1f}%")
        else:
            print("⚠️ 优化方法性能未明显提升")

        # 测试其他音量类型作为对比
        print("\n📊 获取其他音量类型作为对比...")
        media_volume = checker.get_system_volume("media")
        ring_volume = checker.get_system_volume("ring")

        print(f"媒体音量: {media_volume}")
        print(f"铃声音量: {ring_volume}")

        print("\n🎉 优化测试完成!")
        print(f"📋 代码优化总结:")
        print(f"   - 减少了重复的dumpsys调用")
        print(f"   - 复用了详细状态检测逻辑")
        print(f"   - 提供了一次性获取完整信息的方法")
        print(f"   - 保持了向后兼容性")

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_alarm_volume()
