#!/usr/bin/env python3
"""
测试通知音量获取功能
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def test_notification_volume():
    """测试通知音量获取功能"""
    print("🔔 测试通知音量获取功能")
    print("=" * 60)
    
    try:
        # 创建系统状态检查器实例
        checker = SystemStatusChecker()
        
        # 测试获取通知音量数值
        print("\n📊 获取通知音量数值...")
        notification_volume = checker.get_notification_volume()
        
        if notification_volume != -1:
            print(f"✅ 通知音量: {notification_volume}")
        else:
            print("❌ 获取通知音量失败")
        
        # 测试获取通知音量百分比
        print("\n📊 获取通知音量百分比...")
        notification_percentage = checker.get_notification_volume_percentage()
        
        if notification_percentage != -1:
            print(f"✅ 通知音量百分比: {notification_percentage}%")
        else:
            print("❌ 获取通知音量百分比失败")
        
        # 测试获取通知音量完整信息
        print("\n📊 获取通知音量完整信息...")
        notification_info = checker.get_notification_volume_info()
        
        if notification_info['success']:
            print(f"✅ 通知音量完整信息:")
            print(f"   📊 音量值: {notification_info['volume']}")
            print(f"   📈 百分比: {notification_info['percentage']}%")
            print(f"   📏 最大音量: {notification_info['max_volume']}")
            print(f"   🔇 是否静音: {notification_info['is_muted']}")
            print(f"   🔍 检测方法: {notification_info['detection_method']}")
        else:
            print("❌ 获取通知音量完整信息失败")
        
        # 对比通用音量获取方法
        print("\n📊 对比通用音量获取方法...")
        general_notification_volume = checker.get_system_volume("notification")
        
        if general_notification_volume != -1:
            print(f"✅ 通用方法获取通知音量: {general_notification_volume}")
        else:
            print("❌ 通用方法获取通知音量失败")
        
        # 结果对比
        if notification_volume != -1 and general_notification_volume != -1:
            if notification_volume == general_notification_volume:
                print(f"✅ 两种方法结果一致: {notification_volume}")
            else:
                print(f"⚠️ 两种方法结果不一致: 专用方法={notification_volume}, 通用方法={general_notification_volume}")
        
        # 对比闹钟音量和通知音量
        print("\n📊 对比闹钟音量和通知音量...")
        alarm_volume = checker.get_alarm_volume()
        alarm_info = checker.get_alarm_volume_info()
        
        print(f"闹钟音量: {alarm_volume}")
        print(f"通知音量: {notification_volume}")
        
        if alarm_volume != -1 and notification_volume != -1:
            if alarm_volume == notification_volume:
                print(f"✅ 闹钟音量和通知音量相同: {alarm_volume}")
            else:
                print(f"📊 闹钟音量({alarm_volume}) 与 通知音量({notification_volume}) 不同")
        
        # 测试其他音量类型作为对比
        print("\n📊 获取其他音量类型作为对比...")
        media_volume = checker.get_system_volume("media")
        ring_volume = checker.get_system_volume("ring")
        system_volume = checker.get_system_volume("system")
        
        print(f"媒体音量: {media_volume}")
        print(f"铃声音量: {ring_volume}")
        print(f"系统音量: {system_volume}")
        
        # 音量对比表格
        print("\n📋 音量对比表格:")
        print("┌─────────────┬─────────┬─────────────┐")
        print("│ 音量类型    │ 音量值  │ 百分比      │")
        print("├─────────────┼─────────┼─────────────┤")
        print(f"│ 闹钟音量    │ {alarm_volume:7} │ {alarm_info.get('percentage', -1):10}% │")
        print(f"│ 通知音量    │ {notification_volume:7} │ {notification_percentage:10}% │")
        print(f"│ 媒体音量    │ {media_volume:7} │ {'N/A':>10}  │")
        print(f"│ 铃声音量    │ {ring_volume:7} │ {'N/A':>10}  │")
        print(f"│ 系统音量    │ {system_volume:7} │ {'N/A':>10}  │")
        print("└─────────────┴─────────┴─────────────┘")
        
        print("\n🎉 通知音量测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_notification_volume()
