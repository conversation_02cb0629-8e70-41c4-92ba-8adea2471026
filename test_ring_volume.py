#!/usr/bin/env python3
"""
测试铃声音量获取功能
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def test_ring_volume():
    """测试铃声音量获取功能"""
    print("📞 测试铃声音量获取功能")
    print("=" * 60)
    
    try:
        # 创建系统状态检查器实例
        checker = SystemStatusChecker()
        
        # 测试获取铃声音量数值
        print("\n📊 获取铃声音量数值...")
        ring_volume = checker.get_ring_volume()
        
        if ring_volume != -1:
            print(f"✅ 铃声音量: {ring_volume}")
        else:
            print("❌ 获取铃声音量失败")
        
        # 测试获取铃声音量百分比
        print("\n📊 获取铃声音量百分比...")
        ring_percentage = checker.get_ring_volume_percentage()
        
        if ring_percentage != -1:
            print(f"✅ 铃声音量百分比: {ring_percentage}%")
        else:
            print("❌ 获取铃声音量百分比失败")
        
        # 测试获取铃声音量完整信息
        print("\n📊 获取铃声音量完整信息...")
        ring_info = checker.get_ring_volume_info()
        
        if ring_info['success']:
            print(f"✅ 铃声音量完整信息:")
            print(f"   📊 音量值: {ring_info['volume']}")
            print(f"   📈 百分比: {ring_info['percentage']}%")
            print(f"   📏 最大音量: {ring_info['max_volume']}")
            print(f"   🔇 是否静音: {ring_info['is_muted']}")
            print(f"   🔍 检测方法: {ring_info['detection_method']}")
        else:
            print("❌ 获取铃声音量完整信息失败")
        
        # 对比通用音量获取方法
        print("\n📊 对比通用音量获取方法...")
        general_ring_volume = checker.get_system_volume("ring")
        
        if general_ring_volume != -1:
            print(f"✅ 通用方法获取铃声音量: {general_ring_volume}")
        else:
            print("❌ 通用方法获取铃声音量失败")
        
        # 结果对比
        if ring_volume != -1 and general_ring_volume != -1:
            if ring_volume == general_ring_volume:
                print(f"✅ 两种方法结果一致: {ring_volume}")
            else:
                print(f"⚠️ 两种方法结果不一致: 专用方法={ring_volume}, 通用方法={general_ring_volume}")
        
        # 对比所有音量类型
        print("\n📊 对比所有音量类型...")
        alarm_volume = checker.get_alarm_volume()
        notification_volume = checker.get_notification_volume()
        media_volume = checker.get_system_volume("media")
        system_volume = checker.get_system_volume("system")
        voice_call_volume = checker.get_system_volume("voice_call")
        
        print(f"闹钟音量: {alarm_volume}")
        print(f"通知音量: {notification_volume}")
        print(f"铃声音量: {ring_volume}")
        print(f"媒体音量: {media_volume}")
        print(f"系统音量: {system_volume}")
        print(f"通话音量: {voice_call_volume}")
        
        # 获取完整信息进行对比
        alarm_info = checker.get_alarm_volume_info()
        notification_info = checker.get_notification_volume_info()
        
        # 详细音量对比表格
        print("\n📋 详细音量对比表格:")
        print("┌─────────────┬─────────┬─────────────┬─────────────┬─────────────┐")
        print("│ 音量类型    │ 音量值  │ 百分比      │ 最大音量    │ 是否静音    │")
        print("├─────────────┼─────────┼─────────────┼─────────────┼─────────────┤")
        print(f"│ 闹钟音量    │ {alarm_volume:7} │ {alarm_info.get('percentage', -1):10}% │ {alarm_info.get('max_volume', 15):10} │ {str(alarm_info.get('is_muted', False)):>10} │")
        print(f"│ 通知音量    │ {notification_volume:7} │ {notification_info.get('percentage', -1):10}% │ {notification_info.get('max_volume', 15):10} │ {str(notification_info.get('is_muted', False)):>10} │")
        print(f"│ 铃声音量    │ {ring_volume:7} │ {ring_percentage:10}% │ {ring_info.get('max_volume', 15):10} │ {str(ring_info.get('is_muted', False)):>10} │")
        print(f"│ 媒体音量    │ {media_volume:7} │ {'N/A':>10}  │ {'N/A':>10}  │ {'N/A':>10}  │")
        print(f"│ 系统音量    │ {system_volume:7} │ {'N/A':>10}  │ {'N/A':>10}  │ {'N/A':>10}  │")
        print(f"│ 通话音量    │ {voice_call_volume:7} │ {'N/A':>10}  │ {'N/A':>10}  │ {'N/A':>10}  │")
        print("└─────────────┴─────────┴─────────────┴─────────────┴─────────────┘")
        
        # 音量关系分析
        print("\n🔍 音量关系分析:")
        if alarm_volume == notification_volume:
            print(f"✅ 闹钟音量和通知音量相同: {alarm_volume}")
        else:
            print(f"📊 闹钟音量({alarm_volume}) 与 通知音量({notification_volume}) 不同")
        
        if ring_volume == notification_volume:
            print(f"✅ 铃声音量和通知音量相同: {ring_volume}")
        else:
            print(f"📊 铃声音量({ring_volume}) 与 通知音量({notification_volume}) 不同")
        
        if ring_volume == alarm_volume:
            print(f"✅ 铃声音量和闹钟音量相同: {ring_volume}")
        else:
            print(f"📊 铃声音量({ring_volume}) 与 闹钟音量({alarm_volume}) 不同")
        
        # 检测方法对比
        print("\n🔍 检测方法对比:")
        print(f"闹钟音量检测方法: {alarm_info.get('detection_method', 'UNKNOWN')}")
        print(f"通知音量检测方法: {notification_info.get('detection_method', 'UNKNOWN')}")
        print(f"铃声音量检测方法: {ring_info.get('detection_method', 'UNKNOWN')}")
        
        print("\n🎉 铃声音量测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_ring_volume()
