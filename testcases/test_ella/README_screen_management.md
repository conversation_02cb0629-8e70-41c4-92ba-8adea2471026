# Ella测试屏幕管理集成说明

## 📱 功能概述

已将屏幕管理功能无缝集成到Ella测试基类中，实现：

✅ **批量测试自动屏幕管理**：测试开始前自动设置屏幕常亮，结束后自动恢复  
✅ **零代码修改**：现有测试用例无需任何修改即可享受屏幕管理  
✅ **灵活配置**：支持不同测试场景的屏幕设置需求  
✅ **异常安全**：确保测试异常时也能正确恢复屏幕设置

## 🚀 使用方法

### 1. 零配置使用（推荐）

现有的Ella测试用例**无需任何修改**，屏幕管理会自动生效：

```python
# 现有测试代码保持不变
class TestBluetoothCommand(BaseEllaTest):
    def test_open_bluetooth(self, ella_app):
        # 屏幕会自动设置为常亮
        # 测试结束后自动恢复为10分钟超时
        pass
```

### 2. 批量执行时的自动管理

```bash
# 执行单个测试文件
pytest testcases/test_ella/test_open_bluetooth.py -v

# 执行整个目录的测试
pytest testcases/test_ella/ -v

# 执行特定标记的测试
pytest testcases/test_ella/ -m "bluetooth" -v
```

**自动执行流程**：
1. 🚀 **测试会话开始**：自动设置屏幕永不超时 + 充电时常亮
2. 🔄 **执行所有测试**：屏幕保持常亮，不会自动关闭
3. ✅ **测试会话结束**：自动恢复屏幕设置为10分钟超时

### 3. 特殊场景使用

#### 长时间测试（永不超时）

```python
class TestLongRunning(BaseEllaTest):
    @pytest.mark.long_running
    def test_stability(self, ella_app, screen_never_timeout):
        # 使用永不超时fixture，适合长时间测试
        pass
```

#### 快速测试（短超时）

```python
class TestQuick(BaseEllaTest):
    @pytest.mark.quick_test  
    def test_simple_command(self, ella_app, screen_quick_timeout):
        # 使用5分钟超时，适合快速测试
        pass
```

#### 禁用屏幕管理

```python
class TestSpecial(BaseEllaTest):
    def test_without_screen_management(self, ella_app, disable_screen_management):
        # 禁用屏幕管理，使用系统默认设置
        pass
```

## 🔧 配置选项

### 全局配置

```python
# 在测试开始前配置
from testcases.test_ella.base_ella_test import BaseEllaTest

# 禁用屏幕管理
BaseEllaTest.set_screen_management_config(enabled=False)

# 启用屏幕管理（默认）
BaseEllaTest.set_screen_management_config(enabled=True)
```

### 获取屏幕状态

```python
class TestExample(BaseEllaTest):
    def test_check_screen_status(self, ella_app):
        # 获取当前屏幕状态
        status = self.get_screen_status()
        print(f"屏幕超时: {status['screen_timeout_description']}")
        print(f"充电常亮: {status['stay_on_description']}")
```

## 📋 pytest标记

已预定义的测试标记：

```python
@pytest.mark.screen_management  # 需要特殊屏幕管理的测试
@pytest.mark.long_running       # 长时间运行的测试  
@pytest.mark.quick_test         # 快速测试
```

使用方法：
```bash
# 只运行长时间测试
pytest testcases/test_ella/ -m "long_running" -v

# 只运行快速测试
pytest testcases/test_ella/ -m "quick_test" -v

# 排除长时间测试
pytest testcases/test_ella/ -m "not long_running" -v
```

## 🔍 实际效果验证

### 查看屏幕设置变化

```bash
# 测试开始前查看屏幕超时（应该是默认值，如10分钟）
adb shell settings get system screen_off_timeout

# 测试执行中查看（应该是2147483647，表示永不超时）
adb shell settings get system screen_off_timeout

# 测试结束后查看（应该恢复为600000，即10分钟）
adb shell settings get system screen_off_timeout
```

### 查看充电时常亮设置

```bash
# 查看充电时保持常亮设置
adb shell settings get global stay_on_while_plugged_in

# 值说明：
# 0 = 关闭
# 7 = 所有充电方式都保持常亮（测试期间的设置）
```

## 📊 集成架构

```
pytest会话开始
    ↓
conftest.py::pytest_sessionstart()
    ↓
BaseEllaTest.setup_batch_test_screen()
    ↓ 
设置屏幕永不超时 + 充电时常亮
    ↓
执行所有测试用例
    ↓
conftest.py::pytest_sessionfinish()
    ↓
BaseEllaTest.restore_batch_test_screen()
    ↓
恢复屏幕10分钟超时 + 关闭充电时常亮
```

## 🛠️ 核心文件

| 文件 | 作用 | 修改内容 |
|------|------|----------|
| `base_ella_test.py` | 测试基类 | 添加屏幕管理方法，不影响原有逻辑 |
| `conftest.py` | pytest配置 | 处理批量测试的屏幕管理钩子 |
| `test_screen_management_example.py` | 使用示例 | 演示各种屏幕管理用法 |

## ⚠️ 注意事项

### 使用前提
1. **连接充电器**：建议连接充电器以确保"充电时常亮"功能生效
2. **ADB连接正常**：确保ADB调试已开启且连接稳定
3. **设备权限**：确保有修改系统设置的权限

### 最佳实践
1. **批量执行**：建议使用pytest批量执行测试，享受自动屏幕管理
2. **标记使用**：为不同类型的测试添加合适的标记
3. **状态检查**：在长时间测试前检查屏幕状态是否正确设置

### 故障排除
```bash
# 检查ADB连接
adb devices

# 手动验证屏幕设置
adb shell settings get system screen_off_timeout
adb shell settings get global stay_on_while_plugged_in

# 手动恢复默认设置（如果需要）
adb shell settings put system screen_off_timeout 600000
adb shell settings put global stay_on_while_plugged_in 0
```

## 📈 使用效果

### 测试执行前
```
屏幕超时: 10分钟
充电常亮: 关闭
```

### 测试执行中  
```
屏幕超时: 永不超时
充电常亮: 所有充电方式都保持常亮
```

### 测试执行后
```
屏幕超时: 10分钟  
充电常亮: 关闭
```

## 🎯 示例运行

```bash
# 运行屏幕管理示例测试
pytest testcases/test_ella/test_screen_management_example.py -v

# 运行现有的蓝牙测试（自动享受屏幕管理）
pytest testcases/test_ella/system_coupling/test_open_bluetooth.py -v

# 运行整个Ella测试套件
pytest testcases/test_ella/ -v --tb=short
```

---

**现在您的Ella测试已经具备了完整的屏幕管理能力，无需担心测试过程中手机自动灭屏的问题！** 🌟
