"""
Ella测试的pytest配置文件
处理批量测试的屏幕管理
"""
import pytest
from core.logger import log


def pytest_sessionstart(session):
    """
    pytest会话开始时的钩子函数
    在所有测试开始前执行一次
    """
    try:
        # 导入BaseEllaTest类
        from testcases.test_ella.base_ella_test import BaseEllaTest
        
        log.info("🚀 Ella测试会话开始，设置批量测试屏幕管理...")
        
        # 设置批量测试屏幕为常亮
        BaseEllaTest.setup_batch_test_screen()
        
        log.info("✅ 批量测试屏幕设置完成")
        
    except Exception as e:
        log.error(f"❌ 批量测试屏幕设置失败: {e}")


def pytest_sessionfinish(session, exitstatus):
    """
    pytest会话结束时的钩子函数
    在所有测试结束后执行一次
    """
    try:
        # 导入BaseEllaTest类
        from testcases.test_ella.base_ella_test import BaseEllaTest
        
        log.info("🔄 Ella测试会话结束，恢复屏幕设置...")
        
        # 恢复屏幕设置为10分钟超时
        BaseEllaTest.restore_batch_test_screen()
        
        log.info("✅ 批量测试屏幕设置恢复完成")
        
        # 记录测试会话结果
        if exitstatus == 0:
            log.info("🎉 所有测试执行成功")
        else:
            log.warning(f"⚠️ 测试执行完成，退出状态: {exitstatus}")
            
    except Exception as e:
        log.error(f"❌ 批量测试屏幕设置恢复失败: {e}")


def pytest_runtest_setup(item):
    """
    每个测试用例开始前的钩子函数
    可以在这里添加单个测试用例级别的屏幕管理逻辑
    """
    # 这里可以添加单个测试用例的特殊屏幕设置
    # 目前使用类级别的管理，所以这里暂时不需要额外操作
    pass


def pytest_runtest_teardown(item, nextitem):
    """
    每个测试用例结束后的钩子函数
    可以在这里添加单个测试用例级别的清理逻辑
    """
    # 这里可以添加单个测试用例的清理逻辑
    # 目前使用类级别的管理，所以这里暂时不需要额外操作
    pass


def pytest_configure(config):
    """
    pytest配置钩子函数
    可以在这里添加全局配置
    """
    # 可以在这里添加屏幕管理的全局配置
    log.info("📋 Ella测试配置初始化完成")


def pytest_unconfigure(config):
    """
    pytest取消配置钩子函数
    在pytest退出前执行
    """
    log.info("📋 Ella测试配置清理完成")


# 可以添加自定义的pytest标记
def pytest_configure(config):
    """注册自定义标记"""
    config.addinivalue_line(
        "markers", "screen_management: 标记需要特殊屏幕管理的测试"
    )
    config.addinivalue_line(
        "markers", "long_running: 标记长时间运行的测试"
    )
    config.addinivalue_line(
        "markers", "quick_test: 标记快速测试"
    )


# 自定义fixture，用于特殊的屏幕管理需求
@pytest.fixture
def screen_never_timeout():
    """
    为特定测试设置屏幕永不超时的fixture
    使用方法：在测试函数参数中添加 screen_never_timeout
    """
    try:
        from testcases.test_ella.base_ella_test import BaseEllaTest
        
        log.info("🔧 为当前测试设置屏幕永不超时...")
        screen_manager = BaseEllaTest.get_screen_manager()
        
        # 保存当前超时设置
        original_timeout = screen_manager.get_current_screen_timeout()
        
        # 设置永不超时
        screen_manager.set_screen_never_timeout()
        
        yield
        
        # 恢复原始超时设置
        if original_timeout:
            screen_manager.set_screen_timeout(original_timeout)
            log.info(f"🔄 恢复原始屏幕超时设置: {original_timeout}ms")
            
    except Exception as e:
        log.error(f"❌ 屏幕永不超时设置失败: {e}")
        yield


@pytest.fixture
def screen_quick_timeout():
    """
    为快速测试设置较短超时时间的fixture
    使用方法：在测试函数参数中添加 screen_quick_timeout
    """
    try:
        from testcases.test_ella.base_ella_test import BaseEllaTest
        
        log.info("🔧 为快速测试设置5分钟屏幕超时...")
        screen_manager = BaseEllaTest.get_screen_manager()
        
        # 保存当前超时设置
        original_timeout = screen_manager.get_current_screen_timeout()
        
        # 设置5分钟超时
        screen_manager.set_screen_timeout_minutes(5)
        
        yield
        
        # 恢复原始超时设置
        if original_timeout:
            screen_manager.set_screen_timeout(original_timeout)
            log.info(f"🔄 恢复原始屏幕超时设置: {original_timeout}ms")
            
    except Exception as e:
        log.error(f"❌ 快速测试屏幕超时设置失败: {e}")
        yield


@pytest.fixture
def disable_screen_management():
    """
    禁用屏幕管理的fixture
    使用方法：在测试函数参数中添加 disable_screen_management
    """
    try:
        from testcases.test_ella.base_ella_test import BaseEllaTest
        
        log.info("🔧 为当前测试禁用屏幕管理...")
        
        # 保存原始配置
        original_enabled = BaseEllaTest._screen_management_enabled
        
        # 禁用屏幕管理
        BaseEllaTest.set_screen_management_config(enabled=False)
        
        yield
        
        # 恢复原始配置
        BaseEllaTest.set_screen_management_config(enabled=original_enabled)
        log.info("🔄 恢复屏幕管理配置")
        
    except Exception as e:
        log.error(f"❌ 禁用屏幕管理失败: {e}")
        yield
