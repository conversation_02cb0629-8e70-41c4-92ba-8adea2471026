"""
Ella语音助手不支持的指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("不支持的指令")
class TestEllaBookFlightParis(SimpleEllaTest):
    """Ella book a flight to paris 测试类 - 验证不支持的指令响应"""
    command = "book a flight to paris"
    expected_text = ['flight','paris']

    @allure.title(f"测试{command}返回正确的不支持响应")
    @allure.description(f"验证{command}指令返回预期的不支持响应")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.smoke
    def test_book_a_flight_to_paris(self, ella_app):
        f"""{self.command} - 验证不支持指令的响应"""

        command = self.command

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含期望的不支持内容"):
            expected_text = self.expected_text
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step("记录测试结果"):
            summary = {
                "command": command,
                "response_text": response_text,
                "expected_text": expected_text,
                "test_type": "unsupported_command",
                "result": "PASS" if result else "FAIL"
            }
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "unsupported_command_test_completed")
