"""
Ella语音助手第三方集成指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("第三方集成")
class TestEllaChangeYourLanguage(SimpleEllaTest):
    """Ella change your language 测试类"""
    command = "change your language"
    expected_text = ['OK, redirecting to the Ella language list page.']

    @allure.title(f"测试{command}能正常执行")
    @allure.description(f"{command}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_change_your_language(self, ella_app):
        f"""{self.command}"""

        command = self.command
        expected_text = self.expected_text

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command, verify_status=False, verify_files=False
            )

        with allure.step("验证响应包含期望内容"):
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
