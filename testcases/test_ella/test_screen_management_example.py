"""
屏幕管理集成示例测试
演示如何在Ella测试中使用屏幕管理功能
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import BaseEllaTest, SimpleEllaTest
from core.logger import log


class TestScreenManagementExample(BaseEllaTest):
    """屏幕管理示例测试类"""

    def test_basic_command_with_auto_screen_management(self, ella_app):
        """
        基本命令测试 - 自动屏幕管理
        屏幕会自动设置为常亮，测试结束后自动恢复
        """
        with allure.step("执行基本命令测试"):
            command = "open bluetooth"
            
            # 记录当前屏幕状态
            screen_status = self.get_screen_status()
            log.info(f"📊 当前屏幕状态: {screen_status.get('screen_timeout_description', '未知')}")
            
            # 执行命令测试
            initial_status, final_status, response_text, files_status = self.execute_command_and_verify(
                ella_app, command, expected_status_change=True
            )
            
            # 验证响应
            self.verify_expected_in_response(["bluetooth", "open"], response_text)
            
            # 创建测试总结
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            
        log.info("✅ 基本命令测试完成（自动屏幕管理）")

    @pytest.mark.long_running
    def test_long_running_command_with_never_timeout(self, ella_app, screen_never_timeout):
        """
        长时间运行测试 - 使用永不超时fixture
        适用于需要长时间执行的测试用例
        """
        with allure.step("执行长时间运行测试"):
            command = "take a photo"
            
            # 记录屏幕状态
            screen_status = self.get_screen_status()
            log.info(f"📊 长时间测试屏幕状态: {screen_status.get('screen_timeout_description', '未知')}")
            
            # 执行命令测试
            initial_status, final_status, response_text, files_status = self.execute_command_and_verify(
                ella_app, command, expected_status_change=True, verify_files=True
            )
            
            # 验证响应
            self.verify_expected_in_response(["photo", "camera"], response_text)
            
            # 创建测试总结
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            
        log.info("✅ 长时间运行测试完成（永不超时）")

    @pytest.mark.quick_test
    def test_quick_command_with_short_timeout(self, ella_app, screen_quick_timeout):
        """
        快速测试 - 使用较短超时fixture
        适用于执行时间较短的测试用例
        """
        with allure.step("执行快速测试"):
            command = "open calculator"
            
            # 记录屏幕状态
            screen_status = self.get_screen_status()
            log.info(f"📊 快速测试屏幕状态: {screen_status.get('screen_timeout_description', '未知')}")
            
            # 执行命令测试
            initial_status, final_status, response_text, files_status = self.execute_command_and_verify(
                ella_app, command, expected_status_change=True
            )
            
            # 验证响应
            self.verify_expected_in_response(["calculator", "open"], response_text)
            
            # 创建测试总结
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            
        log.info("✅ 快速测试完成（短超时）")

    def test_manual_screen_status_check(self, ella_app):
        """
        手动检查屏幕状态测试
        演示如何在测试中手动获取屏幕状态信息
        """
        with allure.step("检查屏幕状态"):
            # 获取详细屏幕状态
            screen_status = self.get_screen_status()
            
            log.info("📊 详细屏幕状态信息:")
            log.info(f"  - 超时时间: {screen_status.get('screen_timeout_description', '未知')}")
            log.info(f"  - 充电常亮: {screen_status.get('stay_on_description', '未知')}")
            log.info(f"  - 屏幕状态: {screen_status.get('power_state', '未知')}")
            
            # 将屏幕状态信息附加到测试报告
            status_summary = f"""
屏幕状态详情:
- 超时时间: {screen_status.get('screen_timeout_description', '未知')}
- 充电常亮: {screen_status.get('stay_on_description', '未知')}
- 屏幕状态: {screen_status.get('power_state', '未知')}
- 超时毫秒: {screen_status.get('screen_timeout_ms', '未知')}
- 常亮设置值: {screen_status.get('stay_on_while_plugged', '未知')}
            """.strip()
            
            allure.attach(status_summary, name="屏幕状态详情", attachment_type=allure.attachment_type.TEXT)
            
        with allure.step("执行简单命令"):
            command = "hello"
            
            # 执行简单命令
            initial_status, final_status, response_text, files_status = self.execute_command_and_verify(
                ella_app, command, expected_status_change=False
            )
            
            # 验证响应
            self.verify_expected_in_response(["hello", "hi"], response_text)
            
        log.info("✅ 屏幕状态检查测试完成")

    @pytest.mark.screen_management
    def test_disable_screen_management(self, ella_app, disable_screen_management):
        """
        禁用屏幕管理测试
        演示如何在特定测试中禁用屏幕管理
        """
        with allure.step("禁用屏幕管理的测试"):
            command = "open wifi"
            
            log.info("🔧 当前测试已禁用屏幕管理")
            
            # 执行命令测试
            initial_status, final_status, response_text, files_status = self.execute_command_and_verify(
                ella_app, command, expected_status_change=True
            )
            
            # 验证响应
            self.verify_expected_in_response(["wifi", "open"], response_text)
            
            # 创建测试总结
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            
        log.info("✅ 禁用屏幕管理测试完成")


class TestSimpleScreenManagementExample(SimpleEllaTest):
    """简化版屏幕管理示例测试类"""

    def test_simple_command_with_screen_management(self, ella_app):
        """
        简化版命令测试 - 自动屏幕管理
        使用SimpleEllaTest的简化方法
        """
        with allure.step("执行简化版命令测试"):
            command = "close bluetooth"
            
            # 记录屏幕状态
            screen_status = self.get_screen_status()
            log.info(f"📊 简化测试屏幕状态: {screen_status.get('screen_timeout_description', '未知')}")
            
            # 使用简化方法执行测试
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command, verify_status=True
            )
            
            # 验证响应
            self.verify_expected_in_response(["bluetooth", "close"], response_text)
            
        log.info("✅ 简化版命令测试完成")

    def test_multiple_commands_batch(self, ella_app):
        """
        批量命令测试
        演示在一个测试中执行多个命令
        """
        commands = [
            "open bluetooth",
            "close bluetooth", 
            "open wifi",
            "close wifi"
        ]
        
        with allure.step("执行批量命令测试"):
            # 记录屏幕状态
            screen_status = self.get_screen_status()
            log.info(f"📊 批量测试屏幕状态: {screen_status.get('screen_timeout_description', '未知')}")
            
            results = []
            
            for i, command in enumerate(commands, 1):
                with allure.step(f"执行命令 {i}/{len(commands)}: {command}"):
                    log.info(f"🔄 执行第{i}个命令: {command}")
                    
                    # 执行命令
                    initial_status, final_status, response_text, files_status = self.simple_command_test(
                        ella_app, command, verify_status=True
                    )
                    
                    results.append({
                        'command': command,
                        'initial_status': initial_status,
                        'final_status': final_status,
                        'response_text': response_text
                    })
                    
                    log.info(f"✅ 第{i}个命令执行完成")
            
            # 创建批量测试总结
            batch_summary = "批量命令测试结果:\n"
            for i, result in enumerate(results, 1):
                batch_summary += f"{i}. {result['command']}: {result['initial_status']} -> {result['final_status']}\n"
            
            allure.attach(batch_summary, name="批量测试总结", attachment_type=allure.attachment_type.TEXT)
            
        log.info(f"✅ 批量命令测试完成，共执行{len(commands)}个命令")


if __name__ == "__main__":
    # 可以直接运行这个文件进行测试
    pytest.main([__file__, "-v", "--allure-dir=reports/allure-results"])
