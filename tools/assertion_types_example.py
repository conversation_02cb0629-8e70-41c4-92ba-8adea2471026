#!/usr/bin/env python3
"""
断言类型使用示例
展示如何使用优化后的Ella测试用例生成器
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from tools.ella_test_generator_v2 import generate_with_custom_response


def main():
    """断言类型使用示例"""
    
    print("🚀 Ella测试用例生成器 - 断言类型示例")
    print("=" * 60)
    
    # 示例1: 只验证文本响应 (text)
    print("\n📝 示例1: 只验证文本响应")
    print("-" * 30)
    command1 = "tell me a joke"
    expected_response1 = ["Here's a joke for you"]
    assertion_types1 = ['text']
    
    file_path1 = generate_with_custom_response(
        command1, 
        expected_response1, 
        output_dir_name="dialogue",
        assertion_types=assertion_types1
    )
    print(f"✅ 生成的测试用例: {file_path1}")
    print(f"   断言类型: {assertion_types1}")
    print(f"   验证内容: 只验证响应文本包含期望内容")
    
    # 示例2: 验证应用状态 (text + process)
    print("\n📱 示例2: 验证应用状态")
    print("-" * 30)
    command2 = "open calculator"
    expected_response2 = ["Done"]
    assertion_types2 = ['text', 'process']
    
    file_path2 = generate_with_custom_response(
        command2, 
        expected_response2, 
        output_dir_name="open_app",
        assertion_types=assertion_types2
    )
    print(f"✅ 生成的测试用例: {file_path2}")
    print(f"   断言类型: {assertion_types2}")
    print(f"   验证内容: 响应文本 + 应用是否已打开")
    
    # 示例3: 验证文件生成 (text + files)
    print("\n📁 示例3: 验证文件生成")
    print("-" * 30)
    command3 = "record audio for 5 seconds"
    expected_response3 = ["Recording completed"]
    assertion_types3 = ['text', 'files']
    
    file_path3 = generate_with_custom_response(
        command3, 
        expected_response3, 
        output_dir_name="component_coupling",
        assertion_types=assertion_types3
    )
    print(f"✅ 生成的测试用例: {file_path3}")
    print(f"   断言类型: {assertion_types3}")
    print(f"   验证内容: 响应文本 + 文件是否存在")
    
    # 示例4: 全面验证 (text + process + files)
    print("\n🎯 示例4: 全面验证")
    print("-" * 30)
    command4 = "stop screen recording"
    expected_response4 = ["Screen recording finished"]
    assertion_types4 = ['text', 'process', 'files']
    
    file_path4 = generate_with_custom_response(
        command4, 
        expected_response4, 
        output_dir_name="component_coupling",
        assertion_types=assertion_types4
    )
    print(f"✅ 生成的测试用例: {file_path4}")
    print(f"   断言类型: {assertion_types4}")
    print(f"   验证内容: 响应文本 + 应用状态 + 文件存在")
    
    # 示例5: 不支持的命令 (只验证text)
    print("\n❌ 示例5: 不支持的命令")
    print("-" * 30)
    command5 = "fly to the moon"
    expected_response5 = ["Sorry, I can't help with that"]
    assertion_types5 = ['text']
    
    file_path5 = generate_with_custom_response(
        command5, 
        expected_response5, 
        output_dir_name="unsupported_commands",
        assertion_types=assertion_types5
    )
    print(f"✅ 生成的测试用例: {file_path5}")
    print(f"   断言类型: {assertion_types5}")
    print(f"   验证内容: 只验证不支持的响应文本")
    
    print("\n" + "=" * 60)
    print("🎉 所有示例生成完成！")
    print("\n📋 断言类型说明:")
    print("   • text    - 验证响应包含期望内容")
    print("   • process - 验证应用状态变化")
    print("   • files   - 验证文件是否存在")
    print("\n💡 生成的测试用例会根据断言类型自动包含相应的验证步骤")


if __name__ == "__main__":
    main()
