#!/usr/bin/env python3
"""
批量生成所有Excel测试用例脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from tools.excel_test_generator import ExcelTestGenerator

def main():
    """生成所有测试用例"""
    print("🚀 Excel驱动的Ella测试用例批量生成工具")
    print("=" * 60)
    
    # 创建生成器
    generator = ExcelTestGenerator()
    
    # 预览数据
    print("\n📋 数据预览:")
    generator.preview_data(5)
    
    # 询问用户确认
    print(f"\n📊 Excel文件包含 {len(generator.df) if generator.df is not None else 0} 条记录")
    print("将要生成所有测试用例，这可能需要一些时间...")
    
    confirm = input("\n是否继续生成所有测试用例？(y/n): ").strip().lower()
    if confirm not in ['y', 'yes', '是']:
        print("❌ 已取消生成")
        return
    
    print("\n🚀 开始批量生成所有测试用例...")
    
    # 生成所有测试用例
    files = generator.generate_batch_tests()
    
    print(f"\n✅ 批量生成完成！共生成 {len(files)} 个测试文件")
    
    # 统计各目录的文件数量
    dir_stats = {}
    for file_path in files:
        path_obj = Path(file_path)
        dir_name = path_obj.parent.name
        dir_stats[dir_name] = dir_stats.get(dir_name, 0) + 1
    
    print("\n📁 各目录生成统计:")
    for dir_name, count in sorted(dir_stats.items()):
        print(f"  - {dir_name}: {count} 个文件")
    
    print("\n📂 生成的文件位置:")
    print("  - unsupported_commands: 不支持的命令测试 (响应包含Sorry/Oops)")
    print("  - system_coupling: 系统耦合相关测试")
    print("  - third_coupling: 第三方耦合相关测试") 
    print("  - component_coupling: 模块耦合相关测试")
    
    print(f"\n🎉 所有测试用例已生成到: {project_root}/testcases/test_ella/")

if __name__ == "__main__":
    main()
