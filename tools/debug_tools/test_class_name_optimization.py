#!/usr/bin/env python3
"""
测试类名生成优化效果
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from tools.ella_test_generator_v2 import EllaTestGenerator

def test_class_name_generation():
    """测试类名生成优化效果"""
    generator = EllaTestGenerator()
    
    # 测试用例：包含数字、特殊字符的命令
    test_commands = [
        "disable touch optimization",
        "set sim1 ringtone", 
        "check my balance of sim2",
        "set date & time",
        "switch to power saving mode",
        "order a burger",
        "enable call rejection",
        "set screen to minimum brightness",
        "jump to lock screen notification and display settings",
        "set phantom v pen",
        "enable all ai magic box features",
        "set battery saver settings",
        "how's the weather today?",
        "音色设置页面",
        "set scheduled power on/off and restart"
    ]
    
    print("🧪 测试类名和方法名生成优化效果")
    print("=" * 80)
    print(f"{'命令':<40} {'类名':<35} {'方法名'}")
    print("-" * 80)
    
    for command in test_commands:
        class_name = generator.generate_class_name(command)
        method_name = generator.generate_method_name(command)
        print(f"{command:<40} {class_name:<35} {method_name}")
    
    print("\n✅ 优化效果:")
    print("- 类名只保留字母，去除数字和特殊字符")
    print("- 方法名只保留字母，用下划线连接")
    print("- 过滤掉常见的无意义词汇（a, the, to, and, or, of, in, on, at, by, for, with）")
    print("- 处理中文字符和特殊符号")

if __name__ == "__main__":
    test_class_name_generation()
