#!/usr/bin/env python3
"""
测试优化后的生成效果
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from tools.excel_test_generator import ExcelTestGenerator

def main():
    """测试优化后的生成效果"""
    print("🧪 测试优化后的类名生成效果")
    print("=" * 50)
    
    # 创建生成器
    generator = ExcelTestGenerator()
    
    # 生成前5个测试用例到测试目录
    print("🚀 生成前5个测试用例...")
    files = generator.generate_batch_tests(start_row=0, end_row=5)
    
    print(f"\n✅ 生成完成！共生成 {len(files)} 个测试文件:")
    for i, file_path in enumerate(files, 1):
        print(f"  {i}. {file_path}")
        
        # 显示生成的类名
        path_obj = Path(file_path)
        file_name = path_obj.stem  # 去掉.py扩展名
        print(f"     文件名: {file_name}")
    
    print("\n📋 优化效果对比:")
    print("优化前可能的类名: TestEllaDisableTouch1Optimization")
    print("优化后的类名:     TestEllaDisableTouchOptimization")
    print("\n✨ 优化特点:")
    print("- 去除了数字和特殊字符")
    print("- 保持了语义清晰性")
    print("- 符合Python类命名规范")

if __name__ == "__main__":
    main()
