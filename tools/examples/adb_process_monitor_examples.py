"""
ADB进程监控工具使用示例
演示如何使用adb_process_monitor.py的各种功能
"""
import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

try:
    from core.logger import log
except ImportError:
    class SimpleLogger:
        def info(self, msg): print(f"[INFO] {msg}")
        def error(self, msg): print(f"[ERROR] {msg}")
        def warning(self, msg): print(f"[WARNING] {msg}")
    log = SimpleLogger()


def run_command(command: str, description: str):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🔍 {description}")
    print(f"命令: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command.split(),
            cwd=project_root,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"❌ 命令执行失败: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("❌ 命令执行超时")
    except Exception as e:
        print(f"❌ 执行出错: {e}")


def main():
    """主函数 - 演示各种使用场景"""
    
    print("📱 ADB进程监控工具使用示例")
    print("本示例将演示工具的各种功能")
    
    # 检查设备连接
    print("\n🔍 检查设备连接...")
    try:
        result = subprocess.run(["adb", "devices"], capture_output=True, text=True, timeout=10)
        if "device" in result.stdout:
            print("✅ 设备已连接")
        else:
            print("❌ 未检测到设备，请确保:")
            print("1. 设备已连接并开启USB调试")
            print("2. 设备已信任此计算机")
            print("3. ADB工具正常工作")
            return
    except Exception as e:
        print(f"❌ 检查设备连接失败: {e}")
        return
    
    # 示例1: 显示帮助信息
    run_command(
        "python tools/adb_process_monitor.py",
        "显示工具帮助信息"
    )
    
    # 示例2: 显示所有前台进程
    run_command(
        "python tools/adb_process_monitor.py --foreground-only",
        "显示所有前台应用进程"
    )
    
    # 示例3: 显示所有后台进程（限制输出）
    print(f"\n{'='*60}")
    print(f"🔍 显示部分后台应用进程（前10个）")
    print(f"命令: python tools/adb_process_monitor.py --background-only")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            ["python", "tools/adb_process_monitor.py", "--background-only"],
            cwd=project_root,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            # 显示前20行和统计信息
            for i, line in enumerate(lines):
                if i < 20 or "📊 统计信息" in line or "前台进程:" in line or "后台进程:" in line or "总计:" in line:
                    print(line)
                elif i == 20:
                    print("... (更多进程省略)")
        else:
            print(f"❌ 命令执行失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 执行出错: {e}")
    
    # 示例4: 过滤特定应用
    run_command(
        "python tools/adb_process_monitor.py --filter google",
        "过滤包含'google'的进程"
    )
    
    # 示例5: 过滤Chrome相关进程
    run_command(
        "python tools/adb_process_monitor.py --filter chrome",
        "过滤Chrome相关进程"
    )
    
    # 示例6: 导出结果到文件
    output_file = "tools/examples/process_report_example.json"
    run_command(
        f"python tools/adb_process_monitor.py --filter android --export {output_file}",
        "导出Android相关进程到JSON文件"
    )

    # 示例7: 检测指定包名状态 (新增功能)
    run_command(
        "python tools/adb_process_monitor.py --check-package com.transsion.calendar",
        "检测日历应用是否在前台或后台运行"
    )

    # 示例8: 检测Chrome状态
    run_command(
        "python tools/adb_process_monitor.py --check-package com.android.chrome",
        "检测Chrome浏览器运行状态"
    )

    # 示例9: 检测不存在的应用
    run_command(
        "python tools/adb_process_monitor.py --check-package com.example.nonexistent",
        "检测不存在的应用包名"
    )
    
    # 检查导出文件
    if Path(output_file).exists():
        print(f"\n✅ 导出文件已创建: {output_file}")
        print("文件内容预览:")
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                for i, line in enumerate(lines[:10]):  # 显示前10行
                    print(f"  {line}")
                if len(lines) > 10:
                    print("  ... (更多内容省略)")
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
    
    print(f"\n{'='*60}")
    print("🎉 示例演示完成！")
    print("\n💡 更多使用技巧:")
    print("1. 组合使用过滤器: --filter wechat --min-memory 50")
    print("2. 定期监控: 可以写脚本定期运行并导出结果")
    print("3. 性能分析: 结合内存过滤器分析高内存使用应用")
    print("4. 自动化测试: 集成到测试流程中验证应用状态")
    print("5. 包状态检测: --check-package 快速检测指定应用运行状态")
    print("6. 状态导出: --check-package com.app --export status.json")
    print(f"{'='*60}")


if __name__ == '__main__':
    main()
