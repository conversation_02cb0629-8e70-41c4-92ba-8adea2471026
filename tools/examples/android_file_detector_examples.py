"""
Android设备文件检测示例
演示如何使用FileDetector检查Android设备上的文件
"""
import os
import sys
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

try:
    from core.logger import log
    from tools.file_detector import FileDetector
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)


def check_adb_connection():
    """检查ADB连接状态"""
    print("🔍 检查ADB连接状态")
    print("=" * 50)
    
    try:
        # 创建Android模式的检测器
        detector = FileDetector(use_adb=True)
        
        # 检查连接状态
        if detector._check_adb_connection():
            print("✅ ADB连接正常")
            return True
        else:
            print("❌ ADB连接失败")
            print("\n💡 请检查:")
            print("1. Android设备是否已连接并开启USB调试")
            print("2. ADB是否正常工作 (运行 'adb devices')")
            print("3. 设备是否信任此计算机")
            return False
    except Exception as e:
        print(f"❌ 检查ADB连接时发生错误: {e}")
        return False


def example_android_common_directories():
    """示例1: 查看Android设备常见目录"""
    print("\n📁 示例1: Android设备常见目录")
    print("=" * 50)
    
    try:
        detector = FileDetector(use_adb=True)
        common_dirs = detector.get_android_common_directories()
        
        if common_dirs:
            for category, paths in common_dirs.items():
                print(f"\n{category.upper()}:")
                for path in paths:
                    print(f"  ✅ {path}")
        else:
            print("未找到常见目录或设备未连接")
            
    except Exception as e:
        log.error(f"获取Android常见目录失败: {e}")


def example_check_android_camera():
    """示例2: 检查Android相机目录"""
    print("\n📷 示例2: 检查Android相机目录")
    print("=" * 50)
    
    try:
        detector = FileDetector(use_adb=True)
        today = datetime.now().strftime('%Y%m%d')
        
        print(f"检查日期: {today}")
        
        # 检查相机目录
        results = detector.check_camera_images(target_date=today)
        
        print(f"\n检测结果:")
        print(f"  目录: {results['directory']}")
        print(f"  目录存在: {'✅' if results['directory_exists'] else '❌'}")
        print(f"  总文件数: {results['total_files']}")
        print(f"  匹配文件数: {len(results['matched_files'])}")
        print(f"  检查模式: {results.get('device_mode', '未知')}")
        
        if results['matched_files']:
            print(f"\n今天拍摄的照片:")
            for i, file_info in enumerate(results['matched_files'][:5], 1):
                print(f"  {i}. {file_info['name']} ({file_info['size']} bytes)")
            
            if len(results['matched_files']) > 5:
                print(f"  ... 还有 {len(results['matched_files']) - 5} 张照片")
        else:
            print("  今天没有拍摄照片")
            
    except Exception as e:
        log.error(f"检查Android相机目录失败: {e}")


def example_check_android_screenshots():
    """示例3: 检查Android截图目录"""
    print("\n📱 示例3: 检查Android截图目录")
    print("=" * 50)
    
    try:
        detector = FileDetector(use_adb=True)
        today = datetime.now().strftime('%Y%m%d')
        
        # 检查截图目录
        screenshot_dirs = [
            "/sdcard/Pictures/Screenshots",
            "/storage/emulated/0/Pictures/Screenshots"
        ]
        
        for screenshot_dir in screenshot_dirs:
            print(f"\n检查目录: {screenshot_dir}")
            
            # 检查目录是否存在
            if detector._check_android_directory_exists(screenshot_dir):
                print("  ✅ 目录存在")
                
                # 查找截图文件
                matched_files = detector.find_datetime_files(
                    screenshot_dir, 
                    'Screenshot_YYYYMMDD-HHMMSS', 
                    today
                )
                
                print(f"  今天的截图数量: {len(matched_files)}")
                
                if matched_files:
                    for i, file_info in enumerate(matched_files[:3], 1):
                        print(f"    {i}. {file_info['name']}")
            else:
                print("  ❌ 目录不存在")
                
    except Exception as e:
        log.error(f"检查Android截图目录失败: {e}")


def example_check_specific_file():
    """示例4: 检查特定文件是否存在"""
    print("\n📄 示例4: 检查特定文件")
    print("=" * 50)
    
    try:
        detector = FileDetector(use_adb=True)
        
        # 要检查的文件列表
        test_files = [
            "/sdcard/DCIM/Camera/IMG_20250804_120000_001.jpg",
            "/sdcard/Pictures/Screenshots/Screenshot_20250804-120000.png",
            "/sdcard/Download/test.txt",
            "/system/build.prop"
        ]
        
        for file_path in test_files:
            exists = detector.check_file_exists(file_path)
            status = "✅ 存在" if exists else "❌ 不存在"
            print(f"  {file_path}: {status}")
            
    except Exception as e:
        log.error(f"检查特定文件失败: {e}")


def example_batch_check_dates():
    """示例5: 批量检查多个日期"""
    print("\n📅 示例5: 批量检查最近7天的照片")
    print("=" * 50)
    
    try:
        detector = FileDetector(use_adb=True)
        
        print("最近7天的拍照情况:")
        total_photos = 0
        
        for i in range(7):
            date = (datetime.now() - timedelta(days=i)).strftime('%Y%m%d')
            results = detector.check_camera_images(target_date=date)
            
            photo_count = len(results.get('matched_files', []))
            total_photos += photo_count
            
            date_str = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
            if i == 0:
                date_str += " (今天)"
            elif i == 1:
                date_str += " (昨天)"
            
            print(f"  {date_str}: {photo_count} 张照片")
        
        print(f"\n总计: {total_photos} 张照片")
        
    except Exception as e:
        log.error(f"批量检查日期失败: {e}")


def example_scan_android_directory():
    """示例6: 扫描Android目录"""
    print("\n📂 示例6: 扫描Android目录")
    print("=" * 50)
    
    try:
        detector = FileDetector(use_adb=True)
        
        # 扫描下载目录
        download_dir = "/sdcard/Download"
        
        if detector._check_android_directory_exists(download_dir):
            print(f"扫描目录: {download_dir}")
            
            files = detector.scan_directory(download_dir, recursive=False)
            
            print(f"找到 {len(files)} 个文件:")
            
            # 按文件类型分类
            file_types = {}
            for file_info in files:
                if 'error' not in file_info:
                    ext = file_info.get('extension', '').lower()
                    if ext not in file_types:
                        file_types[ext] = []
                    file_types[ext].append(file_info)
            
            for ext, files_list in file_types.items():
                ext_name = ext if ext else '无扩展名'
                print(f"\n  {ext_name} 文件 ({len(files_list)} 个):")
                for file_info in files_list[:3]:
                    size_mb = file_info['size'] / (1024 * 1024) if file_info['size'] > 0 else 0
                    print(f"    - {file_info['name']} ({size_mb:.2f} MB)")
                
                if len(files_list) > 3:
                    print(f"    ... 还有 {len(files_list) - 3} 个文件")
        else:
            print(f"目录不存在: {download_dir}")
            
    except Exception as e:
        log.error(f"扫描Android目录失败: {e}")


def main():
    """运行所有Android文件检测示例"""
    print("🤖 Android设备文件检测示例")
    print("=" * 60)
    
    # 首先检查ADB连接
    if not check_adb_connection():
        print("\n❌ 无法连接到Android设备，请检查连接后重试")
        return
    
    try:
        example_android_common_directories()
        example_check_android_camera()
        example_check_android_screenshots()
        example_check_specific_file()
        example_batch_check_dates()
        example_scan_android_directory()
        
        print("\n" + "=" * 60)
        print("✅ 所有Android文件检测示例运行完成！")
        
        print("\n💡 命令行使用提示:")
        print("# 检查Android设备相机目录")
        print("python tools/file_detector.py --android")
        print("\n# 列出连接的设备")
        print("python tools/file_detector.py --list-devices")
        print("\n# 显示常见目录")
        print("python tools/file_detector.py --android --common-dirs")
        print("\n# 检查特定设备的截图")
        print("python tools/file_detector.py --android --device-id YOUR_DEVICE_ID -d '/sdcard/Pictures/Screenshots' -p 'Screenshot_YYYYMMDD-HHMMSS'")
        
    except Exception as e:
        log.error(f"运行Android示例时发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
