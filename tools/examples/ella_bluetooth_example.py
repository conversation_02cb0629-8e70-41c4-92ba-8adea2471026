"""
Ella语音助手蓝牙控制示例
完整的示例：输入"open bluetooth"命令，验证响应和蓝牙状态
"""
import time
from core.logger import log
from pages.apps.ella.dialogue_page import EllaDialoguePage


class EllaBluetoothExample:
    """Ella蓝牙控制示例类"""
    
    def __init__(self):
        """初始化示例"""
        self.ella_page = EllaDialoguePage()
        self.test_results = {}
    
    def setup(self):
        """设置测试环境"""
        log.info("🔧 设置Ella测试环境")
        
        # 启动Ella应用
        if not self.ella_page.start_app():
            raise Exception("Ella应用启动失败")
        
        # 等待页面加载
        if not self.ella_page.wait_for_page_load(timeout=10):
            raise Exception("Ella页面加载失败")
        
        log.info("✅ Ella应用启动成功")
        
        # 记录初始状态
        self.ella_page.screenshot("ella_setup_complete.png")
    
    def teardown(self):
        """清理测试环境"""
        log.info("🧹 清理Ella测试环境")
        self.ella_page.stop_app()
    
    def test_open_bluetooth_command(self):
        """
        测试open bluetooth命令
        
        测试步骤:
        1. 记录蓝牙初始状态
        2. 输入"open bluetooth"命令
        3. 验证AI响应内容
        4. 验证蓝牙最终状态
        
        预期结果:
        - AI响应包含"open bluetooth"相关内容
        - 蓝牙状态为开启
        """
        log.info("🧪 测试open bluetooth命令")
        log.info("=" * 60)
        
        command = "open bluetooth"
        
        # 步骤1: 记录蓝牙初始状态
        log.info("1️⃣ 记录蓝牙初始状态")
        initial_bluetooth_status = self.ella_page.check_bluetooth_status()
        log.info(f"蓝牙初始状态: {'开启' if initial_bluetooth_status else '关闭'}")
        
        self.test_results['initial_bluetooth_status'] = initial_bluetooth_status
        
        # 截图记录初始状态
        self.ella_page.screenshot("bluetooth_test_initial.png")
        
        # 步骤2: 输入命令
        log.info(f"2️⃣ 输入命令: {command}")
        
        # 执行文本命令
        if not self.ella_page.execute_text_command(command):
            raise Exception(f"命令执行失败: {command}")
        
        log.info("✅ 命令发送成功")
        
        # 截图记录命令发送后状态
        self.ella_page.screenshot("bluetooth_command_sent.png")
        
        # 步骤3: 等待并获取AI响应
        log.info("3️⃣ 等待AI响应")
        
        if not self.ella_page.wait_for_response(timeout=20):
            log.warning("⚠️ 未检测到AI响应，继续获取文本")
        
        # 等待额外时间确保响应完整
        time.sleep(3)
        
        # 获取响应文本
        response_text = self.ella_page.get_response_text()
        log.info(f"AI响应内容: {response_text}")
        
        self.test_results['response_text'] = response_text
        
        # 截图记录响应状态
        self.ella_page.screenshot("bluetooth_response_received.png")
        
        # 步骤4: 验证响应内容
        log.info("4️⃣ 验证响应内容")
        
        # 灵活的响应验证
        response_valid = self._validate_response(command, response_text)
        self.test_results['response_valid'] = response_valid
        
        if response_valid:
            log.info("✅ 响应验证通过")
        else:
            log.warning("⚠️ 响应验证未完全通过，但继续测试")
        
        # 步骤5: 验证蓝牙状态
        log.info("5️⃣ 验证蓝牙状态")
        
        # 等待蓝牙状态可能的变化
        time.sleep(2)
        
        final_bluetooth_status = self.ella_page.check_bluetooth_status()
        log.info(f"蓝牙最终状态: {'开启' if final_bluetooth_status else '关闭'}")
        
        self.test_results['final_bluetooth_status'] = final_bluetooth_status
        
        # 最终截图
        self.ella_page.screenshot("bluetooth_test_final.png")
        
        # 步骤6: 生成测试报告
        self._generate_test_report(command)
        
        return self.test_results
    
    def _validate_response(self, command: str, response: str) -> bool:
        """
        验证AI响应
        
        Args:
            command: 原始命令
            response: AI响应
            
        Returns:
            bool: 响应是否有效
        """
        if not response:
            log.warning("响应为空")
            return False
        
        # 多种验证策略
        validations = []
        
        # 1. 检查是否包含命令关键词
        command_keywords = ["open", "bluetooth", "蓝牙"]
        found_keywords = []
        
        response_lower = response.lower()
        for keyword in command_keywords:
            if keyword.lower() in response_lower:
                found_keywords.append(keyword)
        
        if found_keywords:
            log.info(f"✅ 响应包含关键词: {found_keywords}")
            validations.append(True)
        else:
            log.warning(f"⚠️ 响应未包含预期关键词: {command_keywords}")
            validations.append(False)
        
        # 2. 检查响应长度（有效响应通常不会太短）
        if len(response) > 5:
            log.info(f"✅ 响应长度合理: {len(response)} 字符")
            validations.append(True)
        else:
            log.warning(f"⚠️ 响应过短: {len(response)} 字符")
            validations.append(False)
        
        # 3. 检查是否包含蓝牙相关词汇
        bluetooth_terms = ["蓝牙", "bluetooth", "设置", "开启", "打开", "已", "成功"]
        bluetooth_found = any(term in response_lower for term in bluetooth_terms)
        
        if bluetooth_found:
            log.info("✅ 响应包含蓝牙相关内容")
            validations.append(True)
        else:
            log.warning("⚠️ 响应未包含明显的蓝牙相关内容")
            validations.append(False)
        
        # 综合判断：至少50%的验证通过
        success_rate = sum(validations) / len(validations)
        log.info(f"响应验证成功率: {success_rate:.1%}")
        
        return success_rate >= 0.5
    
    def _generate_test_report(self, command: str):
        """生成测试报告"""
        log.info("📊 生成测试报告")
        log.info("=" * 60)
        
        # 基本信息
        log.info(f"测试命令: {command}")
        log.info(f"AI响应: {self.test_results.get('response_text', 'N/A')}")
        
        # 蓝牙状态
        initial_status = self.test_results.get('initial_bluetooth_status', False)
        final_status = self.test_results.get('final_bluetooth_status', False)
        
        log.info(f"蓝牙初始状态: {'开启' if initial_status else '关闭'}")
        log.info(f"蓝牙最终状态: {'开启' if final_status else '关闭'}")
        log.info(f"状态变化: {'是' if initial_status != final_status else '否'}")
        
        # 验证结果
        response_valid = self.test_results.get('response_valid', False)
        log.info(f"响应验证: {'通过' if response_valid else '未通过'}")
        
        # 总体评估
        bluetooth_ok = final_status  # 蓝牙最终应该是开启的
        
        if bluetooth_ok and response_valid:
            test_result = "完全成功"
            log.info("🎉 测试结果: 完全成功")
        elif bluetooth_ok:
            test_result = "部分成功"
            log.info("✅ 测试结果: 部分成功（蓝牙功能正常）")
        else:
            test_result = "需要检查"
            log.info("⚠️ 测试结果: 需要检查")
        
        self.test_results['test_result'] = test_result
        
        # 断言结果
        log.info("\n🎯 断言验证:")
        
        # 断言1: 响应不为空
        assert self.test_results.get('response_text'), "❌ 断言失败: AI响应为空"
        log.info("✅ 断言1通过: AI响应不为空")
        
        # 断言2: 响应包含相关内容（灵活验证）
        if response_valid:
            log.info("✅ 断言2通过: 响应包含相关内容")
        else:
            log.warning("⚠️ 断言2警告: 响应内容需要进一步验证")
        
        # 断言3: 蓝牙状态正确
        assert final_status, "❌ 断言失败: 蓝牙未开启"
        log.info("✅ 断言3通过: 蓝牙已开启")
        
        log.info(f"\n🏆 最终结果: {test_result}")


def main():
    """主函数 - 运行完整的Ella蓝牙控制示例"""
    log.info("🚀 Ella语音助手蓝牙控制示例")
    log.info("=" * 80)
    
    example = EllaBluetoothExample()
    
    try:
        # 设置环境
        example.setup()
        
        # 运行测试
        results = example.test_open_bluetooth_command()
        
        # 输出最终结果
        log.info("\n" + "=" * 80)
        log.info("🎯 示例执行完成")
        log.info("=" * 80)
        
        log.info("📋 执行总结:")
        log.info(f"  - 命令执行: 成功")
        log.info(f"  - AI响应: {results.get('response_text', 'N/A')}")
        log.info(f"  - 响应验证: {'通过' if results.get('response_valid') else '未通过'}")
        log.info(f"  - 蓝牙状态: {'开启' if results.get('final_bluetooth_status') else '关闭'}")
        log.info(f"  - 测试结果: {results.get('test_result', 'N/A')}")
        
        log.info("\n💡 使用说明:")
        log.info("1. 此示例展示了如何使用Ella进行蓝牙控制")
        log.info("2. 包含完整的输入、响应验证和状态检查")
        log.info("3. 可以作为其他Ella功能测试的模板")
        log.info("4. 支持灵活的响应验证策略")
        
        log.info("\n📁 生成的截图:")
        log.info("  - ella_setup_complete.png (初始设置)")
        log.info("  - bluetooth_test_initial.png (测试开始)")
        log.info("  - bluetooth_command_sent.png (命令发送)")
        log.info("  - bluetooth_response_received.png (响应接收)")
        log.info("  - bluetooth_test_final.png (测试完成)")
        
    except Exception as e:
        log.error(f"❌ 示例执行失败: {e}")
        
    finally:
        # 清理环境
        example.teardown()


if __name__ == "__main__":
    main()
