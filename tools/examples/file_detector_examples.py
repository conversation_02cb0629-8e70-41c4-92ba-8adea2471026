"""
文件检测模块使用示例
演示如何使用FileDetector进行各种文件检测操作
"""
import os
import sys
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

try:
    from core.logger import log
    from tools.file_detector import FileDetector
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)


def example_basic_file_check():
    """示例1: 基础文件检查"""
    print("\n=== 示例1: 基础文件检查 ===")
    
    detector = FileDetector()
    
    # 检查当前目录下的文件
    test_files = [
        "tools/file_detector.py",
        "config/config.yaml",
        "README.md",
        "non_existent_file.txt"
    ]
    
    for file_path in test_files:
        exists = detector.check_file_exists(file_path)
        print(f"文件 {file_path}: {'✅ 存在' if exists else '❌ 不存在'}")


def example_directory_scan():
    """示例2: 目录扫描"""
    print("\n=== 示例2: 目录扫描 ===")
    
    detector = FileDetector()
    
    # 扫描tools目录
    files = detector.scan_directory("tools", recursive=False)
    
    print(f"tools目录下共有 {len(files)} 个文件:")
    for file_info in files[:5]:  # 只显示前5个
        if 'error' not in file_info:
            print(f"  - {file_info['name']} ({file_info['size']} bytes)")
    
    if len(files) > 5:
        print(f"  ... 还有 {len(files) - 5} 个文件")


def example_datetime_pattern_detection():
    """示例3: 日期时间戳模式检测"""
    print("\n=== 示例3: 日期时间戳模式检测 ===")
    
    detector = FileDetector()
    
    # 创建测试目录和文件（模拟）
    test_dir = "tools/output"
    os.makedirs(test_dir, exist_ok=True)
    
    # 模拟创建一些测试文件名
    today = datetime.now().strftime('%Y%m%d')
    yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
    
    test_filenames = [
        f"IMG_{today}_194815_970.jpg",
        f"IMG_{today}_120530_123.png",
        f"IMG_{yesterday}_083045_456.jpg",
        f"{today}_194815.jpg",
        f"Screenshot_{today}-194815.png",
        "regular_file.txt"
    ]
    
    print(f"模拟检测以下文件名模式 (目标日期: {today}):")
    for filename in test_filenames:
        print(f"  - {filename}")
    
    # 检查各种模式
    patterns = list(FileDetector.DATETIME_PATTERNS.keys())
    for pattern in patterns:
        print(f"\n使用模式 '{pattern}' 检测:")
        # 这里只是演示模式匹配，实际使用时需要真实文件
        import re
        pattern_regex = FileDetector.DATETIME_PATTERNS[pattern]
        
        for filename in test_filenames:
            match = re.match(pattern_regex, filename, re.IGNORECASE)
            if match:
                print(f"  ✅ 匹配: {filename}")


def example_camera_directory_check():
    """示例4: 相机目录检查"""
    print("\n=== 示例4: 相机目录检查 ===")
    
    detector = FileDetector()
    
    # 检查相机目录（这里使用tools/output作为示例目录）
    camera_dir = "tools/output"
    today = datetime.now().strftime('%Y%m%d')
    
    print(f"检查目录: {camera_dir}")
    print(f"目标日期: {today}")
    
    results = detector.check_camera_images(camera_dir, today)
    
    print(f"\n检测结果:")
    print(f"  目录存在: {'✅' if results.get('directory_exists') else '❌'}")
    print(f"  总文件数: {results.get('total_files', 0)}")
    print(f"  匹配文件数: {len(results.get('matched_files', []))}")
    print(f"  检查的模式: {', '.join(results.get('patterns_checked', []))}")
    
    # 导出结果
    output_file = detector.export_results(results)
    print(f"  结果已导出到: {output_file}")


def example_custom_patterns():
    """示例5: 自定义模式检测"""
    print("\n=== 示例5: 支持的文件名模式 ===")
    
    patterns = FileDetector.DATETIME_PATTERNS
    
    print("支持的日期时间戳模式:")
    for pattern_name, regex in patterns.items():
        print(f"\n模式名: {pattern_name}")
        print(f"正则表达式: {regex}")
        
        # 生成示例文件名
        today = datetime.now()
        if pattern_name == 'IMG_YYYYMMDD_HHMMSS_SSS':
            example = f"IMG_{today.strftime('%Y%m%d')}_{today.strftime('%H%M%S')}_970.jpg"
        elif pattern_name == 'YYYYMMDD_HHMMSS':
            example = f"{today.strftime('%Y%m%d')}_{today.strftime('%H%M%S')}.jpg"
        elif pattern_name == 'YYYY-MM-DD_HH-MM-SS':
            example = f"{today.strftime('%Y-%m-%d')}_{today.strftime('%H-%M-%S')}.jpg"
        elif pattern_name == 'Screenshot_YYYYMMDD-HHMMSS':
            example = f"Screenshot_{today.strftime('%Y%m%d')}-{today.strftime('%H%M%S')}.png"
        else:
            example = "示例文件名"
        
        print(f"示例文件名: {example}")


def example_image_file_filtering():
    """示例6: 图片文件过滤"""
    print("\n=== 示例6: 图片文件过滤 ===")
    
    detector = FileDetector()
    
    print("支持的图片格式:")
    for ext in sorted(detector.IMAGE_EXTENSIONS):
        print(f"  - {ext}")
    
    # 扫描目录并过滤图片文件
    files = detector.scan_directory("tools", recursive=True)
    image_files = [f for f in files if f.get('is_image', False)]
    
    print(f"\ntools目录下的图片文件 ({len(image_files)} 个):")
    for img_file in image_files:
        if 'error' not in img_file:
            print(f"  - {img_file['name']} ({img_file['extension']})")


def main():
    """运行所有示例"""
    print("文件检测模块使用示例")
    print("=" * 50)
    
    try:
        example_basic_file_check()
        example_directory_scan()
        example_datetime_pattern_detection()
        example_camera_directory_check()
        example_custom_patterns()
        example_image_file_filtering()
        
        print("\n" + "=" * 50)
        print("所有示例运行完成！")
        
    except Exception as e:
        log.error(f"运行示例时发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
