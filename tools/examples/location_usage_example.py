#!/usr/bin/env python3
"""
位置服务状态获取功能使用示例
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


class LocationManager:
    """位置服务管理器示例类"""
    
    def __init__(self):
        self.checker = SystemStatusChecker()
    
    def is_location_available(self) -> bool:
        """检查位置服务是否可用"""
        return self.checker.check_location_status()
    
    def get_location_mode(self) -> str:
        """获取当前位置模式描述"""
        detailed_status = self.checker.get_location_detailed_status()
        return detailed_status.get('location_mode_description', '未知')
    
    def is_gps_enabled(self) -> bool:
        """检查GPS是否启用"""
        detailed_status = self.checker.get_location_detailed_status()
        return detailed_status.get('gps_enabled', False)
    
    def is_network_location_enabled(self) -> bool:
        """检查网络定位是否启用"""
        detailed_status = self.checker.get_location_detailed_status()
        return detailed_status.get('network_location_enabled', False)
    
    def is_high_accuracy_mode(self) -> bool:
        """检查是否为高精度模式"""
        detailed_status = self.checker.get_location_detailed_status()
        return detailed_status.get('location_mode', 0) == 3
    
    def is_mock_location_enabled(self) -> bool:
        """检查是否启用了模拟位置"""
        detailed_status = self.checker.get_location_detailed_status()
        return detailed_status.get('mock_location_enabled', False)
    
    def get_location_providers(self) -> list:
        """获取可用的位置提供者"""
        detailed_status = self.checker.get_location_detailed_status()
        return detailed_status.get('providers_allowed', [])
    
    def analyze_location_status(self) -> dict:
        """分析位置服务状态并返回建议"""
        detailed_status = self.checker.get_location_detailed_status()
        
        analysis = {
            'location_available': False,
            'accuracy_level': 'none',
            'battery_impact': 'low',
            'indoor_capability': False,
            'outdoor_capability': False,
            'privacy_risk': 'low',
            'recommendations': [],
            'warnings': []
        }
        
        location_enabled = detailed_status.get('location_enabled', False)
        location_mode = detailed_status.get('location_mode', 0)
        gps_enabled = detailed_status.get('gps_enabled', False)
        network_enabled = detailed_status.get('network_location_enabled', False)
        mock_enabled = detailed_status.get('mock_location_enabled', False)
        
        # 分析位置服务可用性
        analysis['location_available'] = location_enabled
        
        # 分析精度级别
        if location_mode == 3:  # 高精度
            analysis['accuracy_level'] = 'high'
            analysis['battery_impact'] = 'high'
            analysis['indoor_capability'] = True
            analysis['outdoor_capability'] = True
        elif location_mode == 2:  # 省电模式
            analysis['accuracy_level'] = 'medium'
            analysis['battery_impact'] = 'low'
            analysis['indoor_capability'] = True
            analysis['outdoor_capability'] = True
        elif location_mode == 1:  # 仅设备
            analysis['accuracy_level'] = 'high'
            analysis['battery_impact'] = 'medium'
            analysis['indoor_capability'] = False
            analysis['outdoor_capability'] = True
        else:  # 关闭
            analysis['accuracy_level'] = 'none'
            analysis['battery_impact'] = 'none'
        
        # 隐私风险评估
        if gps_enabled and network_enabled:
            analysis['privacy_risk'] = 'high'
        elif network_enabled:
            analysis['privacy_risk'] = 'medium'
        elif gps_enabled:
            analysis['privacy_risk'] = 'low'
        
        # 生成建议
        if not location_enabled:
            analysis['recommendations'].append("开启位置服务以使用导航和基于位置的功能")
        elif location_mode == 1:
            analysis['recommendations'].append("考虑开启网络定位以提高室内定位能力")
        elif location_mode == 2:
            analysis['recommendations'].append("需要高精度导航时可切换到高精度模式")
        
        # 生成警告
        if mock_enabled:
            analysis['warnings'].append("检测到模拟位置已启用，某些应用可能无法正常工作")
        
        if location_mode == 3:
            analysis['warnings'].append("高精度模式会增加电池消耗")
        
        return analysis
    
    def print_location_report(self):
        """打印位置服务报告"""
        print("=" * 70)
        print("位置服务状态报告")
        print("=" * 70)
        
        # 获取详细状态
        detailed_status = self.checker.get_location_detailed_status()
        analysis = self.analyze_location_status()
        
        # 基本信息
        print(f"\n📍 基本信息:")
        print(f"  检测方法: {detailed_status.get('detection_method', 'Unknown')}")
        print(f"  位置服务: {'✅ 开启' if detailed_status.get('location_enabled', False) else '❌ 关闭'}")
        print(f"  位置模式: {detailed_status.get('location_mode_description', 'Unknown')}")
        print(f"  模式值: {detailed_status.get('location_mode', -1)}")
        
        # 提供者信息
        print(f"\n🛰️ 位置提供者:")
        providers = detailed_status.get('providers_allowed', [])
        if providers:
            print(f"  允许的提供者: {', '.join(providers)}")
        else:
            print(f"  允许的提供者: 无")
        
        active_providers = detailed_status.get('active_providers', [])
        if active_providers:
            print(f"  活跃的提供者: {', '.join(active_providers)}")
        else:
            print(f"  活跃的提供者: 无")
        
        print(f"  GPS: {'✅ 启用' if detailed_status.get('gps_enabled', False) else '❌ 禁用'}")
        print(f"  网络定位: {'✅ 启用' if detailed_status.get('network_location_enabled', False) else '❌ 禁用'}")
        print(f"  被动定位: {'✅ 启用' if detailed_status.get('passive_location_enabled', False) else '❌ 禁用'}")
        
        # 位置信息
        print(f"\n📍 位置信息:")
        last_location = detailed_status.get('last_known_location')
        if last_location:
            print(f"  最后位置: 纬度 {last_location.get('latitude', 'N/A')}, 经度 {last_location.get('longitude', 'N/A')}")
        else:
            print(f"  最后位置: 无记录")
        
        accuracy = detailed_status.get('location_accuracy')
        print(f"  位置精度: {accuracy if accuracy else '未知'}")
        
        # 开发者选项
        print(f"\n🔧 开发者选项:")
        print(f"  模拟位置: {'⚠️ 启用' if detailed_status.get('mock_location_enabled', False) else '✅ 禁用'}")
        
        # 分析结果
        print(f"\n📊 状态分析:")
        print(f"  位置可用性: {'✅ 可用' if analysis['location_available'] else '❌ 不可用'}")
        print(f"  精度级别: {analysis['accuracy_level']}")
        print(f"  电池影响: {analysis['battery_impact']}")
        print(f"  室内定位: {'✅ 支持' if analysis['indoor_capability'] else '❌ 不支持'}")
        print(f"  户外定位: {'✅ 支持' if analysis['outdoor_capability'] else '❌ 不支持'}")
        print(f"  隐私风险: {analysis['privacy_risk']}")
        
        # 建议和警告
        if analysis['recommendations']:
            print(f"\n💡 建议:")
            for rec in analysis['recommendations']:
                print(f"  • {rec}")
        
        if analysis['warnings']:
            print(f"\n⚠️ 警告:")
            for warning in analysis['warnings']:
                print(f"  • {warning}")
        
        print("\n" + "=" * 70)


def main():
    """主函数示例"""
    
    # 创建位置管理器
    location_manager = LocationManager()
    
    # 打印详细报告
    location_manager.print_location_report()
    
    # 简单使用示例
    print("\n📱 简单使用示例:")
    print("-" * 40)
    
    # 检查位置服务是否可用
    if location_manager.is_location_available():
        print("✅ 位置服务可用")
        
        # 获取位置模式
        mode = location_manager.get_location_mode()
        print(f"📍 当前模式: {mode}")
        
        # 检查具体功能
        if location_manager.is_gps_enabled():
            print("🛰️ GPS已启用")
        
        if location_manager.is_network_location_enabled():
            print("🌐 网络定位已启用")
        
        if location_manager.is_high_accuracy_mode():
            print("🎯 高精度模式已启用")
        
        # 检查模拟位置
        if location_manager.is_mock_location_enabled():
            print("⚠️ 检测到模拟位置")
        
        # 获取提供者
        providers = location_manager.get_location_providers()
        if providers:
            print(f"📡 可用提供者: {', '.join(providers)}")
        
    else:
        print("❌ 位置服务不可用")
        print("💡 请在设置中开启位置服务")


if __name__ == "__main__":
    main()
