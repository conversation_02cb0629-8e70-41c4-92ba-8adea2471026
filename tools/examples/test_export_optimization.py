"""
测试导出功能优化
演示output目录的自动管理功能
"""
import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

try:
    from core.logger import log
except ImportError:
    class SimpleLogger:
        def info(self, msg): print(f"[INFO] {msg}")
        def error(self, msg): print(f"[ERROR] {msg}")
        def warning(self, msg): print(f"[WARNING] {msg}")
    log = SimpleLogger()


def run_export_test(command: str, description: str):
    """运行导出测试"""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"命令: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command.split(),
            cwd=project_root,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            # 只显示关键输出行
            lines = result.stdout.split('\n')
            for line in lines:
                if any(keyword in line for keyword in ['✅', '📱', '💡', '已导出到']):
                    print(line)
        else:
            print(f"❌ 命令执行失败: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("❌ 命令执行超时")
    except Exception as e:
        print(f"❌ 执行出错: {e}")


def check_output_directory():
    """检查output目录和文件"""
    output_dir = Path(project_root) / "tools" / "output"
    
    print(f"\n{'='*60}")
    print("📁 检查output目录")
    print(f"{'='*60}")
    
    if output_dir.exists():
        print(f"✅ output目录存在: {output_dir}")
        
        # 列出所有文件
        files = list(output_dir.glob("*.json"))
        if files:
            print(f"\n📄 导出文件列表 ({len(files)} 个):")
            for i, file in enumerate(files, 1):
                file_size = file.stat().st_size
                print(f"  {i}. {file.name} ({file_size} bytes)")
                
                # 显示文件内容预览
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.split('\n')
                        preview_lines = lines[:3]  # 显示前3行
                        print(f"     预览: {' '.join(preview_lines)[:80]}...")
                except Exception as e:
                    print(f"     预览失败: {e}")
        else:
            print("📄 output目录为空")
    else:
        print(f"❌ output目录不存在: {output_dir}")


def test_path_handling():
    """测试路径处理功能"""
    print(f"\n{'='*60}")
    print("🔧 测试路径处理功能")
    print(f"{'='*60}")
    
    # 测试不同的路径格式
    test_cases = [
        ("simple_name.json", "简单文件名"),
        ("path/to/file.json", "相对路径"),
        ("/absolute/path/file.json", "绝对路径"),
        ("C:\\Windows\\file.json", "Windows绝对路径"),
    ]
    
    for filename, description in test_cases:
        print(f"\n🧪 测试 {description}: {filename}")
        
        # 模拟路径处理
        from tools.adb_process_monitor import AdbProcessMonitor
        monitor = AdbProcessMonitor()
        
        try:
            output_path = monitor._get_output_path(filename)
            expected_name = Path(filename).name
            actual_name = output_path.name
            
            if actual_name == expected_name:
                print(f"   ✅ 路径处理正确: {output_path}")
            else:
                print(f"   ❌ 路径处理错误: 期望 {expected_name}, 实际 {actual_name}")
                
        except Exception as e:
            print(f"   ❌ 路径处理失败: {e}")


def main():
    """主函数"""
    
    print("🚀 ADB进程监控工具 - 导出功能优化测试")
    print("=" * 60)
    
    try:
        # 检查设备连接
        print("🔍 检查设备连接...")
        result = subprocess.run(["adb", "devices"], capture_output=True, text=True, timeout=10)
        if "device" not in result.stdout:
            print("❌ 未检测到设备，跳过实际测试")
            print("💡 将只进行路径处理功能测试")
            test_path_handling()
            return
        
        print("✅ 设备连接正常")
        
        # 测试1: 简单校验导出
        run_export_test(
            "python tools/adb_process_monitor.py --is-running com.android.settings --export settings_simple.json",
            "简单校验结果导出测试"
        )
        
        # 测试2: 详细状态导出
        run_export_test(
            "python tools/adb_process_monitor.py --check-package com.android.chrome --export chrome_detailed.json",
            "详细状态信息导出测试"
        )
        
        # 测试3: 进程列表导出
        run_export_test(
            "python tools/adb_process_monitor.py --filter android --export android_processes.json",
            "进程列表导出测试"
        )
        
        # 测试4: 路径处理测试
        run_export_test(
            "python tools/adb_process_monitor.py --is-running com.android.settings --export /some/deep/path/settings_path_test.json",
            "路径处理功能测试"
        )
        
        # 检查output目录
        check_output_directory()
        
        # 测试路径处理功能
        test_path_handling()
        
        print(f"\n{'='*60}")
        print("🎉 导出功能优化测试完成！")
        print("\n💡 优化特性总结:")
        print("✅ 所有导出文件自动存储到 tools/output/ 目录")
        print("✅ output目录会在首次使用时自动创建")
        print("✅ 路径安全处理，只使用文件名部分")
        print("✅ 支持所有导出功能：简单校验、详细状态、进程列表")
        print("✅ 统一的文件管理，便于查找和整理")
        print(f"{'='*60}")
        
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")


if __name__ == '__main__':
    main()
