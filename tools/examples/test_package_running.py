"""
测试包运行状态校验功能
演示如何使用 is_package_running 方法
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

try:
    from core.logger import log
except ImportError:
    class SimpleLogger:
        def info(self, msg): print(f"[INFO] {msg}")
        def error(self, msg): print(f"[ERROR] {msg}")
        def warning(self, msg): print(f"[WARNING] {msg}")
    log = SimpleLogger()

# 导入ADB进程监控器
from tools.adb_process_monitor import AdbProcessMonitor


def test_package_running_status():
    """测试包运行状态校验功能"""
    
    print("📱 包运行状态校验功能测试")
    print("=" * 50)
    
    # 创建监控器实例
    monitor = AdbProcessMonitor()
    
    # 测试用例列表
    test_packages = [
        "com.android.chrome",           # 通常在运行的浏览器
        "com.android.settings",         # 系统设置
        "com.google.android.gms",       # Google服务
        "com.example.nonexistent",      # 不存在的应用
        "com.fake.app.test"             # 另一个不存在的应用
    ]
    
    print("\n🔍 开始测试各个包名的运行状态...")
    
    results = []
    for package_name in test_packages:
        print(f"\n📦 测试包名: {package_name}")
        
        try:
            # 调用校验方法
            is_running = monitor.is_package_running(package_name)
            
            # 显示结果
            status_icon = "✅" if is_running else "❌"
            status_text = "正在运行" if is_running else "未运行"
            
            print(f"   {status_icon} 结果: {is_running} ({status_text})")
            
            results.append({
                'package': package_name,
                'is_running': is_running,
                'status': status_text
            })
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            results.append({
                'package': package_name,
                'is_running': False,
                'status': f"错误: {e}"
            })
    
    # 显示汇总结果
    print(f"\n📊 测试结果汇总:")
    print("-" * 50)
    print(f"{'包名':<30} {'状态':<10} {'结果'}")
    print("-" * 50)
    
    running_count = 0
    for result in results:
        package = result['package'][:28] + ".." if len(result['package']) > 30 else result['package']
        status = "True" if result['is_running'] else "False"
        description = result['status']
        
        print(f"{package:<30} {status:<10} {description}")
        
        if result['is_running']:
            running_count += 1
    
    print("-" * 50)
    print(f"总计测试: {len(results)} 个包")
    print(f"正在运行: {running_count} 个包")
    print(f"未运行: {len(results) - running_count} 个包")
    
    return results


def test_function_usage():
    """演示函数的不同使用方式"""
    
    print(f"\n{'='*50}")
    print("📚 函数使用方式演示")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 方式1: 直接调用方法
    print("\n1️⃣ 直接调用方法:")
    package = "com.android.chrome"
    result = monitor.is_package_running(package)
    print(f"   monitor.is_package_running('{package}') -> {result}")
    
    # 方式2: 在条件判断中使用
    print("\n2️⃣ 在条件判断中使用:")
    if monitor.is_package_running("com.android.settings"):
        print("   ✅ 设置应用正在运行，可以进行相关操作")
    else:
        print("   ❌ 设置应用未运行，需要先启动")
    
    # 方式3: 批量检查
    print("\n3️⃣ 批量检查多个应用:")
    apps_to_check = ["com.android.chrome", "com.android.settings", "com.google.android.gms"]
    running_apps = []
    
    for app in apps_to_check:
        if monitor.is_package_running(app):
            running_apps.append(app)
    
    print(f"   正在运行的应用: {len(running_apps)}/{len(apps_to_check)}")
    for app in running_apps:
        print(f"   ✅ {app}")
    
    # 方式4: 用于自动化测试
    print("\n4️⃣ 自动化测试场景:")
    target_app = "com.android.chrome"
    
    print(f"   检查目标应用 {target_app} 是否运行...")
    if monitor.is_package_running(target_app):
        print("   ✅ 应用已运行，可以继续测试")
        print("   💡 可以进行UI交互、性能监控等操作")
    else:
        print("   ❌ 应用未运行，需要先启动应用")
        print("   💡 建议先启动应用再进行测试")


def main():
    """主函数"""
    
    try:
        # 检查设备连接
        print("🔍 检查设备连接...")
        import subprocess
        result = subprocess.run(["adb", "devices"], capture_output=True, text=True, timeout=10)
        if "device" not in result.stdout:
            print("❌ 未检测到设备，请确保:")
            print("1. 设备已连接并开启USB调试")
            print("2. 设备已信任此计算机")
            return
        
        print("✅ 设备连接正常")
        
        # 运行测试
        test_package_running_status()
        test_function_usage()
        
        print(f"\n{'='*50}")
        print("🎉 测试完成！")
        print("\n💡 使用建议:")
        print("- 在自动化测试中使用此方法验证应用状态")
        print("- 结合其他工具进行应用性能监控")
        print("- 用于CI/CD流程中的应用状态检查")
        print("- 可以扩展为定时监控脚本")
        
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")


if __name__ == '__main__':
    main()
