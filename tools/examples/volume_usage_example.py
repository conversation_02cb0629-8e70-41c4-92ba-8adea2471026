#!/usr/bin/env python3
"""
系统音量获取功能使用示例
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


class VolumeManager:
    """音量管理器示例类"""
    
    def __init__(self):
        self.checker = SystemStatusChecker()
    
    def get_current_media_volume(self) -> int:
        """获取当前媒体音量"""
        return self.checker.get_system_volume("music")
    
    def get_current_ring_volume(self) -> int:
        """获取当前铃声音量"""
        return self.checker.get_system_volume("ring")
    
    def get_all_volumes(self) -> dict:
        """获取所有类型的音量"""
        volumes = {}
        volume_types = ["music", "ring", "alarm", "notification", "system", "voice_call"]
        
        for vol_type in volume_types:
            volumes[vol_type] = self.checker.get_system_volume(vol_type)
        
        return volumes
    
    def check_volume_status(self) -> dict:
        """检查音量状态并返回分析结果"""
        detailed_status = self.checker.get_system_volume_detailed_status()
        
        analysis = {
            'has_sound': False,
            'media_ready': False,
            'ring_audible': False,
            'alarm_audible': False,
            'any_muted': False,
            'volume_summary': {}
        }
        
        # 分析各种音量状态
        music_vol = detailed_status.get('music_volume', -1)
        ring_vol = detailed_status.get('ring_volume', -1)
        alarm_vol = detailed_status.get('alarm_volume', -1)
        
        # 检查是否有声音输出
        if music_vol > 0 or ring_vol > 0 or alarm_vol > 0:
            analysis['has_sound'] = True
        
        # 检查媒体播放是否就绪
        if music_vol > 0:
            analysis['media_ready'] = True
        
        # 检查铃声是否可听见
        if ring_vol > 0:
            analysis['ring_audible'] = True
        
        # 检查闹钟是否可听见
        if alarm_vol > 0:
            analysis['alarm_audible'] = True
        
        # 检查是否有静音
        mute_status = detailed_status.get('mute_status', {})
        if any(mute_status.values()):
            analysis['any_muted'] = True
        
        # 音量摘要
        for vol_type in ["music", "ring", "alarm", "notification", "system", "voice_call"]:
            volume = detailed_status.get(f'{vol_type}_volume', -1)
            percentage = detailed_status.get('volume_percentages', {}).get(vol_type, None)
            is_muted = detailed_status.get('mute_status', {}).get(vol_type, False)
            
            analysis['volume_summary'][vol_type] = {
                'volume': volume,
                'percentage': percentage,
                'muted': is_muted,
                'status': 'muted' if is_muted else ('silent' if volume == 0 else ('low' if volume < 5 else ('medium' if volume < 10 else 'high')))
            }
        
        return analysis
    
    def print_volume_report(self):
        """打印音量报告"""
        print("=" * 60)
        print("系统音量状态报告")
        print("=" * 60)
        
        # 获取详细状态
        detailed_status = self.checker.get_system_volume_detailed_status()
        analysis = self.check_volume_status()
        
        # 基本信息
        print(f"\n📊 基本信息:")
        print(f"  检测方法: {detailed_status.get('detection_method', 'Unknown')}")
        print(f"  音频模式: {detailed_status.get('audio_mode', 'Unknown')}")
        print(f"  音频焦点: {detailed_status.get('current_audio_focus', 'Unknown')}")
        
        # 音量状态概览
        print(f"\n🔊 音量状态概览:")
        print(f"  系统有声音: {'✅ 是' if analysis['has_sound'] else '❌ 否'}")
        print(f"  媒体播放就绪: {'✅ 是' if analysis['media_ready'] else '❌ 否'}")
        print(f"  铃声可听见: {'✅ 是' if analysis['ring_audible'] else '❌ 否'}")
        print(f"  闹钟可听见: {'✅ 是' if analysis['alarm_audible'] else '❌ 否'}")
        print(f"  有静音项目: {'⚠️ 是' if analysis['any_muted'] else '✅ 否'}")
        
        # 详细音量信息
        print(f"\n🎵 详细音量信息:")
        volume_types = ["music", "ring", "alarm", "notification", "system", "voice_call"]
        type_names = {
            "music": "媒体音量",
            "ring": "铃声音量", 
            "alarm": "闹钟音量",
            "notification": "通知音量",
            "system": "系统音量",
            "voice_call": "通话音量"
        }
        
        for vol_type in volume_types:
            summary = analysis['volume_summary'][vol_type]
            volume = summary['volume']
            percentage = summary['percentage']
            status = summary['status']
            muted = summary['muted']
            
            status_icon = {
                'muted': '🔇',
                'silent': '🔈',
                'low': '🔉',
                'medium': '🔊',
                'high': '📢'
            }.get(status, '❓')
            
            volume_text = f"{volume}" if volume >= 0 else "获取失败"
            if percentage is not None:
                volume_text += f" ({percentage}%)"
            
            mute_text = " [静音]" if muted else ""
            
            print(f"  {status_icon} {type_names[vol_type]:8}: {volume_text}{mute_text}")
        
        print("\n" + "=" * 60)


def main():
    """主函数示例"""
    
    # 创建音量管理器
    volume_manager = VolumeManager()
    
    # 打印详细报告
    volume_manager.print_volume_report()
    
    # 简单使用示例
    print("\n📱 简单使用示例:")
    print("-" * 30)
    
    # 获取媒体音量
    media_volume = volume_manager.get_current_media_volume()
    print(f"当前媒体音量: {media_volume}")
    
    # 获取铃声音量
    ring_volume = volume_manager.get_current_ring_volume()
    print(f"当前铃声音量: {ring_volume}")
    
    # 获取所有音量
    all_volumes = volume_manager.get_all_volumes()
    print(f"所有音量: {all_volumes}")
    
    # 检查音量状态
    status = volume_manager.check_volume_status()
    print(f"媒体播放就绪: {status['media_ready']}")
    print(f"铃声可听见: {status['ring_audible']}")


if __name__ == "__main__":
    main()
