#!/usr/bin/env python3
"""
Excel驱动的Ella测试用例批量生成工具
读取Excel文件，批量生成测试脚本
"""
import os
import sys
import re
import pandas as pd
from pathlib import Path
from typing import List, Tuple, Dict, Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from core.logger import log
from tools.ella_test_generator_v2 import EllaTestGenerator, generate_with_custom_response


class ExcelTestGenerator:
    """Excel驱动的测试用例生成器"""
    
    def __init__(self, excel_path: str = None):
        """
        初始化Excel测试生成器
        
        Args:
            excel_path: Excel文件路径，默认为当前目录下的case_sorry_oops.xlsx
        """
        self.project_root = project_root
        if excel_path is None:
            self.excel_path = self.project_root / "tools" / "case_sorry_oops.xlsx"
        else:
            self.excel_path = Path(excel_path)
        
        self.ella_generator = EllaTestGenerator()
        self.df = None
        
    def load_excel(self) -> bool:
        """
        加载Excel文件
        
        Returns:
            bool: 是否加载成功
        """
        try:
            if not self.excel_path.exists():
                log.error(f"Excel文件不存在: {self.excel_path}")
                return False
                
            self.df = pd.read_excel(self.excel_path)
            log.info(f"✅ 成功加载Excel文件: {self.excel_path}")
            log.info(f"📊 数据行数: {len(self.df)}")
            log.info(f"📋 列名: {self.df.columns.tolist()}")
            return True
            
        except Exception as e:
            log.error(f"❌ 加载Excel文件失败: {e}")
            return False
    
    def extract_expected_response(self, response_text: str) -> List[str]:
        """
        从expected_response文本中提取关键词
        
        Args:
            response_text: 原始响应文本，如"1、响应断言：响应文本包含【Sorry】"
            
        Returns:
            List[str]: 提取的关键词列表
        """
        if pd.isna(response_text) or not response_text:
            return ["Sorry"]  # 默认值
        
        # 转换为字符串
        text = str(response_text)
        
        # 提取【】中的内容
        pattern = r'【([^】]+)】'
        matches = re.findall(pattern, text)
        
        if matches:
            return matches
        
        # 如果没有找到【】格式，尝试其他模式
        # 查找包含关键词的模式
        keywords = ["Sorry", "Done", "Oops", "好的", "抱歉", "完成"]
        found_keywords = []
        
        text_lower = text.lower()
        for keyword in keywords:
            if keyword.lower() in text_lower:
                found_keywords.append(keyword)
        
        if found_keywords:
            return found_keywords
        
        # 默认返回Sorry（因为文件名包含sorry_oops）
        return ["Sorry"]
    
    def map_output_dir_name(self, dir_name: str) -> str:
        """
        映射中文目录名到英文目录名
        
        Args:
            dir_name: 中文目录名
            
        Returns:
            str: 英文目录名
        """
        if pd.isna(dir_name) or not dir_name:
            return "unsupported_commands"
        
        dir_mapping = {
            "系统耦合": "system_coupling",
            "三方耦合": "third_coupling", 
            "模块耦合": "component_coupling",
            "dialogue": "dialogue",
            "聊天": "dialogue",
            "不支持指令": "unsupported_commands"
        }
        
        return dir_mapping.get(str(dir_name), "unsupported_commands")
    
    def process_single_record(self, row: pd.Series) -> Optional[Tuple[str, List[str], str]]:
        """
        处理单条记录
        
        Args:
            row: DataFrame行数据
            
        Returns:
            Optional[Tuple[str, List[str], str]]: (command, expected_response, output_dir) 或 None
        """
        try:
            # 提取command（使用query列）
            command = row.get('command', '')
            if pd.isna(command) or not command:
                log.warning(f"跳过空命令行: {row.name}")
                return None
            
            command = str(command).strip()
            
            # 提取expected_response
            response_text = row.get('expected_response', '')
            expected_response = self.extract_expected_response(response_text)
            
            # 提取output_dir_name
            dir_name = row.get('output_dir_name', '')
            output_dir = self.map_output_dir_name(dir_name)
            
            log.info(f"📝 处理记录: {command} -> {expected_response} -> {output_dir}")
            
            return command, expected_response, output_dir
            
        except Exception as e:
            log.error(f"❌ 处理记录失败 (行{row.name}): {e}")
            return None
    
    def generate_batch_tests(self, start_row: int = 0, end_row: int = None, 
                           dry_run: bool = False) -> List[str]:
        """
        批量生成测试用例
        
        Args:
            start_row: 开始行号（包含）
            end_row: 结束行号（不包含），None表示到最后
            dry_run: 是否为试运行（只显示将要生成的内容，不实际生成文件）
            
        Returns:
            List[str]: 生成的文件路径列表
        """
        if self.df is None:
            if not self.load_excel():
                return []
        
        # 确定处理范围
        if end_row is None:
            end_row = len(self.df)
        
        end_row = min(end_row, len(self.df))
        start_row = max(0, start_row)
        
        log.info(f"🚀 开始批量生成测试用例 (行 {start_row} 到 {end_row-1})")
        if dry_run:
            log.info("🔍 试运行模式 - 不会实际生成文件")
        
        generated_files = []
        success_count = 0
        skip_count = 0
        error_count = 0
        
        for idx in range(start_row, end_row):
            try:
                row = self.df.iloc[idx]
                
                # 处理单条记录
                result = self.process_single_record(row)
                if result is None:
                    skip_count += 1
                    continue
                
                command, expected_response, output_dir = result
                
                if dry_run:
                    log.info(f"[试运行] 第{idx+1}行: {command} -> {expected_response} -> {output_dir}")
                    generated_files.append(f"[DRY_RUN] {command}")
                else:
                    # 实际生成测试用例
                    file_path = generate_with_custom_response(
                        command=command,
                        expected_response=expected_response,
                        output_dir_name=output_dir
                    )
                    generated_files.append(file_path)
                    log.info(f"✅ 第{idx+1}行生成成功: {file_path}")
                
                success_count += 1
                
            except Exception as e:
                error_count += 1
                log.error(f"❌ 第{idx+1}行生成失败: {e}")
        
        # 输出统计信息
        log.info(f"📊 批量生成完成:")
        log.info(f"   ✅ 成功: {success_count}")
        log.info(f"   ⏭️  跳过: {skip_count}")
        log.info(f"   ❌ 失败: {error_count}")
        log.info(f"   📁 总计: {len(generated_files)} 个文件")
        
        return generated_files
    
    def preview_data(self, num_rows: int = 10) -> None:
        """
        预览Excel数据
        
        Args:
            num_rows: 预览行数
        """
        if self.df is None:
            if not self.load_excel():
                return
        
        log.info(f"📋 Excel数据预览 (前{num_rows}行):")
        print("=" * 80)
        
        for idx in range(min(num_rows, len(self.df))):
            row = self.df.iloc[idx]
            result = self.process_single_record(row)
            
            if result:
                command, expected_response, output_dir = result
                print(f"第{idx+1}行:")
                print(f"  命令: {command}")
                print(f"  期望响应: {expected_response}")
                print(f"  输出目录: {output_dir}")
                print("-" * 40)
            else:
                print(f"第{idx+1}行: [跳过 - 数据无效]")
                print("-" * 40)


def main():
    """主函数"""
    print("🚀 Excel驱动的Ella测试用例批量生成工具")
    print("=" * 60)
    
    # 创建生成器
    generator = ExcelTestGenerator()
    
    # 预览数据
    print("\n📋 数据预览:")
    generator.preview_data(5)
    
    # 询问用户操作
    print("\n请选择操作:")
    print("1. 试运行 (预览将要生成的测试用例)")
    print("2. 生成前10个测试用例")
    print("3. 生成所有测试用例")
    print("4. 自定义范围生成")
    print("5. 退出")
    
    choice = input("请选择 (1-5): ").strip()
    
    if choice == "1":
        print("\n🔍 试运行模式:")
        generator.generate_batch_tests(dry_run=True)
    elif choice == "2":
        print("\n🚀 生成前10个测试用例:")
        files = generator.generate_batch_tests(end_row=10)
        print(f"✅ 生成完成，共{len(files)}个文件")
    elif choice == "3":
        print("\n🚀 生成所有测试用例:")
        files = generator.generate_batch_tests()
        print(f"✅ 生成完成，共{len(files)}个文件")
    elif choice == "4":
        try:
            start = int(input("请输入开始行号 (从0开始): "))
            end = input("请输入结束行号 (留空表示到最后): ").strip()
            end = int(end) if end else None
            
            print(f"\n🚀 生成测试用例 (行 {start} 到 {end or '最后'}):")
            files = generator.generate_batch_tests(start_row=start, end_row=end)
            print(f"✅ 生成完成，共{len(files)}个文件")
        except ValueError:
            print("❌ 输入的行号格式不正确")
    elif choice == "5":
        print("👋 再见!")
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    # main()
    generator = ExcelTestGenerator(excel_path=r'D:\aigc\app_test\tools\case_dialogue.xlsx')
    # generator.generate_batch_tests(end_row=1)
    generator.generate_batch_tests()
