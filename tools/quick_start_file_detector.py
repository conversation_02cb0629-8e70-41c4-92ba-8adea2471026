"""
文件检测模块快速使用指南
演示最常用的文件检测功能
"""
import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    from core.logger import log
    from tools.file_detector import FileDetector
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)


def quick_demo():
    """快速演示文件检测功能"""
    print("🔍 文件检测模块快速演示")
    print("=" * 50)
    
    # 1. 创建检测器
    print("\n1️⃣ 创建文件检测器")
    detector = FileDetector()
    print("✅ 文件检测器创建成功")
    
    # 2. 检查当前项目的关键文件
    print("\n2️⃣ 检查项目关键文件")
    key_files = [
        "config/config.yaml",
        "core/logger.py", 
        "tools/file_detector.py",
        "README.md"
    ]
    
    for file_path in key_files:
        exists = detector.check_file_exists(file_path)
        status = "✅ 存在" if exists else "❌ 不存在"
        print(f"   {file_path}: {status}")
    
    # 3. 扫描tools目录
    print("\n3️⃣ 扫描tools目录")
    files = detector.scan_directory("tools", recursive=False)
    print(f"   找到 {len(files)} 个文件")
    
    # 显示图片文件
    image_files = [f for f in files if f.get('is_image', False)]
    if image_files:
        print(f"   其中包含 {len(image_files)} 个图片文件:")
        for img in image_files[:3]:
            print(f"     - {img['name']}")
    else:
        print("   未找到图片文件")
    
    # 4. 演示日期时间戳检测
    print("\n4️⃣ 日期时间戳文件检测演示")
    today = datetime.now().strftime('%Y%m%d')
    print(f"   目标日期: {today}")
    
    # 检查output目录
    results = detector.check_camera_images("tools/output", today)
    print(f"   检查目录: {results['directory']}")
    print(f"   目录存在: {'✅' if results['directory_exists'] else '❌'}")
    print(f"   总文件数: {results['total_files']}")
    print(f"   匹配文件数: {len(results['matched_files'])}")
    
    # 5. 支持的文件模式
    print("\n5️⃣ 支持的文件名模式")
    patterns = FileDetector.DATETIME_PATTERNS
    for i, (name, regex) in enumerate(patterns.items(), 1):
        print(f"   {i}. {name}")
        
        # 生成示例文件名
        now = datetime.now()
        if name == 'IMG_YYYYMMDD_HHMMSS_SSS':
            example = f"IMG_{now.strftime('%Y%m%d')}_{now.strftime('%H%M%S')}_970.jpg"
        elif name == 'YYYYMMDD_HHMMSS':
            example = f"{now.strftime('%Y%m%d')}_{now.strftime('%H%M%S')}.jpg"
        elif name == 'YYYY-MM-DD_HH-MM-SS':
            example = f"{now.strftime('%Y-%m-%d')}_{now.strftime('%H-%M-%S')}.jpg"
        elif name == 'Screenshot_YYYYMMDD-HHMMSS':
            example = f"Screenshot_{now.strftime('%Y%m%d')}-{now.strftime('%H%M%S')}.png"
        
        print(f"      示例: {example}")
    
    print("\n" + "=" * 50)
    print("✅ 快速演示完成！")


def usage_examples():
    """使用示例"""
    print("\n📖 常用命令示例")
    print("=" * 50)

    print("\n🖥️ 本地文件系统模式:")
    local_examples = [
        {
            "title": "检查默认相机目录下今天的照片",
            "command": "python tools/file_detector.py",
            "description": "使用默认设置检查 \\DCIM\\Camera 目录"
        },
        {
            "title": "检查指定目录和日期",
            "command": 'python tools/file_detector.py -d "DCIM/Camera" -t "20250716"',
            "description": "检查指定目录下特定日期的文件"
        },
        {
            "title": "使用截图模式检测",
            "command": 'python tools/file_detector.py -d "Screenshots" -p "Screenshot_YYYYMMDD-HHMMSS"',
            "description": "检测截图文件"
        }
    ]

    for i, example in enumerate(local_examples, 1):
        print(f"\n{i}️⃣ {example['title']}")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['description']}")

    print("\n📱 Android设备模式:")
    android_examples = [
        {
            "title": "列出连接的Android设备",
            "command": "python tools/file_detector.py --list-devices",
            "description": "查看当前连接的所有Android设备"
        },
        {
            "title": "检查Android相机目录",
            "command": "python tools/file_detector.py --android",
            "description": "检查Android设备默认相机目录下今天的照片"
        },
        {
            "title": "显示Android常见目录",
            "command": "python tools/file_detector.py --android --common-dirs",
            "description": "显示Android设备上的常见目录路径"
        },
        {
            "title": "检查Android截图目录",
            "command": 'python tools/file_detector.py --android -d "/sdcard/Pictures/Screenshots" -p "Screenshot_YYYYMMDD-HHMMSS"',
            "description": "检查Android设备截图目录"
        },
        {
            "title": "检查特定Android设备",
            "command": 'python tools/file_detector.py --android --device-id "YOUR_DEVICE_ID" -t "20250716"',
            "description": "检查指定Android设备的文件"
        }
    ]

    for i, example in enumerate(android_examples, 1):
        print(f"\n{i}️⃣ {example['title']}")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['description']}")


def programmatic_usage():
    """编程使用示例"""
    print("\n💻 编程使用示例")
    print("=" * 50)

    print("\n🖥️ 本地文件系统:")
    local_examples = [
        {
            "title": "基础文件检查",
            "code": '''from tools.file_detector import FileDetector

detector = FileDetector()
exists = detector.check_file_exists("path/to/file.jpg")
print(f"文件存在: {exists}")'''
        },
        {
            "title": "检查今天的相机照片",
            "code": '''from tools.file_detector import FileDetector
from datetime import datetime

detector = FileDetector()
today = datetime.now().strftime('%Y%m%d')
results = detector.check_camera_images("DCIM/Camera", today)
print(f"今天拍摄了 {len(results['matched_files'])} 张照片")'''
        }
    ]

    for i, example in enumerate(local_examples, 1):
        print(f"\n{i}️⃣ {example['title']}")
        print("```python")
        print(example['code'])
        print("```")

    print("\n📱 Android设备:")
    android_examples = [
        {
            "title": "检查Android设备文件",
            "code": '''from tools.file_detector import FileDetector

# 创建Android模式检测器
detector = FileDetector(use_adb=True)
exists = detector.check_file_exists("/sdcard/DCIM/Camera/IMG_20250716_120000_001.jpg")
print(f"Android文件存在: {exists}")'''
        },
        {
            "title": "检查Android相机照片",
            "code": '''from tools.file_detector import FileDetector
from datetime import datetime

# 创建Android模式检测器
detector = FileDetector(use_adb=True)
today = datetime.now().strftime('%Y%m%d')
results = detector.check_camera_images(target_date=today)
print(f"Android设备今天拍摄了 {len(results['matched_files'])} 张照片")'''
        },
        {
            "title": "获取Android常见目录",
            "code": '''from tools.file_detector import FileDetector

detector = FileDetector(use_adb=True)
common_dirs = detector.get_android_common_directories()
for category, paths in common_dirs.items():
    print(f"{category}: {paths}")'''
        },
        {
            "title": "指定Android设备",
            "code": '''from tools.file_detector import FileDetector

# 指定特定的Android设备
detector = FileDetector(use_adb=True, device_id="YOUR_DEVICE_ID")
results = detector.check_camera_images()
print(f"设备 {detector.device_id} 的照片数量: {len(results['matched_files'])}")'''
        }
    ]

    for i, example in enumerate(android_examples, 1):
        print(f"\n{i}️⃣ {example['title']}")
        print("```python")
        print(example['code'])
        print("```")


def main():
    """主函数"""
    try:
        quick_demo()
        usage_examples()
        programmatic_usage()
        
        print("\n" + "=" * 50)
        print("📚 更多信息请查看:")
        print("   - 详细文档: tools/README_file_detector.md")
        print("   - 完整示例: tools/examples/file_detector_examples.py")
        print("   - 源代码: tools/file_detector.py")
        
    except Exception as e:
        log.error(f"运行快速指南时发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
