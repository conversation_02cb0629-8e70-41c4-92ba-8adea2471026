"""
屏幕管理工具
用于控制屏幕超时、保持屏幕常亮等功能
"""
import subprocess
import time
from typing import Optional, Dict, Tuple
from core.logger import log


class ScreenManager:
    """屏幕管理器"""

    def __init__(self):
        """初始化屏幕管理器"""
        self._default_timeout = 5
        self._original_screen_timeout = None
        self._original_stay_on_setting = None

    def _run_adb_command(self, command: list, timeout: int = None) -> Tuple[bool, str, str]:
        """
        执行ADB命令

        Args:
            command: ADB命令列表
            timeout: 超时时间

        Returns:
            Tuple[bool, str, str]: (是否成功, stdout, stderr)
        """
        try:
            timeout = timeout or self._default_timeout
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding='utf-8',
                errors='ignore'
            )

            success = result.returncode == 0
            return success, result.stdout.strip(), result.stderr.strip()

        except subprocess.TimeoutExpired:
            log.warning(f"ADB命令超时: {' '.join(command)}")
            return False, "", "Timeout"
        except Exception as e:
            log.warning(f"ADB命令执行失败: {e}")
            return False, "", str(e)

    def get_current_screen_timeout(self) -> Optional[int]:
        """
        获取当前屏幕超时时间

        Returns:
            Optional[int]: 屏幕超时时间(毫秒)，None表示获取失败
        """
        try:
            log.info("获取当前屏幕超时时间")

            command = ["adb", "shell", "settings", "get", "system", "screen_off_timeout"]
            success, stdout, stderr = self._run_adb_command(command)

            if success and stdout and stdout != "null":
                timeout_ms = int(stdout)
                timeout_seconds = timeout_ms / 1000
                log.info(f"当前屏幕超时时间: {timeout_ms}ms ({timeout_seconds}秒)")
                return timeout_ms
            else:
                log.warning(f"获取屏幕超时时间失败: {stderr}")
                return None

        except ValueError:
            log.warning(f"无法解析屏幕超时时间: {stdout}")
            return None
        except Exception as e:
            log.error(f"获取屏幕超时时间异常: {e}")
            return None

    def set_screen_timeout(self, timeout_ms: int) -> bool:
        """
        设置屏幕超时时间

        Args:
            timeout_ms: 超时时间(毫秒)
                       常用值: 15000(15秒), 30000(30秒), 60000(1分钟), 
                              300000(5分钟), 600000(10分钟), 1800000(30分钟),
                              2147483647(永不超时)

        Returns:
            bool: 是否设置成功
        """
        try:
            log.info(f"设置屏幕超时时间为: {timeout_ms}ms ({timeout_ms/1000}秒)")

            # 保存原始设置
            if self._original_screen_timeout is None:
                self._original_screen_timeout = self.get_current_screen_timeout()

            command = ["adb", "shell", "settings", "put", "system", "screen_off_timeout", str(timeout_ms)]
            success, stdout, stderr = self._run_adb_command(command)

            if success:
                # 验证设置是否生效
                time.sleep(1)
                current_timeout = self.get_current_screen_timeout()
                if current_timeout == timeout_ms:
                    log.info(f"屏幕超时时间设置成功: {timeout_ms}ms")
                    return True
                else:
                    log.warning(f"屏幕超时时间设置可能未生效，当前值: {current_timeout}ms")
                    return False
            else:
                log.error(f"设置屏幕超时时间失败: {stderr}")
                return False

        except Exception as e:
            log.error(f"设置屏幕超时时间异常: {e}")
            return False

    def set_screen_never_timeout(self) -> bool:
        """
        设置屏幕永不超时

        Returns:
            bool: 是否设置成功
        """
        log.info("设置屏幕永不超时")
        return self.set_screen_timeout(2147483647)  # 最大值，表示永不超时

    def set_screen_timeout_minutes(self, minutes: int) -> bool:
        """
        设置屏幕超时时间(分钟)

        Args:
            minutes: 超时时间(分钟)

        Returns:
            bool: 是否设置成功
        """
        timeout_ms = minutes * 60 * 1000
        return self.set_screen_timeout(timeout_ms)

    def get_stay_on_while_plugged_setting(self) -> Optional[int]:
        """
        获取充电时保持屏幕常亮设置

        Returns:
            Optional[int]: 设置值，None表示获取失败
                          0: 关闭
                          1: AC充电时保持常亮
                          2: USB充电时保持常亮
                          3: AC和USB充电时都保持常亮
                          4: 无线充电时保持常亮
                          7: 所有充电方式都保持常亮
        """
        try:
            log.info("获取充电时保持屏幕常亮设置")

            command = ["adb", "shell", "settings", "get", "global", "stay_on_while_plugged_in"]
            success, stdout, stderr = self._run_adb_command(command)

            if success and stdout and stdout != "null":
                setting_value = int(stdout)
                setting_desc = {
                    0: "关闭",
                    1: "AC充电时保持常亮",
                    2: "USB充电时保持常亮", 
                    3: "AC和USB充电时都保持常亮",
                    4: "无线充电时保持常亮",
                    7: "所有充电方式都保持常亮"
                }.get(setting_value, f"未知设置({setting_value})")
                
                log.info(f"充电时保持屏幕常亮设置: {setting_desc} (值: {setting_value})")
                return setting_value
            else:
                log.warning(f"获取充电时保持屏幕常亮设置失败: {stderr}")
                return None

        except ValueError:
            log.warning(f"无法解析充电时保持屏幕常亮设置: {stdout}")
            return None
        except Exception as e:
            log.error(f"获取充电时保持屏幕常亮设置异常: {e}")
            return None

    def set_stay_on_while_plugged(self, setting_value: int) -> bool:
        """
        设置充电时保持屏幕常亮

        Args:
            setting_value: 设置值
                          0: 关闭
                          1: AC充电时保持常亮
                          2: USB充电时保持常亮
                          3: AC和USB充电时都保持常亮
                          4: 无线充电时保持常亮
                          7: 所有充电方式都保持常亮

        Returns:
            bool: 是否设置成功
        """
        try:
            setting_desc = {
                0: "关闭",
                1: "AC充电时保持常亮",
                2: "USB充电时保持常亮",
                3: "AC和USB充电时都保持常亮", 
                4: "无线充电时保持常亮",
                7: "所有充电方式都保持常亮"
            }.get(setting_value, f"未知设置({setting_value})")

            log.info(f"设置充电时保持屏幕常亮: {setting_desc}")

            # 保存原始设置
            if self._original_stay_on_setting is None:
                self._original_stay_on_setting = self.get_stay_on_while_plugged_setting()

            command = ["adb", "shell", "settings", "put", "global", "stay_on_while_plugged_in", str(setting_value)]
            success, stdout, stderr = self._run_adb_command(command)

            if success:
                # 验证设置是否生效
                time.sleep(1)
                current_setting = self.get_stay_on_while_plugged_setting()
                if current_setting == setting_value:
                    log.info(f"充电时保持屏幕常亮设置成功: {setting_desc}")
                    return True
                else:
                    log.warning(f"充电时保持屏幕常亮设置可能未生效，当前值: {current_setting}")
                    return False
            else:
                log.error(f"设置充电时保持屏幕常亮失败: {stderr}")
                return False

        except Exception as e:
            log.error(f"设置充电时保持屏幕常亮异常: {e}")
            return False

    def enable_stay_on_while_plugged(self) -> bool:
        """
        启用充电时保持屏幕常亮(所有充电方式)

        Returns:
            bool: 是否设置成功
        """
        log.info("启用充电时保持屏幕常亮(所有充电方式)")
        return self.set_stay_on_while_plugged(7)

    def disable_stay_on_while_plugged(self) -> bool:
        """
        禁用充电时保持屏幕常亮

        Returns:
            bool: 是否设置成功
        """
        log.info("禁用充电时保持屏幕常亮")
        return self.set_stay_on_while_plugged(0)

    def setup_for_testing(self, timeout_minutes: int = 30) -> bool:
        """
        为测试设置屏幕参数(延长超时时间并启用充电时常亮)

        Args:
            timeout_minutes: 屏幕超时时间(分钟)，默认30分钟

        Returns:
            bool: 是否设置成功
        """
        try:
            log.info(f"为测试设置屏幕参数: 超时时间{timeout_minutes}分钟，启用充电时常亮")

            # 设置较长的屏幕超时时间
            timeout_success = self.set_screen_timeout_minutes(timeout_minutes)
            
            # 启用充电时保持屏幕常亮
            stay_on_success = self.enable_stay_on_while_plugged()

            success = timeout_success and stay_on_success
            if success:
                log.info("测试屏幕参数设置完成")
            else:
                log.warning("测试屏幕参数设置部分失败")
            
            return success

        except Exception as e:
            log.error(f"设置测试屏幕参数异常: {e}")
            return False

    def restore_original_settings(self) -> bool:
        """
        恢复原始屏幕设置

        Returns:
            bool: 是否恢复成功
        """
        try:
            log.info("恢复原始屏幕设置")
            
            success = True

            # 恢复屏幕超时时间
            if self._original_screen_timeout is not None:
                timeout_success = self.set_screen_timeout(self._original_screen_timeout)
                if timeout_success:
                    log.info(f"已恢复原始屏幕超时时间: {self._original_screen_timeout}ms")
                    self._original_screen_timeout = None
                else:
                    log.warning("恢复原始屏幕超时时间失败")
                    success = False

            # 恢复充电时保持常亮设置
            if self._original_stay_on_setting is not None:
                stay_on_success = self.set_stay_on_while_plugged(self._original_stay_on_setting)
                if stay_on_success:
                    log.info(f"已恢复原始充电时保持常亮设置: {self._original_stay_on_setting}")
                    self._original_stay_on_setting = None
                else:
                    log.warning("恢复原始充电时保持常亮设置失败")
                    success = False

            if success:
                log.info("原始屏幕设置恢复完成")
            else:
                log.warning("原始屏幕设置恢复部分失败")

            return success

        except Exception as e:
            log.error(f"恢复原始屏幕设置异常: {e}")
            return False

    def get_screen_status_info(self) -> Dict:
        """
        获取屏幕状态详细信息

        Returns:
            Dict: 屏幕状态信息
        """
        try:
            log.info("获取屏幕状态详细信息")

            status_info = {
                'screen_timeout_ms': None,
                'screen_timeout_seconds': None,
                'screen_timeout_description': '未知',
                'stay_on_while_plugged': None,
                'stay_on_description': '未知',
                'is_screen_on': None,
                'power_state': '未知'
            }

            # 获取屏幕超时时间
            timeout_ms = self.get_current_screen_timeout()
            if timeout_ms is not None:
                status_info['screen_timeout_ms'] = timeout_ms
                status_info['screen_timeout_seconds'] = timeout_ms / 1000
                
                if timeout_ms >= 2147483647:
                    status_info['screen_timeout_description'] = '永不超时'
                elif timeout_ms >= 1800000:
                    status_info['screen_timeout_description'] = f'{timeout_ms/60000:.0f}分钟'
                elif timeout_ms >= 60000:
                    status_info['screen_timeout_description'] = f'{timeout_ms/60000:.1f}分钟'
                else:
                    status_info['screen_timeout_description'] = f'{timeout_ms/1000:.0f}秒'

            # 获取充电时保持常亮设置
            stay_on_setting = self.get_stay_on_while_plugged_setting()
            if stay_on_setting is not None:
                status_info['stay_on_while_plugged'] = stay_on_setting
                status_info['stay_on_description'] = {
                    0: "关闭",
                    1: "AC充电时保持常亮",
                    2: "USB充电时保持常亮",
                    3: "AC和USB充电时都保持常亮",
                    4: "无线充电时保持常亮", 
                    7: "所有充电方式都保持常亮"
                }.get(stay_on_setting, f"未知设置({stay_on_setting})")

            # 检查屏幕是否点亮
            try:
                command = ["adb", "shell", "dumpsys", "power"]
                success, stdout, stderr = self._run_adb_command(command, timeout=10)
                
                if success and stdout:
                    if "mScreenOn=true" in stdout or "Display Power: state=ON" in stdout:
                        status_info['is_screen_on'] = True
                        status_info['power_state'] = '屏幕开启'
                    elif "mScreenOn=false" in stdout or "Display Power: state=OFF" in stdout:
                        status_info['is_screen_on'] = False
                        status_info['power_state'] = '屏幕关闭'
                    else:
                        status_info['power_state'] = '无法确定'
            except:
                pass

            return status_info

        except Exception as e:
            log.error(f"获取屏幕状态详细信息异常: {e}")
            return {
                'screen_timeout_ms': None,
                'screen_timeout_seconds': None,
                'screen_timeout_description': '错误',
                'stay_on_while_plugged': None,
                'stay_on_description': '错误',
                'is_screen_on': None,
                'power_state': '错误'
            }

if __name__ == '__main__':
    screen_manager = ScreenManager()
    print(screen_manager.get_screen_status_info())
